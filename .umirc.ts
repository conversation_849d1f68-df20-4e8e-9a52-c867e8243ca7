import { defineConfig } from '@umijs/max';
import { routeList } from './src/router';
export default defineConfig({
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  antd: {},
  cssLoaderModules: {
    // 配置驼峰式使用
    exportLocalsConvention: 'camelCase',
  },
  plugins: ['@umijs/max-plugin-openapi', 'umi-plugin-keep-alive'],
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'SCM供应链',
  },
  mfsu: false,
  title: 'SCM供应链',
  extraPostCSSPlugins: [
    require('tailwindcss')({ config: './tailwind.config.js' }),
  ],
  hash: true,
  theme: {
    '@color_fff': '#FFFFFF',
    '@color_header': '#2069D1',
    '@color_tab': '#F4F7FA',
    '@color_main': '#F4F7FA',
    '@color_home': '#EFF2F7',
    '@color_link': '#3D66FE',
    '@color_disabled': '#F2F3F5',

    '@page_bg': '#F3F4F7',

    // 主色
    '@color_theme': '#3D66FE',
    '@color_theme_bg': '#E8F1FF',

    // 按钮，辅助图形，图标
    '@color_init': '#1D2129',
    '@color_default': '#3D66FE',
    '@color_danger': '#FF0000',
    '@color_invalid': '#86909C',
    '@color_warning': '#FF7D01',
    '@color_success': '#00B42B',
    '@color_purple': '#CC66CC',
    // 边框、分栏
    '@color_line1': '#393D49',
    '@color_line2': '#E5E6EA',
    '@color_line3': '#F5F7FA',
    '@color_line4': '#DCDFE6',
    '@color_line5': '#ECEDF0',
    // 文字
    color_black1: '#000',
    color_black2: '#333',
    color_black3: '#666',
    color_black4: '#999',

    '@size_5': '5px',
    '@size_10': '10px',
    '@size_12': '12px',
    '@size_14': '14px',
    '@size_18': '18px',
    '@size_20': '20px',
  },
  routes: routeList,
  npmClient: 'npm',
  // 开启后路由会自动添加前缀，如：/xlb_tms
  // qiankun: {
  //   slave: {},
  // },
  // proxy 代理配置
  proxy: {
    '/scm': {
      target: 'https://react-web.react-web.ali-test.xlbsoft.com/',
      changeOrigin: true,
      // pathRewrite: { '^/erp': '' },
      onProxyReq: (proxyReq) => {
        proxyReq.setHeader('origin', 'http://localhost:7777');
      },
    },
    '/erp': {
      target: 'https://react-web.react-web.ali-test.xlbsoft.com/',
      changeOrigin: true,
      // pathRewrite: { '^/erp': '' },
      onProxyReq: (proxyReq) => {
        proxyReq.setHeader('origin', 'http://localhost:7777');
      },
    },
    '/scm-mdm': {
      target: 'https://scm-mdm.mps.ali-test.xlbsoft.com/',
      changeOrigin: true,
      // pathRewrite: { '^/erp': '' },
      onProxyReq: (proxyReq) => {
        proxyReq.setHeader('origin', 'http://localhost:7777');
      },
    },
    '/center': {
      target: 'https://react-web.react-web.ali-test.xlbsoft.com/',
      changeOrigin: true,
      // pathRewrite: { '^/erp': '' },
      onProxyReq: (proxyReq) => {
        proxyReq.setHeader('origin', 'http://localhost:7777');
      },
    },
    // '/erp': {
    //   target: 'https://test-api.xlbsoft.com/',
    //   changeOrigin: true,
    //   // pathRewrite: { '^/erp': '' },
    //   onProxyReq: (proxyReq) => {
    //     proxyReq.setHeader('origin', 'http://localhost:8000/');
    //   },
    // },
    // '/tms': {
    //   target: 'https://test-api.xlbsoft.com/', // 测试环境
    //   // target: 'http://**************:55126/', // 赵本权本地
    //   changeOrigin: true,
    //   onProxyReq: (proxyReq) => {
    //     proxyReq.setHeader('origin', 'http://localhost:8000/');
    //   },
    // },
    // '/export': {
    //   target: 'https://test-api.xlbsoft.com/', // 测试环境
    //   // target: 'http://**************:55126/', // 赵本权本地
    //   changeOrigin: true,
    //   onProxyReq: (proxyReq) => {
    //     proxyReq.setHeader('origin', 'http://localhost:8000/');
    //   },
    // },
    // '/api': {
    //   target: 'https://test-api.xlbsoft.com/', // 测试环境
    //   // target: 'http://**************:55126/', // 赵本权本地
    //   changeOrigin: true,
    //   pathRewrite: { '^/api': '' },
    //   onProxyReq: (proxyReq) => {
    //     proxyReq.setHeader('origin', 'http://localhost:8000/');
    //   },
    // },
  },
  /**
   * @name openAPI 插件的配置
   * @description 基于 openapi 的规范生成serve 和mock，能减少很多样板代码
   * @doc https://pro.ant.design/zh-cn/docs/openapi/
   */
  openAPI: [
    {
      requestLibPath: "import { XlbFetch as request } from '@xlb/utils'",
      // apiPrefix: "'/api'",
      // 或者使用在线的版本
      schemaPath: 'https://test-api.xlbsoft.com/tms/v3/api-docs?group=tms',
      // schemaPath: join(__dirname, 'oneapi.json'),
      // requestImportStatement: 'xlb',
      mock: false,
      hook: {
        // @ts-ignore
        customFunctionName: (data) => {
          return data.operationId.split('_')[0];
        },
      },
    },
  ],
});
