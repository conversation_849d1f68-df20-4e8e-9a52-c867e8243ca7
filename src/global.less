@import 'tailwindcss/base';

// @import 'tailwindcss/components';

@import 'tailwindcss/utilities';
@import '@/assets/style/margin';

// 通用样式
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: auto;
  font-weight: 400;
  font-size: 13px;
  font-family: 'NUM', 'PingFang SC', helvetica, arial, verdana, sans-serif;
  background-color: @color_fff;
  border-radius: 4px;
}

// flex布局样式
.row-flex {
  display: flex;
  flex-direction: row;
}
.v-flex {
  display: flex;
  align-items: center;
}

.link {
  color: #3d66fe;
  text-decoration: underline;
  cursor: pointer;
}

.info {
  color: #1d2129;
}

.default {
  color: #3d66fe;
}

.warning {
  color: #ff7d01;
}

.success {
  color: #00b42b;
}

.danger {
  color: #ff0000;
}

.invalid {
  color: #86909c;
}

.purple {
  color: #cc66cc;
}

.xlb-field-item-after {
  z-index: 1;
}
