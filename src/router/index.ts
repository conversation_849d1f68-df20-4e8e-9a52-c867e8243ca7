interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/',
    redirect: '/xlb_scm/scmBoard',
  },
  {
    path: '/scmBoard',
    redirect: '/xlb_scm/scmBoard',
  },
  {
    path: '*',
    component: '@/pages/404/index',
    name: '404',
  },
  {
    path: '/chart',
    component: '@/pages/testRoute/chart',
    name: '图表',
  },
  {
    path: '/setting',
    component: '@/pages/testRoute/setting',
    name: '设置',
  },
  {
    path: '/xlb_scm/storeOrders/index',
    component: '@/pages/storeOrders/index',
    title: '门店订单',
    tabClass: 'storeOrders',
    subTitle: '业务管理',
    subMenu: 'businessManage',
  },
  {
    path: '/xlb_scm/newProductDetails/index',
    component: '@/pages/newProductDetails/index',
    title: '新品明细',
    tabClass: 'newProductDetails',
    subTitle: '业务管理',
    subMenu: 'businessManage',
  },
  {
    path: '/xlb_scm/newProductSign/index',
    component: '@/pages/newProductSign/index',
    title: '新品申请',
    tabClass: 'newProductSign',
    subTitle: '业务管理',
    subMenu: 'businessManage',
  },
  {
    path: '/xlb_scm/PurchaseReturn/index',
    component: '@/pages/PurchaseReturn/index',
    title: '采购退货',
    tabClass: 'PurchaseReturn',
    subTitle: '业务管理',
    subMenu: 'businessManage',
  },
  {
    path: '/xlb_scm/blindPoll/index',
    component: '@/pages/blindPoll/index',
    title: '盲测投票',
    subTitle: '业务管理',
    subMenu: 'businessManage',
  },
  {
    path: '/xlb_scm/takeShelves/index',
    component: '@/pages/takeShelves/index',
    title: '商品下架',
    subTitle: '业务管理',
    subMenu: 'businessManage',
  },
  {
    path: '/xlb_scm/storeCredentialsManage/index',
    component: '@/pages/storeCredentialsManage/index',
    title: '供应商证件',
    tabClass: 'newProductDetails',
    subTitle: '档案管理',
    subMenu: 'basicConfig',
  },
  {
    path: '/xlb_scm/surveyReportManage/index',
    component: '@/pages/surveyReportManage/index',
    title: '外检报告',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'surveyReportManage',
  },
  {
    path: '/xlb_scm/supplierMonthlyBill/index',
    component: '@/pages/supplierMonthlyBill/index',
    title: '供应商月度账单',
    subTitle: '结算管理',
    subMenu: 'settlementManage',
    tabClass: 'voucherManage',
  },
  {
    path: '/xlb_scm/storeHouseCheck/index',
    component: '@/pages/storeHouseCheck/index',
    title: '仓库商品抽检',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'storeHouseCheck',
  },
  {
    path: '/xlb_scm/reportManageFilterItem/index',
    component: '@/pages/reportManageFilterItem/index',
    title: '外检报告过滤商品',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'reportManageFilterItem',
  },
  {
    path: '/xlb_scm/qualityOrder/index',
    component: '@/pages/qualityOrder/index',
    title: '质量问题管理',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'surveyReportManage',
  },
  {
    path: '/xlb_scm/governmentSpotChecks/index',
    component: '@/pages/governmentSpotChecks/header/index',
    title: '政府抽检汇总',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'rebateDetails',
  },
  {
    path: '/xlb_scm/contractRebateParameters/index',
    component: '@/pages/contractRebateParameters/index',
    title: '合同与返利参数',
    subTitle: '参数配置',
    subMenu: 'basicConfig',
    tabClass: 'contractRebateParameters',
  },
  {
    path: '/xlb_scm/supplierCheckDetail/index',
    component: '@/pages/supplierCheckDetail/index',
    title: '抽检单合格率',
    subTitle: '数据查询',
    subMenu: 'dataQuery',
    tabClass: 'supplierCheckDetail',
  },
  {
    // Productsampling
    path: '/xlb_scm/productsampling/index',
    component: '@/pages/productsampling/index',
    title: '商品抽检标准',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'productsampling',
  },
  {
    path: '/xlb_scm/stockUpPlan/index',
    component: '@/pages/stockUpPlan/index',
    title: '采购计划',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'stockUpPlan',
  },
  {
    path: '/xlb_scm/evaluationPass/index',
    component: '@/pages/evaluationPass/index',
    title: '品评通过率',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'evaluationPass',
  },
  {
    path: '/xlb_scm/newItemReport/index',
    component: '@/pages/Business/newItemReport/index',
    title: '新品报告',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'newItemReport',
  },
  {
    path: '/xlb_scm/newProductSuccessJudge/index',
    component: '@/pages/newProductSuccessJudge/index',
    title: '新品成功判定',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'newProductSuccessJudge',
  },
  {
    path: '/xlb_scm/newProductAudit/index',
    component: '@/pages/newProductAudit/index',
    title: '新品审核周期',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'newProductAudit',
  },
  {
    path: '/xlb_scm/deliveryCycleSetting/index',
    component: '@/pages/deliveryCycleSetting/index',
    title: '交货周期设置',
    subTitle: '业务管理',
    subMenu: 'businessManage',
    tabClass: 'deliveryCycleSetting',
  },
  {
    path: '/xlb_scm/spotCheckReportCompletionRate/index',
    component: '@/pages/spotCheckReportCompletionRate/index',
    title: '检测报告完成率',
    subTitle: '数据查询',
    subMenu: 'dataQuery',
    tabClass: 'spotCheckReportCompletionRate',
  },
  {
    path: '/xlb_scm/contractRebatReport/index',
    component: '@/pages/Data/contractRebatReport/index',
    title: '合同返利明细',
    subTitle: '数据查询',
    subMenu: 'dataQuery',
    tabClass: 'contractRebatReport',
  },

  {
    path: '/xlb_scm/contractRebatReport/index',
    component: '@/pages/Data/contractRebatReport/index',
    title: '合同返利明细',
    subTitle: '数据查询',
    subMenu: 'dataQuery',
    tabClass: 'contractRebatReport',
  },
  {
    
     path: '/xlb_scm/directSupplyAnalysis/index',
    component: '@/pages/Data/directSupplyAnalysis/index',
    title: '直供分析',
    subTitle: '数据查询',
    subMenu: 'dataQuery',
    tabClass: 'directSupplyAnalysis',
  }
];
