import { routeList } from './index';

// kms菜单栏配置
export const MenuList = {
  Board: {
    label: '看板',
    key: 'scmBoard',
    isWujie: true,
    iconUrl: 'iconfont icon-kanban',
  },
  basicConfig: {
    label: '档案',
    iconUrl: 'iconfont icon-dangan',
    key: 'basicConfig',
    list: [
      { label: '档案管理', children: [] },
      { label: '参数配置', children: [] },
    ],
  },
  businessManage: {
    label: '业务',
    iconUrl: 'iconfont icon-dangan',
    key: 'businessManage',
    list: [{ label: '业务管理', children: [] }],
  },
  dataQuery: {
    label: '数据',
    iconUrl: 'iconfont icon-dangan',
    key: 'dataQuery',
    list: [{ label: '数据查询', children: [] }],
  },
  settlementManage: {
    label: '结算',
    iconUrl: 'iconfont icon-dangan',
    key: 'settlementManage',
    list: [{ label: '结算管理', children: [] }],
  },
};

export const RouteSetting = {
  ['xlb_scm']: {
    MenuList: MenuList,
    RouteList: routeList,
  },
};
