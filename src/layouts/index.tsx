import { appName, isWUJIE } from '@/wujie/utils';
import { KeepAlive, Outlet, useAliveController, useLocation } from '@umijs/max';
import { XlbErrorBoundary } from '@xlb/components';
import { useSubWujie } from '@xlb/max';
import { useState } from 'react';
import XlbMenus from './component/xlbMenus';
import style from './index.less';

const HomePage: React.FC = () => {
  const { pathname } = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const { dropScope } = useAliveController();

  const [menuType] = useState<{ Menu: string; subMenu: string }>({
    Menu: appName,
    subMenu: '',
  });

  useSubWujie({
    appName: 'xlb_scm',
  });

  return (
    <div className={style['xlb-sub-container']}>
      {!isWUJIE() && (
        <XlbMenus
          menuType={menuType}
          setCollapsed={setCollapsed}
          collapsed={collapsed}
        />
      )}

      <div
        className={style.xlb_main}
        style={{
          width: collapsed ? 'calc(100vw - 60px)' : 'calc(100vw - 120px)',
        }}
      >
        <XlbErrorBoundary>
          <KeepAlive
            when={true}
            name={pathname}
            id={pathname}
            autoFreeze={false}
            cacheKey={pathname}
            // saveScrollPosition="screen"
          >
            <Outlet />
          </KeepAlive>
        </XlbErrorBoundary>
      </div>
    </div>
  );
};

export default HomePage;
