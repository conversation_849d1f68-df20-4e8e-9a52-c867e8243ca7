import { XlbFetch } from '@xlb/utils';
import { create, StateCreator } from 'zustand';
import { persist } from 'zustand/middleware';

export type useBase = {
  /**是否启用组织机构 */
  enable_organization?: boolean;
  setEnableOrganization: (enable_organization: boolean) => void;
  getEnableOrganization: () => Promise<void>;
};

type BoundState = useBase;

export type BoundStateCreator<SliceState> = StateCreator<
  BoundState,
  [],
  [],
  SliceState
>;

export const createBaseParams = persist(
  (set) => ({
    enable_organization: undefined,
    setEnableOrganization: (enable_organization: boolean) =>
      set({ enable_organization }),
    getEnableOrganization: async () => {
      const res = await XlbFetch.post(
        `${process.env.BASE_URL}` + '/erp/hxl.erp.baseparam.read',
        {},
      );
      if (res?.data?.code == 0) {
        set({ enable_organization: res.data.enable_organization });
      }
    },
  }),
  {
    name: 'baseParams',
    partialize: (state: useBase) => ({
      enable_organization: state.enable_organization,
    }),
  },
);

export const useBaseParams = create<BoundState>((...arg) =>
  createBaseParams(...arg),
);
