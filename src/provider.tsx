import NiceModal from '@ebay/nice-modal-react';
import { XlbConfigProvider } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { FC, PropsWithChildren } from 'react';
import { config } from './constants/baseDataConfig';
import { scmFormList } from './constants/config/scm';
import { replaceUrl } from './utils/purchaseUrl';
import { LStorage } from './utils/storage';

const Provider: FC<PropsWithChildren> = ({ children }) => {
  XlbFetch.interceptors.request.use((config) => {
    config.url = replaceUrl(
      config?.url ?? '',
      LStorage.get('userInfo')?.company_id,
    );
    return config;
  });
  return (
    <XlbConfigProvider.Provider
      value={{
        //@ts-ignore
        baseURL: process.env.BASE_URL,
        config: config,
        globalFetch: XlbFetch,
        columns: {
          query: '/erp/hxl.erp.usercolumn.get',
          update: '/erp/hxl.erp.usercolumn.update',
        },
        componentsConfig: {
          XlbPageContainer: {
            resizeColumn: true,
            defaultColumnSetting: true,
          },
        },
        fieldList: [...scmFormList],
      }}
    >
      <NiceModal.Provider>{children}</NiceModal.Provider>
    </XlbConfigProvider.Provider>
  );
};

export default Provider;
