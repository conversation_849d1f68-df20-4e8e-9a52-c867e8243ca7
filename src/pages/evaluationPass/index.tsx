import Container from '@/components/indexContainer';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbProPageContainer } from '@xlb/components';
import { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type';
import { FC } from 'react';

const Index: FC = () => {
  const userInfo = LStorage.get('userInfo');

  // 表单配置
  const formList = [
    {
      id: 'dateCommon',
      format: 'YYYY-MM-DD',
      label: '日期范围',
      name: 'appraise_date',
    },
    {
      id: 'commonSelect',
      label: '汇总条件',
      name: 'summary_conditions',
      fieldProps: {
        mode: 'multiple',
        options: [
          { label: '组织', value: 'ORG' },
          { label: '商品品类', value: 'ITEM_PUBLIC_CATEGORY' },
          { label: '品评日期', value: 'APPRAISE_DATE' },
        ],
      },
    },
    {
      id: 'commonSelect',
      label: '所属组织',
      name: 'org_ids',
      fieldProps: {
        mode: 'multiple',
        options: userInfo?.query_orgs
          ? userInfo?.query_orgs
              .filter((ele: any) => ele.level === 2)
              .map((item: any) => ({
                ...item,
                label: item.name,
                value: item.id,
              }))
          : [],
      },
      disabled: !!userInfo.supplier,
      dependencies: ['summary_conditions'],
      hidden: (formValues) => !formValues?.summary_conditions?.includes('ORG'),
    },
    {
      id: ScmFieldKeyMap.goodsCategoryId,
      name: 'item_public_category_ids',
      label: '商品品类',
      fieldProps: {
        mode: 'multiple',
      },
      dependencies: ['summary_conditions'],
      hidden: (formValues) =>
        !formValues?.summary_conditions?.includes('ITEM_PUBLIC_CATEGORY'),
    },
  ] as CreateMapItem[];

  // 表格列配置
  const tableColumn = (formValues: any) => {
    return [
      {
        name: '序号',
        code: '_index',
        width: 80,
        align: 'center',
      },
      {
        name: '组织',
        code: 'org_name',
        width: 188,
        hidden: !formValues?.summary_conditions?.includes('ORG'),
      },
      {
        name: '品评日期',
        code: 'appraise_date',
        width: 175,
        hidden: !formValues?.summary_conditions?.includes('APPRAISE_DATE'),
      },
      {
        name: '商品品类',
        code: 'item_public_category_name',
        width: 175,
        hidden: !formValues?.summary_conditions?.includes(
          'ITEM_PUBLIC_CATEGORY',
        ),
      },
      {
        name: '申请数量',
        code: 'apply_num',
        width: 100,
        align: 'center',
      },
      {
        name: '品评通过数量',
        code: 'pass_num',
        width: 120,
        align: 'center',
      },
      {
        name: '品评通过率',
        code: 'pass_rate',
        width: 120,
        align: 'center',
        render: (value: any) => `${Math.round((value ?? 0) * 100) / 100}%`,
      },
    ];
  };

  return (
    <Container>
      <XlbProPageContainer
        searchFieldProps={{
          formList,
          initialValues: {
            summary_conditions: ['ORG'],
          },
        }}
        tableFieldProps={{
          url: '/scm-mdm/hxl.scm.newitemapplyorder.appraiseaudit.passrate',
          tableColumn,
          changeColumnAndResetDataSource: false,
          immediatePost: false,
          showColumnsSetting: false,
        }}
        exportFieldProps={{
          url: hasAuth(['品评通过率', '导出'])
            ? '/scm-mdm/hxl.scm.newitemapplyorder.appraiseaudit.passrate.export'
            : undefined,
          fileName: '品评通过率',
        }}
      />
    </Container>
  );
};
export default Index;
