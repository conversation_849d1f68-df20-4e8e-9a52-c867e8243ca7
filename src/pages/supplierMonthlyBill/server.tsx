import XlbFetch from '@/utils/XlbFetch';
import { XlbFetch as fetch } from '@xlb/utils/';

// 签字确认单导出
export default {
  billsignExport: async (data: any) => {
    return await fetch.post(
      `${process.env.BASE_URL}` + '/fss/hxl.fss.suppliermonthbillsign.export',
      data,
      {
        responseType: 'blob',
      },
    );
  },
  //对账单导出
  billExport: async (data: any) => {
    return await fetch.post(
      `${process.env.BASE_URL}` + '/fss/hxl.fss.suppliermonthbill.export',
      data,
      {
        responseType: 'blob',
      },
    );
  },
  //签字确认单删除
  billsignDelete: async (data: any) => {
    return await XlbFetch('/fss/hxl.fss.suppliermonthbillsign.delete', data);
  },
  billsignUpload: async (data: any) => {
    return await XlbFetch('/fss/hxl.fss.suppliermonthbillsign.upload', data);
  },
};
