import { ScmFieldKeyMap } from '@/constants/config/scm';
import { useBaseParams } from '@/hooks/useBaseParams';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbButton, XlbProPageContainer } from '@xlb/components';
import IconFont from '@xlb/components/dist/components/icon';
import moment from 'moment';
import { type FC } from 'react';
import { tableList, tureOrFalse, UPLOAD_STATE } from './data';
import Api from './server';
//月度账单
const ProForm: FC<{ title: string }> = () => {
  const userInfo = LStorage.get('userInfo');
  const { enable_organization } = useBaseParams((state) => state);

  const exportItem = async (fetchData: any, type: string, data: any) => {
    if (type === '对账单') {
      // data.responseType = 'blob';
      const res = await Api.billExport(data);
      const download = new Download();
      download.filename = '供应商月度对账单.zip';
      download.zip(res?.data);
    }

    if (type === '确认单') {
      // data.responseType = 'blob';
      const res = await Api.billsignExport(data);
      const download = new Download();
      download.filename = '供应商月度确认单.zip';
      download.zip(res?.data);
    }
  };
  return (
    <div style={{ height: 'calc(100vh - 80px)' }}>
      <XlbProPageContainer // 查询
        searchFieldProps={{
          formList: [
            {
              id: ScmFieldKeyMap.billDate,
            },
            {
              id: ScmFieldKeyMap.supplierIds,
              disabled: userInfo?.supplier ? true : false,
            },
            {
              id: ScmFieldKeyMap.orgIds,
              hidden: !enable_organization || !!userInfo?.supplier,
            },
            {
              id: ScmFieldKeyMap.billStoreIds,
            },
            {
              id: 'commonSelect',
              name: 'sign_flag',
              label: '签字确认单',
              fieldProps: { options: UPLOAD_STATE },
            },
            {
              id: 'commonSelect',
              label: '核对完成',
              name: 'check_flag',
              fieldProps: { options: tureOrFalse },
            },
          ],
          initialValues: {
            supplier_ids: userInfo?.supplier ? [userInfo.supplier.id] : null,
            bill_date: moment().subtract(1, 'months').format('YYYY-MM'),
            // store_ids_name: [LStorage.get('userInfo')?.store],
            // store_ids: [LStorage.get('userInfo')?.store_id]
          },
        }}
        tableFieldProps={{
          url: '/fss/hxl.fss.suppliermonthbill.page',
          tableColumn: tableList,
          selectMode: 'multiple',
          immediatePost: true,
        }}
        deleteFieldProps={{
          url: hasAuth(['供应商月度账单', '编辑'])
            ? '/fss/hxl.fss.suppliermonthbill.delete'
            : '',
        }}
        extra={(context) => {
          const { fetchData, loading, dataSource, requestForm } = context;
          return (
            <XlbButton.Group>
              {hasAuth(['供应商月度账单', '导出']) && (
                <XlbButton
                  type="primary"
                  onClick={() => exportItem(fetchData, '对账单', requestForm)}
                  loading={loading}
                  disabled={dataSource?.length === 0}
                  icon={
                    <IconFont name="daochu" color="currentColor" size={16} />
                  }
                >
                  导出对账单
                </XlbButton>
              )}
              {hasAuth(['供应商月度账单', '导出']) && (
                <XlbButton
                  type="primary"
                  onClick={() => exportItem(fetchData, '确认单', requestForm)}
                  loading={loading}
                  disabled={dataSource?.length === 0}
                  icon={
                    <IconFont name="daochu" color="currentColor" size={16} />
                  }
                >
                  导出确认单
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
      />
    </div>
  );
};

export default ProForm;
