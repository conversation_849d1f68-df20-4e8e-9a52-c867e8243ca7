import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbBaseUpload, XlbTableColumnProps } from '@xlb/components';
import { message } from 'antd';
import Api from './server';

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 72,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 120,
    align: 'center',
  },
  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '对账门店',
    code: 'store_name',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '对账月份',
    code: 'bill_date',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '操作',
    code: 'url',
    width: 72,
    features: { sortable: true },
    render: (text: any) => {
      return (
        <span
          className="link cursor"
          onClick={(e) => {
            e.stopPropagation();
            window.open(text, '_blank');
          }}
        >
          查看
        </span>
      );
    },
  },
  {
    name: '生成人',
    code: 'create_by',
    width: 102,
    features: { sortable: true },
  },
  {
    name: '生成时间',
    code: 'create_time',
    width: 212,
    features: { sortable: true },
  },
  {
    name: '签字确认单',
    code: 'sign_url',
    width: 212,
    features: { sortable: true },
    render: (text: any, record, index: any) => {
      return (
        <div>
          {!text ? (
            <div>
              {hasAuth(['供应商月度账单', '编辑']) && (
                <XlbBaseUpload
                  data={{
                    refType: 'allowanceApply',
                    //id: record.id
                  }}
                  mode="textButton"
                  listType="text"
                  multiple={false}
                  maxCount={1}
                  accept={'pdf'}
                  fileList={[]}
                  onOk={async (data) => {
                    if (!data[0].originFileObj) return;
                    let formData = new FormData();
                    formData.append('file', data[0]?.originFileObj);
                    formData.append('id', record.id);
                    const res = await fetch(
                      process.env.BASE_URL +
                        '/fss/hxl.fss.suppliermonthbillsign.upload',
                      {
                        method: 'POST',
                        body: formData,
                        headers: {
                          'Access-Token': LStorage.get('access_token'),
                          'Api-Version': '1.5.0',
                        },
                      },
                    );
                    
                    if (res?.status === 200) {
                      message.success('上传成功');
                    } else {
                      let msgData = await res.json();
                      message.error(msgData?.msg || '上传失败');
                    }
                    index?.fetchData();
                  }}
                  onCancel={() => index?.fetchData()}
                />
              )}
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbBaseUpload
                mode="look"
                hiddenControlerIcon={true}
                showUpload={false}
                listType={'text'}
                fileList={[{ url: text }]}
              />
              {hasAuth(['供应商月度账单', '编辑']) && !record?.check_flag && (
                <span
                  className="link cursors"
                  style={{ marginLeft: '10px' }}
                  onClick={async (e) => {
                    e.stopPropagation();
                    const res = (await Api.billsignDelete({
                      id: record.id,
                    })) as any;
                    if (res.code === 0) {
                      index?.fetchData();
                      message.success('删除成功');
                    }
                  }}
                >
                  删除
                </span>
              )}
            </div>
          )}
        </div>
      );
    },
  },
  {
    name: '核对完成',
    code: 'check_flag',
    width: 212,
    features: { sortable: true },
    render: (text: any) => {
      return text?.toString ? (
        text ? (
          <span className="success">是</span>
        ) : (
          <span>否</span>
        )
      ) : (
        ''
      );
    },
  },
];

export const UPLOAD_STATE = [
  {
    label: '未上传',
    value: false,
  },
  {
    label: '已上传',
    value: true,
  },
];

export const tureOrFalse = [
  { label: '是', value: true },
  { label: '否', value: false },
];
