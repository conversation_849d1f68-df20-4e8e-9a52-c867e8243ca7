import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
} from '@xlb/components';
import dayjs from 'dayjs';
import { formList, tableList } from './data';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const NewItemReport = () => {
  const [form] = XlbBasicForm.useForm<any>();

  const prevPost = () => {
    const { ...rest } = form.getFieldsValue(true);
    return {
      ...rest,
      date: [
        dayjs(rest?.date?.[0])?.format('YYYY-MM-DD'),
        dayjs(rest?.date?.[1])?.format('YYYY-MM-DD'),
      ],
    };
  };

  return (
    <XlbPageContainer
      url={'/scm/hxl.scm.newitemsuccessdetermination.report'}
      tableColumn={tableList}
      prevPost={prevPost}
      immediatePost={true}
    >
      <ToolBtn showColumnsSetting>
        {({ fetchData, loading }) => {
          return (
            <XlbButton.Group>
              <XlbButton
                key="query"
                label="查询"
                type="primary"
                disabled={loading}
                onClick={() => {
                  fetchData();
                }}
                icon={<span className="iconfont icon-sousuo" />}
              />
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <SearchForm>
        <XlbForm
          formList={formList}
          form={form}
          isHideDate
          initialValues={{
            create_date: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
          }}
        />
      </SearchForm>
      <Table primaryKey="id" key="id" />
    </XlbPageContainer>
  );
};

export default NewItemReport;
