import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const formList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'create_date',
    allowClear: false,
  },
  {
    label: '商品分类',
    name: 'category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
    clear: true,
    check: true,
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '新品数',
    code: 'new_item_num',
    align: 'left',
    width: 180,
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '成功',
    code: 'success_num',
    align: 'left',
    width: 180,
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '不成功',
    code: 'fail_num',
    align: 'left',
    width: 180,
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '转正常品',
    code: 'transfer_normal_num',
    align: 'left',
    width: 180,
    features: { sortable: true, format: 'QUANTITY' },
  },
];
