import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbProPageContainer } from '@xlb/components';
import { FC } from 'react';
import { Columns, stateType } from './data';

const Index: FC = () => {
  const userInfo = LStorage.get('userInfo');
  // TODO总计
  return (
    <div style={{ height: 'calc(100vh - 80px)' }}>
      <XlbProPageContainer
        searchFieldProps={{
          formList: [
            {
              id: ScmFieldKeyMap.stockUpPlanOrgIds,
              disabled: !!userInfo?.supplier?.id,
            },
            { id: ScmFieldKeyMap?.selectStoreIds },
            { id: ScmFieldKeyMap?.scmItemIds },
            {
              id: 'commonSelect',
              name: 'enable',
              label: '状态',
              options: stateType,
            },
          ],
          initialValues: {
            app_type: 'SCM',
            supplier_ids: userInfo?.supplier?.id
              ? [userInfo?.supplier?.id]
              : null,
          },
        }}
        tableFieldProps={{
          url: '/erp/hxl.scm.erp.purchaseplandetail.page',
          tableColumn: Columns,
          primaryKey: 'id',
          selectMode: 'single',
          immediatePost: false,
        }}
        exportFieldProps={{
          url: hasAuth(['采购计划', '导出'])
            ? '/erp/hxl.scm.erp.purchaseplandetail.export'
            : undefined,
          fileName: '采购计划',
        }}
      />
    </div>
  );
};
export default Index;
