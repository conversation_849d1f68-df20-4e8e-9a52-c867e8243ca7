import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { hasAuth } from '@/utils/kit';
import { XlbInputNumber, XlbTableColumnProps } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';

export const stateType = [
  { label: '启用', value: true },
  { label: '禁用', value: false },
];

/**
 * @description: 更新库存数量
 * @param {any} record 行数据
 * @param {any} value 值
 * @param {any} fetchData 请求
 */
const updateChange = async (record: any, value: any, fetchData: any) => {
  if (value < 0) return;
  let params: any = {};
  params = {
    id: record?.id,
    supplier_stock_quantity: value,
  };

  const res = await XlbFetch.post<any, any>(
    `${process.env.BASE_URL}/erp/hxl.scm.erp.purchaseplandetail.update`,
    {
      ...params,
    },
  );
  if (res?.code === 0) {
    message.success('操作成功');
    fetchData();
  }
};

export const Columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: COLUMN_WIDTH_ESUM.INDEX,
    align: 'center',
  },
  {
    name: '所属组织',
    code: 'org_id',
    width: 130,
    features: { sortable: true },
    render(text, record) {
      return record?.org_name;
    },
  },
  {
    name: '所属门店',
    code: 'store_id',
    width: 130,
    features: { sortable: true },
    render(text, record) {
      return record?.store_name;
    },
  },
  {
    name: '商品名称',
    code: 'item_id',
    width: 180,
    features: { sortable: true },
    render: (text, record) => {
      return record?.item_name;
    },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_barcode',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'purchase_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '状态',
    code: 'enable',
    width: 90,
    features: { sortable: true },
    render(text) {
      return text ? '启用' : '禁用';
    },
  },
  {
    name: '开始时间',
    code: 'begin_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '结束时间',
    code: 'end_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '预测数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购数量',
    code: 'purchase_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '库存数量',
    code: 'supplier_stock_quantity',
    width: 100,
    features: { sortable: true, cellNoPadding: true },
    render(text, record, index) {
      return !(record?._edit && hasAuth(['采购计划', '编辑'])) ? (
        <div style={{ padding: '4px 8px' }}>{text}</div>
      ) : (
        <>
          <XlbInputNumber
            value={text}
            min={0}
            style={{ marginTop: 4 }}
            onClick={(e) => {
              e.stopPropagation();
            }}
            onFocus={(e) => {
              e.target.select();
            }}
            onBlur={(e) => {
              updateChange(record, e.target.value, index?.fetchData);
            }}
          />
        </>
      );
    },
  },
  {
    name: '最近更新时间',
    code: 'update_time',
    width: 150,
    features: { sortable: true },
  },
];
