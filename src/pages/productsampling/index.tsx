// import { ScmFieldKeyMap } from '@/constants/fieldsConfig';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer, XlbTipsModal } from '@xlb/components';
import dayjs from 'dayjs';
import { FC } from 'react';
import { tableList } from './data';

const Index: FC = () => {
  return (
    <XlbProPageContainer
      details={{
        primaryKey: 'id',
        hiddenSaveBtn: true,
        isCancel: true,
        // queryFieldProps: {
        //   // /fsms/hxl.fsms.organizationpayinfo.read
        //   url: hasAuth(['商品抽检标准', '查询'])
        //     ? '/fsms/hxl.fsms.organizationpayinfo.read'
        //     : '',
        //   params: (row: any) => {
        //     return { ...row };
        //   },
        // },
        title: (v) => {
          return !v?.id ? '新增' : '编辑';
        },
        mode: 'modal',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              itemSpan: 18,
              formList: [
                {
                  id: ScmFieldKeyMap.scmInputNumber,
                  name: 'receive_quantity_start',
                  label: '商品到货数量始',
                  fieldProps: {
                    precision: 0,
                    min: 0,
                  },
                  rules: [{ required: true, message: '请输入商品到货数量始' }],
                },
                {
                  id: ScmFieldKeyMap.scmInputNumber,
                  name: 'receive_quantity_end',
                  label: '商品到货数量止',
                  fieldProps: {
                    precision: 0,
                    min: 0,
                  },
                  dependencies: ['receive_quantity_start'],
                  rules: [
                    ({ getFieldValue }: any) => ({
                      required: true,
                      validator: (_, value) => {
                        if (!!!value) {
                          return Promise.reject('请输入商品到货数量止');
                        }
                        const start = getFieldValue('receive_quantity_start');
                        if (!!value && start > value) {
                          return Promise.reject(
                            '商品到货数量始不能大于商品到货数量止',
                          );
                        }
                        return Promise.resolve();
                      },
                    }),
                  ],
                },
                {
                  id: ScmFieldKeyMap.scmInputNumber,
                  name: 'normal_check_num',
                  label: '正常品抽检数',
                  fieldProps: {
                    precision: 0,
                    min: 0,
                  },
                  rules: [{ required: true, message: '请输入正常品抽检数' }],
                },
                {
                  id: ScmFieldKeyMap.scmInputNumber,
                  name: 'point_check_num',
                  label: '重点品抽检数',
                  fieldProps: {
                    precision: 0,
                    min: 0,
                  },
                  rules: [{ required: true, message: '请输入重点品抽检数' }],
                },
                {
                  id: ScmFieldKeyMap.scmInputNumber,
                  name: 'exempt_check_num',
                  label: '免检品抽检数',
                  fieldProps: {
                    precision: 0,
                    min: 0,
                  },
                  rules: [{ required: true, message: '请输入免检品抽检数' }],
                },
              ],
            },
          },
        ],
        // updateFieldProps: {
        //   url: hasAuth(['商品抽检标准', '编辑']) ? '/fsms/hxl.fsms.organizationpayinfo.update' : '',
        //   // beforePost(data) {
        //   //   return {
        //   //     ...data,
        //   //     parent_id: data?.parent_id || 0,
        //   //   };
        //   // },
        // },
      }}
      searchFieldProps={{
        formList: (formValues) => {
          return [{ id: 'dateCommon', label: '日期范围', name: 'create_date' }];
        },
        initialValues: {
          // query_model: 'ITEM',
          create_date: [
            dayjs().format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
          ],
        },
      }}
      tableFieldProps={{
        url: '/scm/hxl.scm.itemspotcheckstandard.page',
        tableColumn: tableList,
        immediatePost: true,
        selectMode: 'multiple',
        primaryKey: 'id',
        showColumnsSetting: false,
      }}
      addFieldProps={{
        url: hasAuth(['商品抽检标准', '编辑'])
          ? '/scm/hxl.scm.itemspotcheckstandard.save'
          : '',
      }}
      deleteFieldProps={{
        url: hasAuth(['商品抽检标准', '删除'])
          ? '/scm/hxl.scm.itemspotcheckstandard.batchdelete'
          : '',
        params(data: any) {
          return {
            ids: data,
          };
        },
        beforeTips: (selectRowKey: string[], selectRow?: any[] | undefined) => {
          return XlbTipsModal({
            tips: `是否删除该标准`,
            isCancel: true,
            onOk: () => {
              return true;
            },
          });
        },
        // showField: 'id',
      }}
    />
  );
};

export default Index;
