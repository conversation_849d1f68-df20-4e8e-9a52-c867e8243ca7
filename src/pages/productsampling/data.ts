import { XlbTableColumnProps } from '@xlb/components';

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
    lock: true,
  },
  {
    name: '商品到货数量',
    code: 'receive_quantity_start',
    width: 200,
    features: { sortable: true },
    render(text, record, index) {
      return `${record?.receive_quantity_start}-${record?.receive_quantity_end}`;
    },
  },
  {
    name: '正常品抽检数',
    code: 'normal_check_num',
    features: { sortable: true },
    width: 150,
  },
  {
    name: '重点品抽检数',
    code: 'point_check_num',
    features: { sortable: true },
    width: 156,
  },
  {
    name: '免检品抽检数',
    code: 'exempt_check_num',
    features: { sortable: true },
    width: 156,
  },
  {
    name: '创建人',
    code: 'create_by',
    features: { sortable: true },
    width: 162,
  },
  {
    name: '创建时间',
    code: 'create_time',
    features: { sortable: true },
    width: 162,
  },
];
