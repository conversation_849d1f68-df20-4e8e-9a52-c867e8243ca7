// import { XlbFetch } from '@/utils';

import XlbFetch from '@/utils/XlbFetch';

export const getLog = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.itemoffshelf.operationrecord.read', data);
};
export const update = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.itemoffshelf.update', data);
};
export const save = async (data: any) => {
  return await XlbFetch('/scm/hxl.scm.itemoffshelf.save', data);
};
export default {
  getLog,
};
