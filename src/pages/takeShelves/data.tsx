export const stateType = [
  { label: '合格', value: 'QUALIFIED' },
  { label: '不合格', value: 'UNQUALIFIED' },
];
export let staterecode = {
  AUDITING: '审核中',
  AUDIT_REFUSE: '审核拒绝',
  HANDING: '处理中',
  HAND_REFUSE: '处理拒绝',
  APPROVEING: '批复中',
  APPROVE: '批复通过',
  APPROVAL_REFUSE: '批复拒绝',
};
export const stateEmail = [
  // { label: '制单', value: 'INIT', type: 'info' },
  { label: '审核中', value: 'AUDITING', type: 'warning' },
  { label: '处理中', value: 'HANDING', type: 'warning' },
  { label: '批复中', value: 'APPROVEING', type: 'warning' },
  { label: '审核拒绝', value: 'AUDIT_REFUSE', type: 'danger' },
  { label: '处理通过', value: 'HANDLE_PASS', type: 'success' },
  { label: '批复通过', value: 'APPROVE', type: 'success' },
  { label: '处理拒绝', value: 'HAND_REFUSE', type: 'danger' },
  { label: '批复拒绝', value: 'APPROVAL_REFUSE', type: 'danger' },
  // { label: '已撤回', value: 'ROLLBACK', type: 'invalid' },
];
export let typeArry = [
  // 审核中、审核拒绝、处理中、处理拒绝、批复中、批复通过、批复拒绝
  {
    label: '审核中',
    value: 'AUDITING',
    type: 'success',
  },
  {
    label: '审核拒绝',
    value: 'AUDIT_REFUSE',
    type: 'danger',
  },
  {
    label: '处理中',
    value: 'HANDING',
    type: 'danger',
  },
  {
    label: '处理拒绝',
    value: 'HAND_REFUSE',
    type: 'danger',
  },
  {
    label: '批复中',
    value: 'APPROVEING',
    type: 'danger',
  },
  {
    label: '批复通过',
    value: 'APPROVE',
    type: 'danger',
  },
  {
    label: '批复拒绝',
    value: 'APPROVAL_REFUSE',
    type: 'danger',
  },
];
export const operation = [
  { value: '提交审核', type: 'primary' },
  { value: '撤销', type: 'danger' },
  { value: '审核通过', type: 'success' },
  { value: '审核拒绝', type: 'danger' },
  { value: '处理通过', type: 'success' },
  { value: '处理拒绝', type: 'danger' },
];
