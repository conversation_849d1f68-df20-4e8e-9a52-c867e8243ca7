// import { hasAuth } from '@/utils/kit';
import {
  ContextState,
  XlbBasicData,
  XlbButton,
  XlbDrawer,
  XlbDropdownButton,
  XlbIcon,
  XlbProPageContainer,
  XlbTableColumnProps,
  XlbTimeLine,
} from '@xlb/components';
import { message } from 'antd';
import dayjs from 'dayjs';
import { FC, useState } from 'react';
// import { hasAuth } from '@/utils';
import defaultAvatar from '@/assets/images/basiclayout/defaultAvatar.png';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { operation, stateEmail, typeArry } from './data';
import { getLog, save, update } from './server';
const Index: FC = () => {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<any>([]);
  const Columns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 80,
      align: 'center',
    },
    {
      name: '单据号',
      code: 'fid',
      width: 120,
      align: 'center',
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 130,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: 130,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 130,
      features: { sortable: true },
      render: (text, record) => {
        return record?.item_name;
      },
    },
    {
      name: '状态',
      code: 'state',
      width: 130,
      features: { sortable: true },
      render(text, record, index) {
        const obj = stateEmail.find((item) => item.value === text);
        return <div className={obj?.type}>{obj?.label}</div>;
        // return staterecode[text];
      },
    },
    {
      name: '提交人',
      code: 'update_by',
      width: 80,
      features: { sortable: true },
    },
    {
      name: '提交时间',
      code: 'update_time',
      width: 200,
      features: { sortable: true },
    },
    {
      name: '操作',
      code: 'option',
      width: 88,
      align: 'center',
      render: (_, record: any) => {
        return (
          <XlbButton
            type="text"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleClick(record);
            }}
          >
            操作记录
          </XlbButton>
        );
      },
    },
  ];

  const handleClick = async (data: any) => {
    const res: any = await getLog({ fid: data.fid });
    // /center/hlx.center.itemoffshelf.operationrecord.read;
    setOpen(true);
    if (res.code === 0) {
      setData(res.data);
      console.log(res.data);
    }
  };
  const addItem = async (
    setLoading: Function | undefined,
    fetchData: Function,
  ) => {
    const list = await XlbBasicData({
      dataType: 'tree',
      isMultiple: true,
      type: 'item',
    });
    if (list?.length) {
      const data = { item_ids: list.map((v) => v.id) };
      setLoading?.(true);
      const res = await save({ ...data });
      setLoading?.(false);
      if (res?.code === 0) {
        fetchData();
        message.success('操作成功');
      }
    }
  };
  const typestates = {
    审核通过: 'HANDING',
    审核拒绝: 'AUDIT_REFUSE',
    处理通过: 'APPROVEING',
    处理拒绝: 'HAND_REFUSE',
    批复通过: 'APPROVE',
    批复拒绝: 'APPROVAL_REFUSE',
  } as const;
  const handleResults = (
    type: keyof typeof typestates,
    context: ContextState,
  ) => {
    console.log(context, 'context');
    update({ fid: context?.selectRowKeys?.[0], state: typestates[type] }).then(
      (res) => {
        // console.log(res);
        message.success('操作成功');
        context?.fetchData();
      },
    );
  };
  // TODO总计
  return (
    <>
      <XlbDrawer
        placement={'right'}
        title="操作记录"
        onClose={() => setOpen(false)}
        open={open}
        footer={false}
        width={500}
      >
        <XlbTimeLine
          mode="left"
          items={data.map((item: any, index: number) => ({
            activeKey: index,
            color: 'gray',
            label: (
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {item.operate}
              </div>
            ),
            children: (
              <div style={{ display: 'flex', alignItems: 'center', gap: 10 }}>
                <img width={36} height={36} src={defaultAvatar} />
                <div style={{ flex: 1 }}>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span style={{ fontSize: 14, fontWeight: 400 }}>
                      {item.create_by}
                    </span>
                    <span style={{ fontSize: 12, color: '#86909C' }}>
                      {item.create_time}
                    </span>
                  </div>
                  <div>
                    <span
                      className={
                        operation.find((i) => i.value == item.action)?.type
                      }
                      style={{ fontSize: 12 }}
                    >
                      {item?.action}
                    </span>
                    {/* {['审核拒绝', '处理拒绝'].includes(item.operate) && <span style={{ fontSize: 12, marginLeft: 10 }}> 拒绝原因：{item.memo}</span>} */}
                  </div>
                </div>
              </div>
            ),
          }))}
          defaultActiveKey={['1', '2']}
        />
      </XlbDrawer>
      <XlbProPageContainer
        searchFieldProps={{
          formList: [
            { id: 'dateCommon', name: 'sumbit_date' },
            { id: ScmFieldKeyMap?.scmItemId, label: '商品档案' },
            // {
            //   label: '商品档案',
            //   id: 'itemIds',
            //   name: 'item_ids',
            //   fieldProps: {
            //     dialogParams: {
            //       type: 'goods',
            //       dataType: 'lists',
            //       isLeftColumn: true,
            //       isMultiple: true,
            //       data: {
            //         enabled: true,
            //       },
            //     },
            //   },
            // },
            {
              label: '状态',
              id: 'commonSelect',
              name: 'state',
              fieldProps: {
                options: typeArry,
              },
              // onChange(e, form) {
              //   form?.setFieldsValue({ ids: undefined });
              // },
            },
          ],
          initialValues: {
            sumbit_date: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.itemoffshelf.page',
          tableColumn: Columns,
          primaryKey: 'fid',
          selectMode: 'single',
          immediatePost: true,
        }}
        deleteFieldProps={{
          url: hasAuth(['商品下架', '删除'])
            ? '/scm/hxl.scm.itemoffshelf.delete'
            : '',
          // beforeTips: (selectRowKeys: string[], selectRow: any[] | undefined) => {
          //   return XlbTipsModal({
          //     tips: `是否删除所选中商品？`,
          //     isCancel: true,
          //     onOk: () => {
          //       return true;
          //     },
          //   });
          // },
          params(data: any) {
            return {
              ids: data,
            };
          },
          name: '删除',
          showField: 'fid',
        }}
        exportFieldProps={{
          url: hasAuth(['商品下架', '导出'])
            ? '/scm/hxl.scm.itemoffshelf.export'
            : undefined,
          fileName: '商品下架',
        }}
        extra={(context) => {
          const { setLoading, fetchData, selectRow } = context;
          return (
            <>
              {hasAuth(['商品下架', '编辑']) ? (
                <XlbButton
                  type="primary"
                  label="新增"
                  onClick={() => addItem(setLoading, fetchData)}
                  icon={<XlbIcon name="jia" />}
                />
              ) : null}
              {hasAuth(['商品下架', '审核']) &&
              selectRow?.[0]?.state === 'AUDITING' ? (
                <XlbDropdownButton
                  // @ts-ignore
                  // disabled={context?.selectRow[0]?.state === 'AUDITING'}
                  trigger={['click']}
                  dropList={[{ label: '审核通过' }, { label: '审核拒绝' }]}
                  dropdownItemClick={(index) => {
                    // console.log(index);
                    const type = index === 0 ? '审核通过' : '审核拒绝';
                    handleResults(type, context);
                  }}
                  label={'审核'}
                ></XlbDropdownButton>
              ) : null}
              {hasAuth(['商品下架', '处理']) &&
              selectRow?.[0]?.state === 'HANDING' ? (
                <XlbDropdownButton
                  // @ts-ignore
                  // disabled={}
                  trigger={['click']}
                  dropList={[{ label: '处理通过' }, { label: '处理拒绝' }]}
                  dropdownItemClick={(index) => {
                    const type = index === 0 ? '处理通过' : '处理拒绝';
                    handleResults(type, context);
                  }}
                  label={'处理'}
                ></XlbDropdownButton>
              ) : null}
              {hasAuth(['商品下架', '批复']) &&
              selectRow?.[0]?.state === 'APPROVEING' ? (
                <XlbDropdownButton
                  // @ts-ignore
                  // disabled={!context.selectRow.length}
                  trigger={['click']}
                  dropList={[{ label: '批复通过' }, { label: '批复拒绝' }]}
                  dropdownItemClick={(index) => {
                    const type = index === 0 ? '批复通过' : '批复拒绝';
                    handleResults(type, context);
                  }}
                  label={'批复'}
                ></XlbDropdownButton>
              ) : null}
            </>
          );
        }}
      />
    </>
  );
};
export default Index;
