import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
} from '@xlb/components';
import dayjs from 'dayjs';
import { formList, tableList } from './data';
import { exportContract } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const ContractRebatReportIndex = () => {
  const [form] = XlbBasicForm.useForm<any>();

  const prevPost = () => {
    const values = form.getFieldsValue(true);
    return {
      start_time: values?.date_time?.[0],
      end_time: values?.date_time?.[1],
      ...values,
    };
  };

  const exportItem = async (data: any) => {
    const res = await exportContract(data);
    const download = new Download();
    download.filename = '合同返利明细.xlsx';
    download.xlsx(res?.data);
  };

  return (
    <XlbPageContainer
      url={'/scm/hxl.scm.suppliercontract.page'}
      tableColumn={tableList}
      prevPost={prevPost}
      immediatePost={true}
      footerDataSource={(data) => {
        return [
          {
            _index: '合计',
            basic_in_quantity: data?.total_basic_in_quantity,
            quantity_subtotal: data?.total_quantity_subtotal,
            money: data?.money,
            no_tax_money: data?.total_no_tax_money,
            cash_rebate_amount: data?.total_cash_rebate_amount,
            no_tax_cash_rebate_amount: data?.total_no_tax_cash_rebate_amount,
          },
        ];
      }}
    >
      <ToolBtn showColumnsSetting>
        {({ fetchData, loading, requestForm }) => {
          return (
            <XlbButton.Group>
              <XlbButton
                key="query"
                label="查询"
                type="primary"
                disabled={loading}
                onClick={() => {
                  fetchData();
                }}
                icon={<XlbIcon name="sousuo" />}
              />

              {hasAuth(['合同返利明细', '导出']) && (
                <XlbButton
                  key="query"
                  label="导出"
                  type="primary"
                  disabled={loading}
                  onClick={() => {
                    exportItem(requestForm);
                  }}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <SearchForm>
        <XlbForm
          formList={formList}
          form={form}
          isHideDate
          initialValues={{
            date_time: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
          }}
        />
      </SearchForm>
      <Table primaryKey="id" key="id" />
    </XlbPageContainer>
  );
};

export default ContractRebatReportIndex;
