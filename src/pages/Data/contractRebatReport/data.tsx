import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { FormInstance } from 'antd';
import { getHouseList } from './server';

//查询门店下仓库
const getStockData = async (id: any) => {
  if (id?.length > 1) {
    return [];
  } else {
    let tempId = id;
    if (Array.isArray(id)) {
      tempId = id && id[0];
    }
    let houseArrs: any[] = [];
    const res = await getHouseList({ store_id: tempId });
    if (res.code == 0) {
      const defaultHouseList = res?.data; //?.filter((item: any) => item.default_flag === true)
      houseArrs = await defaultHouseList.map((item: any) => {
        return { label: item.name, value: item.id };
      });
      return houseArrs;
    }
  }
};

// 时间类型
export const timeTypes = [
  {
    label: '制单时间',
    value: 0,
  },
  {
    label: '审核时间',
    value: 1,
  },
  {
    label: '处理时间',
    value: 2,
  },
  {
    label: '作废时间',
    value: 3,
  },
];

export const rebateCycleTypes = [
  {
    label: '日',
    value: 'DAY',
  },
  {
    label: '周',
    value: 'WEEKLY',
  },
  {
    label: '月',
    value: 'MONTHLY',
  },
  {
    label: '季',
    value: 'QUARTERLY',
  },
  {
    label: '年',
    value: 'YEAR',
  },
];

export const formList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'date_time',
    select_type: 'month',
    allowClear: false,
    format: 'YYYY-MM',
  },
  {
    label: '组织',
    name: 'org_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择组织',
      url: '/erp/hxl.erp.org.tree',
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      checkStrictly: true,
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    allowClear: true,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    onChange: async (e, _, form: FormInstance) => {
      if (!e) {
        form.setFieldsValue({
          storehouse_id: undefined,
          storehouse: undefined,
        });
      } else {
        form.setFieldsValue({
          storehouse: await getStockData(form.getFieldValue('store_ids')),
          storehouse_id: undefined,
        });
      }
    },
  },
  {
    label: '仓库',
    name: 'storehouse_id',
    type: 'select',
    disabled: (form) => {
      const { store_ids } = form.getFieldsValue(true);
      return !store_ids || store_ids?.length > 1;
    },
    dependencies: ['store_ids'],
    linkId: 'storehouse',
    width: 200,
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,

    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '商品分类',
    name: 'category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类',
      url: '/erp/hxl.erp.category.find',
      dataType: 'lists',
      checkable: true,
      primaryKey: 'id',
      width: 420, // 模态框宽度
    },
    clear: true,
    check: true,
  },
  {
    label: '返利周期',
    name: 'rebate_cycle',
    type: 'select',
    allowClear: true,
    options: rebateCycleTypes,
  },
  {
    label: '返利方式',
    name: 'rebate_type',
    type: 'select',
    allowClear: true,
    options: [
      {
        label: '单品返利',
        value: 'SINGLE',
      },
      {
        label: '阶梯返利',
        value: 'FIXED',
      },
      {
        label: '商品分销奖励',
        value: 'ITEM_REWARDS',
      },
      {
        label: '无实物报损',
        value: 'CARGO_DAMAGE',
      },
    ],
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '月份',
    code: 'month',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '组织',
    code: 'org_name',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '商品品牌',
    code: 'item_brand_name',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_specification',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '采购单位',
    code: 'purchase_unit',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_in_quantity',
    width: 100,
    align: 'center',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '数量小计',
    code: 'quantity_subtotal',
    width: 100,
    align: 'center',
    features: { sortable: false, format: 'QUANTITY' },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    align: 'center',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '合同号',
    code: 'contract_fid',
    width: 100,
    align: 'center',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '返利周期',
    code: 'rebate_cycle',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '返利方式',
    code: 'rebate_type',
    width: 100,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '下限额度',
    code: 'lower_limit',
    width: 100,
    align: 'center',
    features: { sortable: false },
  },
  {
    name: '返利比例',
    code: 'rebate_ratio_str',
    width: 100,
    align: 'center',
    features: { sortable: false },
  },
  {
    name: '返利金额',
    code: 'rebate_amount',
    width: 100,
    align: 'center',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '现金返利金额',
    code: 'cash_rebate_amount',
    width: 120,
    align: 'center',
    features: { sortable: true, format: 'MONEY' },
  },
];
