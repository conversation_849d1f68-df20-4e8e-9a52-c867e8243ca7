import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { FormInstance } from 'antd';
import { getHouseList } from './server';

//查询门店下仓库
const getStockData = async (id: any) => {
  if (id?.length > 1) {
    return [];
  } else {
    let tempId = id;
    if (Array.isArray(id)) {
      tempId = id && id[0];
    }
    let houseArrs: any[] = [];
    const res = await getHouseList({ store_id: tempId });
    if (res.code == 0) {
      const defaultHouseList = res?.data; //?.filter((item: any) => item.default_flag === true)
      houseArrs = await defaultHouseList.map((item: any) => {
        return { label: item.name, value: item.id };
      });
      return houseArrs;
    }
  }
};

// 时间类型
export const timeTypes = [
  {
    label: '制单时间',
    value: 0,
  },
  {
    label: '审核时间',
    value: 1,
  },
  {
    label: '处理时间',
    value: 2,
  },
  {
    label: '作废时间',
    value: 3,
  },
];
export const TIME_TYPE = [
  { label: '创建时间', value: 'create_date' },
  { label: '审核时间', value: 'audit_date' },
];

export const formList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'date_time',
    select_type: 'month',
    allowClear: false,
    format: 'YYYY-MM',
  },
  {
    id: 'timeType',
    name: 'time_type',
    type: 'select',
    label: '时间类型',
    allowClear: false,
    defaultValue: 'audit_date',
    options: TIME_TYPE,
  },
  {
    // 默认为为空,多选,必填,枚举:门店、供应商、单据、日、周、月
    // 支持按照时间维度查询门店数据、供应商数据、订单数据
    // 汇总条件支持多选
    id: 'commonSelect',
    label: '汇总条件',
    name: 'summary_conditions',
    type: 'select',
    options: [
      { label: '组织', value: 'ORG' },
      { label: '商品品类', value: 'ITEM_PUBLIC_CATEGORY' },
      { label: '品评日期', value: 'APPRAISE_DATE' },
    ], //
    fieldProps: {
      mode: 'multiple',
      options: [
        { label: '组织', value: 'ORG' },
        { label: '商品品类', value: 'ITEM_PUBLIC_CATEGORY' },
        { label: '品评日期', value: 'APPRAISE_DATE' },
      ],
    },
  },
  {
    label: '收货门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    allowClear: true,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    onChange: async (e, _, form: FormInstance) => {
      if (!e) {
        form.setFieldsValue({
          storehouse_id: undefined,
          storehouse: undefined,
        });
      } else {
        form.setFieldsValue({
          storehouse: await getStockData(form.getFieldValue('store_ids')),
          storehouse_id: undefined,
        });
      }
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,

    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'right',
  },
  {
    name: '时间',
    code: 'month',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '单据号',
    code: 'org_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货门店',
    code: 'store_name',
    width: 120,
    features: { sortable: true },
  },

  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '下单数量',
    code: 'basic_in_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },

  {
    name: '下单金额(元)',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '实发数量',
    code: 'basic_in_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },

  {
    name: '实发金额(元)',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '实发数量',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '实手金额(元)',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
  {
    name: '差异值',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true, format: 'MONEY' },
  },
];
