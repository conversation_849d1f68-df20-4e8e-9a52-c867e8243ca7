import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import {
  SearchFormType,
  XlbBaseUpload,
  XlbTableColumnProps,
} from '@xlb/components';
import dayjs from 'dayjs';

export const fileLogColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '有效期',
    code: 'valid_date',
    width: 110,
    features: { sortable: true },
    render(text) {
      return <span>{text && dayjs(text).format('YYYY-MM-DD')}</span>;
    },
  },
  {
    name: '外检报告',
    code: 'files',
    width: 130,
    features: { sortable: false },
    render(text, record, index) {
      return (
        <XlbBaseUpload
          mode="look"
          showUpload={false}
          listType={'picture'}
          fileList={text ?? []}
        />
      );
    },
  },
  {
    name: '归档时间',
    code: 'archive_date',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '操作人',
    code: 'create_by',
    width: COLUMN_WIDTH_ESUM.BY,
    features: { sortable: true },
  },
];

export const formList: SearchFormType[] = [
  {
    label: '商品档案',
    name: 'item_name',
    type: 'input',
    allowClear: true,
    disabled: true,
  },
  {
    name: 'supplier_name',
    label: '供应商',
    disabled: true,
    type: 'input',
  },
];
//采购类型
export const purchaseTypes = [
  { label: '集采品', value: 'COLLECTIVE_PURCHASE' },
  { label: '集售品', value: 'COLLECTIVE_SALE' },
  { label: '地采品', value: 'GROUND_PURCHASE' },
  { label: '店采品', value: 'SHOP_PURCHASE' },
];
