import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { history } from '@umijs/max';
import {
  XlbBaseUploadFormItem,
  XlbBasicForm,
  XlbDatePicker,
  XlbProPageContainer,
  XlbTable,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { FC, useEffect, useState } from 'react';
import ChangeRequest from './changeRequest';
import { fileLogColumn, purchaseTypes } from './data';
import Api from './server';
const Index: FC = () => {
  const userInfo = LStorage.get('userInfo');
  const [form] = XlbBasicForm.useForm();
  const [record, setRecord] = useState(history?.location.state as any);

  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      align: 'center',
    },
    {
      name: '供应商',
      code: 'supplier_name',
      width: 180,
      features: { sortable: true },
    },
    {
      name: '所属组织',
      code: 'org_name',
      width: 180,
      features: { sortable: true },
    },
    {
      name: '门店',
      code: 'stores',
      width: 180,
      features: {},
      render(text, record, index) {
        return <div>{text?.map((item) => item?.store_name)?.join(',')}</div>;
      },
    },
    {
      name: '生产商',
      code: 'producer_supplier_name',
      width: 180,
      features: { sortable: true },
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: COLUMN_WIDTH_ESUM.ITEM_CODE,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'bar_code',
      width: COLUMN_WIDTH_ESUM.ITEM_BAR_CODE,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 180,
      features: { sortable: true },
    },
    {
      name: '采购类型',
      code: 'purchase_type',
      width: 180,
      features: { sortable: true },
      render(text) {
        return (
          <div>
            {purchaseTypes?.find((item) => item?.value === text)?.label}
          </div>
        );
      },
    },
    {
      name: '采购规格',
      code: 'purchase_spec',
      width: COLUMN_WIDTH_ESUM.ITEM_SPEC,
      features: { sortable: true },
    },
    {
      name: '有效期',
      code: 'valid_date',
      width: 130,
      features: { sortable: true },
      render(text, record) {
        return (
          <span
            style={{ color: dateColor(text, record.item_report_remind_days) }}
          >
            {text}
          </span>
        );
      },
    },
    {
      name: '外检报告',
      code: 'report',
      width: 160,
      render(text, record, index) {
        return (
          <div>
            {!isEmpty(text) && (
              <span
                className="link"
                style={{ marginRight: 10 }}
                onClick={() => uploadFielsModel(record, 'updata')}
              >
                查看
              </span>
            )}
            {isEmpty(text) &&
              !userInfo.supplier &&
              hasAuth(['外检报告', '编辑']) && (
                <span
                  className="link"
                  style={{ marginRight: 10 }}
                  onClick={() =>
                    uploadFielsModel(record, 'add', index.fetchData)
                  }
                >
                  上传
                </span>
              )}
            {!isEmpty(text) &&
              !userInfo.supplier &&
              hasAuth(['外检报告', '编辑']) && (
                <span
                  className="link"
                  onClick={() => delReportFiles(record, index.fetchData)}
                >
                  删除
                </span>
              )}
          </div>
        );
      },
    },
    {
      name: '变更申请',
      code: 'change_file_req_dtos',
      width: 160,
      render(text, record, index) {
        return (
          <>
            <span
              className="link"
              style={{ marginRight: 10 }}
              onClick={() => {
                XlbTipsModal({
                  title: '变更申请',
                  bordered: true,
                  width: 800,
                  onCancel(e) {
                    //@ts-ignore
                    index?.fetchData();
                  },
                  tips: <ChangeRequest data={record} />,
                });
              }}
            >
              查看
            </span>
            {record?.change_state !== 'INIT' &&
              hasAuth(['外检报告', '编辑']) &&
              !!userInfo.supplier && (
                <span
                  className="link"
                  onClick={() =>
                    uploadFielsModel(record, 'change', index.fetchData)
                  }
                >
                  上传
                </span>
              )}
          </>
        );
      },
    },
    {
      name: '归档',
      code: 'archive_report',
      width: 120,
      render(text, record, index) {
        return (
          <span
            className="link"
            onClick={async () => {
              await XlbTipsModal({
                title: '查看归档',
                footer: null,
                width: 600,
                tips: (
                  <div style={{ height: 300, margin: '12px 0' }}>
                    <XlbTable
                      style={{ height: 'calc(100% - 50px)' }}
                      columns={fileLogColumn}
                      dataSource={text ?? []}
                      selectMode="single"
                      keepDataSource={false}
                      total={text?.length}
                    />
                  </div>
                ),
              });
            }}
          >
            查看
          </span>
        );
      },
    },
    {
      name: '审批备注',
      code: 'handle_memo',
      width: 180,
      features: { sortable: true },
    },
    {
      name: '最后更新人',
      code: 'update_by',
      width: COLUMN_WIDTH_ESUM.BY,
      features: { sortable: true },
    },
    {
      name: '最后更新时间',
      code: 'update_time',
      width: 160,
      features: { sortable: true },
    },
  ];

  const uploadFielsModel = async (
    record: any,
    type: 'updata' | 'add' | 'change',
    fetchData?: Function,
  ) => {
    type == 'updata' &&
      record?.report &&
      form.setFieldsValue({
        ...record.report,
      });
    await XlbTipsModal({
      title: type === 'change' ? '变更申请' : '外检报告',
      bordered: true,
      isCancel: true,
      isConfirm: (type === 'updata' && !userInfo.supplier) || type !== 'updata',
      tips: (
        <>
          <XlbBasicForm form={form} colon={true} style={{ margin: '12px 0' }}>
            <XlbBasicForm.Item
              label="有效期"
              name="valid_date"
              rules={[{ required: true, message: '请选择有效期' }]}
            >
              <XlbDatePicker width={305} format={'YYYY-MM-DD'} />
            </XlbBasicForm.Item>
            <XlbBaseUploadFormItem
              name="files"
              label={'附件'}
              rules={[{ required: true, message: '请上传外检报告' }]}
              uploadProps={{
                width: 68,
                data: {
                  id: record.id,
                },
                mode: 'default',
                listType: 'picture',
                multiple: true,
                action:
                  type === 'change'
                    ? '/scm/hxl.scm.supplierqualitychangerecord.file.upload'
                    : '/scm/hxl.scm.supplierqualityrecord.file.upload',
              }}
            />
          </XlbBasicForm>
        </>
      ),
      onCancel(e) {
        form.resetFields();
      },
      onOkBeforeFunction: async () => {
        try {
          await form.validateFields();
        } catch (error) {
          return false;
        }

        const data = {
          ...form.getFieldsValue(),
          id: record.id,
          record_id: record.id,
        };

        const res =
          type === 'change'
            ? await Api.addChangeItem(data)
            : type !== 'updata'
              ? await Api.addItem(data)
              : await Api.updataItem(data);
        //@ts-ignore
        if (res?.code === 0) {
          message.success('操作成功');
          fetchData && fetchData();
        }

        form.resetFields();
        return true;
      },
    });
  };

  const delReportFiles = async (data: any, fetchData?: Function) => {
    XlbTipsModal({
      tips: '请确认是否删除!',
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await Api.delItem({ id: data.id });
        //@ts-ignore
        if (res.code === 0) {
          message.success('操作成功');
          //@ts-ignore
          fetchData();
          return true;
        }
      },
    });
  };

  const dateColor = (date: string, remindDay: number) => {
    if (!date) return '';
    if (dayjs(date).format('YYYY-MM-DD') < dayjs().format('YYYY-MM-DD'))
      return '#FF0000';
    if (
      dayjs(date).format('YYYY-MM-DD') >= dayjs().format('YYYY-MM-DD') &&
      dayjs(date).diff(dayjs(), 'day') <= remindDay
    )
      return '#FF7D01';
    return '#1D2129';
  };
  useEffect(() => {
    setRecord(history?.location.state as any);
  }, [JSON.stringify(history?.location.state as any)]);
  // console.log(history?.location.state, 'history?.location.state');

  // console.log(record, 'recordrecord===>');

  // TODO总计
  return (
    <XlbProPageContainer
      searchFieldProps={{
        formList: (formValue: any) => {
          const baseForm = [
            {
              id: 'supplierIds',
              name: 'supplier_ids',
              label: '供应商',
              disabled: !!userInfo.supplier,
              fieldProps: {
                dialogParams: {
                  type: 'supplier',
                  dataType: 'lists',
                  isMultiple: true,
                },
              },
            },
            {
              id: 'itemIds',
              name: 'item_ids',
              label: '商品档案',
              fieldProps: {
                dialogParams: {
                  type: 'goods',
                  dataType: 'lists',
                  isMultiple: true,
                  url: '/erp/hxl.erp.item.supplier.page',
                  data: {
                    item_types: ['STANDARD', 'COMPONENT'],
                    stop_purchase: !!userInfo.supplier ? false : undefined,
                  },
                },
              },
            },
            {
              // id: ScmFieldKeyMap?.scmOrgIds,
              id: 'commonSelect',
              label: '所属组织',
              name: 'org_ids',
              fieldProps: {
                mode: 'multiple',
                options: userInfo?.query_orgs
                  ? userInfo?.query_orgs
                      .filter((ele) => ele.level < 3)
                      .map((item: any) => ({
                        ...item,
                        label: item.name,
                        value: item.id,
                      }))
                  : [],
              },
              disabled: !!userInfo.supplier,
            },
            {
              id: ScmFieldKeyMap?.scmCenterStoreIds,
              // name: '门店',
            },
            {
              name: 'change_states',
              label: '审批状态',
              id: 'commonSelect',
              fieldProps: {
                mode: 'multiple',
                options: [
                  { label: '审批通过', value: 'PASS' },
                  { label: '待审批', value: 'INIT' },
                  { label: '审批拒绝', value: 'DENY' },
                ],
              },
            },
            {
              name: 'purchase_types',
              label: '采购类型',
              id: 'commonSelect',
              fieldProps: {
                mode: 'multiple',
                options: purchaseTypes,
              },
            },
            'ScmInputPanel',
            {
              name: 'update_date',
              label: '日期',
              id: 'commonRangePicker',
            },
            {
              id: ScmFieldKeyMap?.centerLaterAndReport,
            },
          ];
          return baseForm;
        },
        initialValues: {
          supplier_ids: !!userInfo.supplier
            ? [userInfo.supplier.id]
            : undefined,
          ...record,
        },
      }}
      tableFieldProps={{
        url: '/scm/hxl.scm.supplierqualityrecord.page',
        tableColumn: tableColumn,
        primaryKey: 'id',
        immediatePost: true,
        selectMode: 'multiple',
      }}
      exportFieldProps={
        hasAuth(['外检报告', '导出'])
          ? {
              url: '/scm/hxl.scm.supplierqualityrecord.export',
              fileName: '外检报告',
            }
          : undefined
      }
      deleteFieldProps={{
        url: hasAuth(['外检报告', '删除'])
          ? '/scm/hxl.scm.supplierqualityrecord.batchdelete'
          : '',
        beforeTips: () => {
          return XlbTipsModal({
            tips: `是否删除外检报告？`,
            isCancel: true,
            onOk: () => {
              return true;
            },
          });
        },
        name: '批量删除',
      }}
    />
  );
};
export default Index;
