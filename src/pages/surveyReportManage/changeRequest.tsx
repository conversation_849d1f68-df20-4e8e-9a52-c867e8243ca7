import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbBaseUpload,
  XlbBasicForm,
  XlbDatePicker,
  XlbForm,
  XlbIcon,
  XlbInput,
  XlbPageContainer,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import { useEffect, useRef } from 'react';
import { formList } from './data';
import Api from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = (props: any) => {
  const { data, refresh } = props;

  const [form] = XlbBasicForm.useForm();
  const changeModelRef = useRef(null);
  const userInfo = LStorage.get('userInfo');

  const stateEnum: any = {
    INIT: '待审核',
    PASS: '已通过',
    DENY: '已拒绝',
  };
  const stateColorEnum = {
    INIT: 'info',
    PASS: 'success',
    DENY: 'danger',
  };

  const changeFileLogColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      align: 'center',
    },
    {
      name: '操作',
      code: 'delete',
      width: 50,
      align: 'center',
      hidden: !userInfo.supplier,
      render(text, record, index) {
        return (
          <>
            {record.state === 'INIT' &&
              !!userInfo.supplier &&
              hasAuth(['外检报告', '编辑']) && (
                <XlbIcon
                  name="shanchu"
                  size={16}
                  style={{ color: '#3D66FE' }}
                  onClick={() => {
                    XlbTipsModal({
                      tips: '请确认是否删除!',
                      isCancel: true,
                      onOkBeforeFunction: async () => {
                        const res = await Api.delChangeItem({ id: record.id });
                        //@ts-ignore
                        if (res.code === 0) {
                          message.success('操作成功');
                          //@ts-ignore
                          index?.fetchData();
                          return true;
                        }
                      },
                    });
                  }}
                />
              )}
          </>
        );
      },
    },
    {
      name: '生产商',
      code: 'producer_supplier_name',
      width: 180,
      features: { sortable: true },
    },
    {
      name: '有效期',
      code: 'valid_date',
      width: 110,
      features: { sortable: true },
      render(text, record) {
        if (record._edit && record.state === 'INIT') {
          return (
            <div
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <XlbDatePicker
                defaultValue={text}
                width={305}
                format={'YYYY-MM-DD'}
                onChange={(e) => {
                  record.valid_date = e;
                }}
                key={record._index + text?.toString()}
              />
            </div>
          );
        } else {
          return text;
        }
      },
    },
    {
      name: '外检报告',
      code: 'files',
      width: 130,
      features: { sortable: false },
      render(text) {
        return (
          <XlbBaseUpload
            mode="look"
            showUpload={false}
            listType={'picture'}
            fileList={text ?? []}
          />
        );
      },
    },
    {
      name: '申请人',
      code: 'create_by',
      width: COLUMN_WIDTH_ESUM.BY,
      features: { sortable: true },
    },
    {
      name: '申请时间',
      code: 'create_time',
      width: COLUMN_WIDTH_ESUM.BY,
      features: { sortable: true },
    },
    {
      name: '操作',
      code: 'operator',
      width: 160,
      render(text, record, index) {
        return (
          <>
            {!userInfo.supplier && record.state === 'INIT' ? (
              hasAuth(['外检报告', '审核']) && (
                <div>
                  <span
                    style={{
                      marginRight: 10,
                      padding: '0 15px 1px 15px',
                      display: 'inline-block',
                      borderRadius: 5,
                      backgroundColor: '#FF0000',
                      color: '#fff',
                    }}
                    onClick={() => auditHandle('DENY', record, index.fetchData)}
                  >
                    拒绝
                  </span>
                  <span
                    style={{
                      padding: '0 15px 1px 15px',
                      borderRadius: 5,
                      display: 'inline-block',
                      backgroundColor: '#00B42B',
                      color: '#fff',
                    }}
                    onClick={() => auditHandle('PASS', record, index.fetchData)}
                  >
                    通过
                  </span>
                </div>
              )
            ) : (
              //@ts-ignore
              <span className={stateColorEnum[record.state]}>
                {stateEnum[record.state]}
              </span>
            )}
          </>
        );
      },
    },
    {
      name: '审批备注',
      code: 'handle_memo',
      width: 160,
      features: { sortable: true },
    },
  ];

  const auditHandle = async (
    type: 'PASS' | 'DENY',
    record: any,
    fetchData?: Function,
  ) => {
    let bool: any;
    if (type === 'DENY') {
      bool = XlbTipsModal({
        title: '审批备注',
        bordered: true,
        isCancel: true,
        tips: (
          <>
            <XlbBasicForm form={form} style={{ marginTop: 12 }}>
              <XlbBasicForm.Item
                label="备注"
                name={'memo'}
                rules={[{ required: type === 'DENY', message: '请输入备注' }]}
              >
                <XlbInput.TextArea style={{ width: 290, height: 100 }} />
              </XlbBasicForm.Item>
            </XlbBasicForm>
          </>
        ),
        onCancel(e) {
          form.setFieldValue('memo', null);
        },
        onOkBeforeFunction: async () => {
          try {
            await form.validateFields();
          } catch (error) {
            return false;
          }
          const data = {
            memo: form.getFieldValue('memo'),
            id: record.id,
            state: type,
            valid_date: record.valid_date,
          };
          //   console.log('valid_date', data, record.valid_date)
          const res = await Api.handleChangeItem(data);
          //@ts-ignore
          if (res.code === 0) {
            message.success('操作成功');
            fetchData && fetchData();
            form.setFieldValue('memo', null);
            return true;
          }
        },
      });
      return;
    }
    const res = await Api.handleChangeItem({
      memo: '',
      id: record.id,
      state: type,
      valid_date: record.valid_date,
    });
    //@ts-ignore
    if (res.code === 0) {
      message.success('操作成功');
      fetchData && fetchData();
      return true;
    }
  };

  useEffect(() => {
    Object.values(data)?.length &&
      form.setFieldsValue({
        item_name: data?.item_name,
        supplier_name: data?.supplier_name,
        record_id: data?.id,
      });
  }, [data]);

  return (
    <div>
      <XlbPageContainer
        url={'/scm/hxl.scm.supplierqualitychangerecord.page'}
        tableColumn={changeFileLogColumn}
        ref={changeModelRef}
        immediatePost={true}
        prevPost={() => {
          return { record_id: data.id };
        }}
      >
        <XlbForm
          isHideDate
          formList={formList}
          form={form}
          style={{ marginTop: 12 }}
        />
        <Table key="id" selectMode="single" keepDataSource={false} />
      </XlbPageContainer>
    </div>
  );
};

export default Index;
