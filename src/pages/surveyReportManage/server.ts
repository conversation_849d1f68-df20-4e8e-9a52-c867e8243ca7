import XlbFetch from '@/utils/XlbFetch';

export default class Api {
  // 分页查询
  static delItem = async (data: any) =>
    await XlbFetch('/scm/hxl.scm.supplierqualityrecord.delete', data);
  static addItem = async (data: any) =>
    await XlbFetch('/scm/hxl.scm.supplierqualityrecord.save', data);
  static updataItem = async (data: any) =>
    await XlbFetch('/scm/hxl.scm.supplierqualityrecord.update', data);
  static addChangeItem = async (data: any) =>
    await XlbFetch('/scm/hxl.scm.supplierqualitychangerecord.save', data);
  static delChangeItem = async (data: any) =>
    await XlbFetch('/scm/hxl.scm.supplierqualitychangerecord.delete', data);
  static handleChangeItem = async (data: any) =>
    await XlbFetch('/scm/hxl.scm.supplierqualitychangerecord.audit', data);
}
