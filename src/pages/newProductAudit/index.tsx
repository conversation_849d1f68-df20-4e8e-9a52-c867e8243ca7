import Container from '@/components/indexContainer';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainerWithMemo } from '@xlb/components';
import dayjs from 'dayjs';
import { FOOD_SAFE_TYPE, STATE_TYPE, tableList, timeList } from './data';

enum TabOptions {
  Audit = 'audit',
  Time = 'time',
}

const NewProductAuditIndex = () => {
  return (
    <Container>
      <XlbProPageContainerWithMemo // 查询
        tabFieldProps={{
          defaultActiveKey: 'audit',
          name: 'tabs',
          items: [
            {
              label: '新品审核周期',
              key: TabOptions.Audit,
              children: {
                searchFieldProps: {
                  formList: [
                    {
                      id: 'dateCommon',
                      format: 'YYYY-MM-DD',
                      name: 'create_date',
                      label: '日期选择',
                      fieldProps: {
                        allowClear: false,
                      },
                    },
                    {
                      id: ScmFieldKeyMap.goodsCategoryId,
                      name: 'item_public_category_id',
                      label: '商品品类',
                    },
                    {
                      label: '状态',
                      name: 'states',
                      id: 'commonSelect',
                      fieldProps: {
                        mode: 'multiple',
                        options: STATE_TYPE,
                      },
                    },
                    {
                      label: '食安状态',
                      name: 'food_safe_audit_states',
                      id: 'commonSelect',
                      fieldProps: {
                        mode: 'multiple',
                        options: FOOD_SAFE_TYPE,
                      },
                    },
                    'keyword',
                  ],
                  initialValues: {
                    create_date: [
                      dayjs().startOf('d').format('YYYY-MM-DD'),
                      dayjs().endOf('d').format('YYYY-MM-DD'),
                    ],
                  },
                },

                tableFieldProps: {
                  url: hasAuth(['新品审核周期', '查询'])
                    ? '/scm-mdm/hxl.scm.newitemapplyorder.auditcycle'
                    : '',
                  tableColumn: tableList,
                  selectMode: 'multiple',
                  immediatePost: false,
                },
                exportFieldProps: {
                  url: hasAuth(['新品审核周期', '导出'])
                    ? '/scm-mdm/hxl.scm.newitemapplyorder.auditcycle.export'
                    : '',
                  fileName: '新品审核周期汇总',
                },
              },
            },
            {
              label: '新品周期',
              key: TabOptions.Time,
              children: {
                searchFieldProps: {
                  formList: [
                    {
                      id: 'dateCommon',
                      format: 'YYYY-MM-DD',
                      name: 'create_date',
                      label: '日期选择',
                      fieldProps: {
                        allowClear: false,
                      },
                    },
                    {
                      label: '状态',
                      name: 'states',
                      id: 'commonSelect',
                      fieldProps: {
                        mode: 'multiple',
                        options: STATE_TYPE,
                      },
                    },
                    'keyword',
                  ],
                  initialValues: {
                    create_date: [
                      dayjs().startOf('d').format('YYYY-MM-DD'),
                      dayjs().endOf('d').format('YYYY-MM-DD'),
                    ],
                  },
                },

                tableFieldProps: {
                  url: hasAuth(['新品审核周期', '查询'])
                    ? '/scm-mdm/hxl.scm.newitemapplyorder.cycle'
                    : '',
                  tableColumn: timeList,
                  selectMode: 'multiple',
                  immediatePost: false,
                },
                exportFieldProps: {
                  url: hasAuth(['新品审核周期', '导出'])
                    ? '/scm-mdm/hxl.scm.newitemapplyorder.cycle.export'
                    : '',
                  fileName: '新品周期',
                },
              },
            },
          ],
        }}
      />
    </Container>
  );
};

export default NewProductAuditIndex;
