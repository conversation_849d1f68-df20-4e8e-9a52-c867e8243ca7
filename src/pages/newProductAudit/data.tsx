//初审中、初审拒绝、品评中、品评拒绝、二次谈判中、采购总监审核中、审核拒绝、集团商品中心处理中、处理通过、处理拒绝、食安批复中、批复通过、批复拒绝
export const STATE_TYPE1 = [
  { label: '制单', value: 'INIT', color: 'info' },
  { label: '初审中', value: 'REVIEW_AUDIT_ING', color: 'warning' },
  { label: '初审拒绝', value: 'FIRST_REVIEW_REJECT', color: 'danger' },
  { label: '初审驳回', value: 'REVIEW_AUDIT_OPPOSE', color: 'danger' },
  { label: '品评中', value: 'APPRAISE_AUDIT_ING', color: 'warning' },
  { label: '品评拒绝', value: 'APPRAISE_AUDIT_REJECT', color: 'danger' },
  { label: '二次谈判中', value: 'NEGOTIATE_AUDIT_ING', color: 'warning' },
  { label: '二次谈判拒绝', value: 'NEGOTIATE_AUDIT_REJECT', color: 'danger' },
  {
    label: '公司采购总监审核中',
    value: 'PURCHASING_DIRECTOR_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '公司采购总监审核通过',
    value: 'PURCHASING_DIRECTOR_AUDIT_PASS',
    color: 'success',
  },
  {
    label: '集团品类经理审核中',
    value: 'GROUP_PRODUCT_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团品类经理拒绝',
    value: 'GROUP_PRODUCT_MANAGER_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '集团采购总监审核中',
    value: 'GROUP_CPO_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团采购总监拒绝',
    value: 'GROUP_CPO_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '集团采购总监通过',
    value: 'GROUP_CPO_AUDIT_PASS',
    color: 'success',
  },
  {
    label: '公司食安审核中',
    value: 'COMPANY_FOOD_SAFE_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '公司食安审核拒绝',
    value: 'COMPANY_FOOD_SAFE_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '公司食安审核驳回',
    value: 'COMPANY_FOOD_SAFE_AUDIT_OPPOSE',
    color: 'danger',
  },
  { label: '食安审核中', value: 'FOOD_SAFE_AUDIT_ING', color: 'warning' },
  { label: '食安审核拒绝', value: 'FOOD_SAFE_AUDIT_REJECT', color: 'danger' },
  { label: '食安审核驳回', value: 'FOOD_SAFE_AUDIT_OPPOSE', color: 'danger' },
  {
    label: '公司总经理审核中',
    value: 'COMPANY_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '公司总经理审核拒绝',
    value: 'COMPANY_MANAGER_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '集团总经理审核中',
    value: 'GROUP_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团总经理审核通过',
    value: 'GROUP_MANAGER_AUDIT_PASS',
    color: 'success',
  },
  {
    label: '集团总经理审核拒绝',
    value: 'GROUP_MANAGER_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '公司商品主档审核中',
    value: 'COMPANY_ITEM_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '公司商品主档审核拒绝',
    value: 'COMPANY_ITEM_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '集团商品主档审核中',
    value: 'GROUP_ITEM_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团商品主档审核拒绝',
    value: 'GROUP_ITEM_AUDIT_REJECT',
    color: 'danger',
  },
  { label: '审核通过', value: 'AUDIT_PASS', color: 'success' },
  { label: '审核拒绝', value: 'AUDIT_REJECT', color: 'danger' },
  {
    label: '供应链经理审核中',
    value: 'SCM_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '供应链经理审核拒绝',
    value: 'SCM_MANAGER_AUDIT_REJECT',
    color: 'danger',
  },
  {
    label: '集团商品中心处理中',
    value: 'GROUP_ITEM_CENTER_HANDLE_ING',
    color: 'warning',
  },
  { label: '处理通过', value: 'HANDLE_PASS', color: 'success' },
  { label: '处理拒绝', value: 'HANDLE_REJECT', color: 'danger' },
  {
    label: '品控批复中',
    value: 'QUALITY_CONTROL_APPROVE_ING',
    color: 'warning',
  },
  { label: '食安批复中', value: 'FSMS_APPROVE_ING', color: 'warning' },
  { label: '批复通过', value: 'APPROVE_PASS', color: 'success' },
  { label: '批复拒绝', value: 'APPROVE_REJECT', color: 'danger' },
  { label: '已结束', value: 'END', color: 'default' },
];

export const STATE_TYPE = [
  { label: '制单', value: 'INIT', color: 'info' },
  { label: '初审中', value: 'REVIEW_AUDIT_ING', color: 'warning' },
  { label: '初审拒绝', value: 'FIRST_REVIEW_REJECT', color: 'danger' },
  { label: '初审驳回', value: 'REVIEW_AUDIT_OPPOSE', color: 'danger' },
  { label: '品评中', value: 'APPRAISE_AUDIT_ING', color: 'warning' },
  { label: '品评拒绝', value: 'APPRAISE_AUDIT_REJECT', color: 'danger' },
  { label: '二次谈判中', value: 'NEGOTIATE_AUDIT_ING', color: 'warning' },
  { label: '二次谈判拒绝', value: 'NEGOTIATE_AUDIT_REJECT', color: 'danger' },
  {
    label: '公司采购总监审核中',
    value: 'COMPANY_CPO_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团品类经理审核中',
    value: 'GROUP_PRODUCT_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团采购总监审核中',
    value: 'PURCHASING_DIRECTOR_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团采购总监审核通过',
    value: 'PURCHASING_DIRECTOR_AUDIT_PASS',
    color: 'success',
  },
  {
    label: '集团总经理审核中',
    value: 'GROUP_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团总经理审核拒绝',
    value: 'GROUP_MANAGER_AUDIT_REJECT',
    color: 'danger',
  },
  // {
  //   label: '公司商品主档审核中',
  //   value: 'COMPANY_ITEM_AUDIT_ING',
  //   color: 'warning',
  // },
  // {
  //   label: '公司商品主档审核拒绝',
  //   value: 'COMPANY_ITEM_AUDIT_REJECT',
  //   color: 'danger',
  // },
  {
    label: '集团商品主档审核中',
    value: 'GROUP_ITEM_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团商品主档审核拒绝',
    value: 'GROUP_ITEM_AUDIT_REJECT',
    color: 'danger',
  },
  { label: '审核通过', value: 'AUDIT_PASS', color: 'success' },
  { label: '已结束', value: 'END', color: 'info' },
];

export enum FoodSafeType {
  noStart = 'NO_START',
  waitAudit = 'WAIT_AUDIT',
  audit = 'AUDIT',
  reject = 'REJECT',
  oppose = 'OPPOSE',
}

export const FOOD_SAFE_TYPE = Object.freeze([
  { label: '未开始', value: FoodSafeType.noStart, color: 'info' },
  { label: '待审核', value: FoodSafeType.waitAudit, color: 'warning' },
  { label: '审核通过', value: FoodSafeType.audit, color: 'success' },
  { label: '审核拒绝', value: FoodSafeType.reject, color: 'danger' },
  { label: '驳回', value: FoodSafeType.oppose, color: 'danger' },
]);

import { XlbTableColumnProps } from '@xlb/components';

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: 160,
    align: 'center',
  },
  {
    name: '公司',
    code: 'org_name',
    width: 180,
    align: 'center',
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 180,
    align: 'center',
  },
  {
    name: '商品名称',
    code: 'apply_item_name',
    width: 120,
    align: 'center',
  },
  {
    name: '商品品类',
    code: 'item_public_category_name',
    width: 120,
    align: 'center',
  },
  {
    name: '状态',
    code: 'state',
    width: 180,
    align: 'center',
    render: (text) => {
      return (
        <span className={STATE_TYPE.find((item) => item.value == text)?.color}>
          {STATE_TYPE.find((item) => item.value == text)?.label}
        </span>
      );
    },
  },
  {
    name: '食安状态',
    code: 'food_safe_audit_state',
    width: 175,
    features: { sortable: true },
    render: (text) => {
      return (
        <span
          className={FOOD_SAFE_TYPE.find((item) => item.value === text)?.color}
        >
          {FOOD_SAFE_TYPE.find((item) => item.value === text)?.label}
        </span>
      );
    },
  },
  {
    name: '初审操作时间',
    code: 'review_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '品评操作时间',
    code: 'appraise_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '二次谈判操作时间',
    code: 'negotiate_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '公司采购总监操作时间',
    code: 'company_purchasing_director_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '集团品类经理操作时间',
    code: 'group_public_category_manager_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '集团采购总监操作时间',
    code: 'group_purchasing_director_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '公司食安操作时间',
    code: 'food_safe_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '集团总经理操作时间',
    code: 'group_manager_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '集团主档操作时间',
    code: 'company_item_audit_use_time',
    width: 120,
    align: 'center',
  },
  {
    name: '提交人',
    code: 'submit_by',
    width: 120,
    align: 'center',
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: 220,
    align: 'center',
  },
];
export const timeList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: 160,
    align: 'center',
  },
  {
    name: '公司',
    code: 'org_name',
    width: 180,
    align: 'center',
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 180,
    align: 'center',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 220,
    align: 'center',
  },

  {
    name: '状态',
    code: 'state',
    width: 180,
    align: 'center',
    render: (text) => {
      return (
        <span className={STATE_TYPE.find((item) => item.value == text)?.color}>
          {STATE_TYPE.find((item) => item.value == text)?.label}
        </span>
      );
    },
  },
  {
    name: '提报日期',
    code: 'submit_date',
    width: 220,
    align: 'center',
  },
  {
    name: '过会日期',
    code: 'meetings_date',
    width: 180,
    features: { format: 'TIME' },
    align: 'center',
  },
  {
    name: '建档日期',
    code: 'archive_date',
    width: 180,
    features: { format: 'TIME' },
    align: 'center',
  },
  {
    name: '首单下单日期',
    code: 'first_order_date',
    width: 180,
    features: { format: 'TIME' },
    align: 'center',
  },
  {
    name: '首次到仓日期',
    code: 'first_in_order_date',
    width: 180,
    features: { format: 'TIME' },
    align: 'center',
  },
  {
    name: '首店销售日期',
    code: 'first_sale_date',
    width: 180,
    features: { format: 'TIME' },
    align: 'center',
  },
  {
    name: '提交人',
    code: 'submit_by',
    width: 120,
    align: 'center',
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: 180,
    features: { format: 'TIME' },
    align: 'center',
  },
];
