import { ScmFieldKeyMap } from '@/constants/config/scm';
import { dateStrSlice } from '@/utils/format';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { history } from '@@/core/history';
import { ProFormDependency } from '@ant-design/pro-components';
import {
  XlbButton,
  XlbDropdownButton,
  XlbIcon,
  XlbProForm,
  XlbProPageContainer,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { DetailFormList } from '@xlb/components/dist/lowcodes/XlbProDetail/type';
import { message } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import {
  UPLOAD_STATE,
  UPLOAD_TYPE,
  aduitlist,
  baseList,
  itemfiles,
  itemlook,
  states,
  supplierOptions,
  tableBaseList,
  tableList,
} from './data';
import styles from './index.less';
import {
  ocr,
  save,
  savebusinessproduction,
  supplierfilemanage,
} from './server';
const Index: React.FC = () => {
  const formRef = useRef<any>(null);
  const userInfo = LStorage.get('userInfo');
  const record = history.location.state as any;
  let onClose = () => {};
  const transformData = (key: string) => {
    const list = [...baseList];
    if (key === 'food_production') {
      list.splice(1, 0, itemfiles);
    }
    const transformList = list.map((item) => {
      return {
        ...item,
        name: [key, item.name[1]],
        dependencies: item?.dependencies ? [[key, 'start_date']] : undefined,
        rules:
          item?.name[1] === 'end_date'
            ? [
                ({ getFieldValue }: any) => ({
                  validator: (_, value) => {
                    const start = getFieldValue([key, 'start_date']);
                    if (!!value && start && dayjs(start).isAfter(value)) {
                      return Promise.reject('有效期始不得大于有效期止');
                    }
                    return Promise.resolve();
                  },
                }),
              ]
            : item?.rules,
        onChange:
          item?.name[1] === 'files'
            ? async (e, form, options, globalFetch, baseURL, index) => {
                if (e[0]?.url) {
                  const ocrType = {
                    license: 'BUSINESS_LICENSE',
                    food_production: 'FOOD_PRODUCTION_LICENSE',
                    food_business: 'FOOD_BUSINESS_LICENSE',
                  };
                  const ocrname = {
                    license: 'business_license',
                    food_production: 'food_production_license',
                    food_business: 'food_business_license',
                  };
                  const res = await ocr({ type: ocrType[key], url: e[0]?.url });
                  if (res?.code === 0) {
                    const datarang = dateStrSlice(
                      res?.data?.[ocrname?.[key]]?.valid_period,
                    );
                    // 食品经营许可法人legal_representative
                    // 食品经营许可名字operator_name
                    // 食品生产许可法人legal_representative
                    // 食品生产许可名字producer_name
                    const producer_name =
                      res?.data?.[ocrname?.[key]]?.company_name ||
                      res?.data?.[ocrname?.[key]]?.operator_name;
                    res?.data?.[ocrname?.[key]]?.producer_name;
                    const legal =
                      res?.data?.[ocrname?.[key]]?.legal_representative ||
                      res?.data?.[ocrname?.[key]]?.legal_person;
                    form?.setFieldsValue({
                      [key]: {
                        ...res?.data?.[ocrname?.[key]],
                        name: producer_name,
                        legal_person: legal,
                        start_date: datarang?.start_date,
                        end_date: datarang?.end_date,
                      },
                    });
                  }
                }
              }
            : undefined,
      };
    });
    return transformList;
  };

  const handleCheck = (value: any, key: string) => {
    let columns = [...tableBaseList];
    if (key == 'food_production') {
      columns.splice(7, 0, itemlook);
    }
    if (key == 'aduit') {
      columns = [...aduitlist];
    }
    if (key == 'business_production') {
      columns = tableBaseList.filter((item) => item.code === 'files');
    }
    XlbTipsModal({
      width: key == 'weighing_licence' ? 1200 : 800,
      bordered: true,
      isConfirm: false,
      title: key === 'aduit' ? '审核记录' : '查看归档',
      tips: (
        <XlbTable
          columns={columns || []}
          dataSource={
            (key !== 'aduit'
              ? value?.[key]?.archive_report
              : value?.record_report) || []
          }
          hideOnSinglePage={true}
        />
      ),
      isCancel: false,
    });
  };

  // 查看归档
  const readcheckout = (type: string, formValues: any) => {
    return {
      componentType: 'customer',
      render: (...args) => {
        const validateArry = transformData(type).map((ele) => ele?.name);
        return (
          <ProFormDependency name={[type]}>
            {(obj, form) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    gap: 20,
                  }}
                >
                  {
                    <XlbButton
                      type="primary"
                      onClick={() => handleCheck(formValues, type)}
                      icon={
                        <XlbIcon
                          name="xianshimima"
                          color="currentColor"
                          size={16}
                        />
                      }
                    >
                      查看归档
                    </XlbButton>
                  }
                  {formValues?.look_state === 'TEMP' &&
                    formValues?.state !== 'AUDIT' && (
                      <XlbButton
                        type="primary"
                        onClick={() => {
                          form
                            ?.validateFields(validateArry)
                            .then(async (res: any) => {
                              const ocrType = {
                                license: 'LICENSE', // 营业执照
                                food_production: 'FOODPRODUCTION', // 食品生产许可证
                                food_business: 'FOODBUSINESS', // 食品经营许可证
                                business_production: 'BUSINESSPRODUCTION',
                              };
                              const resresult = await (
                                type === 'business_production'
                                  ? savebusinessproduction
                                  : save
                              )({
                                ...res?.[type],
                                operation_type: ocrType[type],
                                id: formValues?.id,
                              });
                              resresult?.code === 0 &&
                                message.success('保存成功');
                            });
                        }}
                      >
                        保存
                      </XlbButton>
                    )}
                </div>
              );
            }}
          </ProFormDependency>
        );
      },
      // hidden:
      //   formValues?.look_state === 'PASS' || formValues?.state === 'AUDIT',
    };
  };
  // 审核
  const processingResults = async (data: any, index: any, onClose) => {
    if (index === 0) {
      const res = await supplierfilemanage({ id: data?.id, state: 'PASS' });
      res?.code === 0 && message.success('操作成功');
      onClose();
      return;
    }
    XlbTipsModal({
      tips: (
        <XlbProForm
          formRef={formRef}
          formList={[
            {
              label: '审核拒绝说明',
              id: 'commoneTextArea',
              name: 'memo',
              fieldProps: { autoSize: { minRows: 2, maxRows: 4 }, width: 126 },
              rules: [{ required: true, message: '请填写拒绝原因' }],
              render(formValues: any) {
                return formValues?.memo || '';
              },
            },
          ]}
        />
      ),
      title: '审核',
      width: 350,
      isFullScreen: true,
      isCancel: true,
      onOkBeforeFunction: async () => {
        // const v = await formRef.current.validateFields();
        // if (!v) {
        //   return;
        // }
        try {
          await formRef.current.validateFields();
        } catch (error) {
          return false;
        }
        const res = await supplierfilemanage({
          ...formRef.current.getFieldsValue(true),
          id: data?.id,
          state: 'DENY',
        });
        if (res?.code == 0) {
          message.success('操作成功');
          onClose();
          return true;
        }
        // }
      },
    });
  };
  return (
    <div className={styles.cardContent}>
      <XlbProPageContainer
        details={{
          itemSpan: 12,
          primaryKey: 'id',
          readOnly: (formValues) => {
            return (
              formValues?.look_state === 'PASS' || formValues?.state === 'AUDIT'
            );
          },
          queryFieldProps: {
            url: '/scm/hxl.scm.supplierfilemanage.read',
          },
          updateFieldProps: {
            url: '/scm/hxl.scm.supplierfilemanage.submit',
          },
          saveFieldProps: {
            name: '提交',
            url: '/scm/hxl.scm.supplierfilemanage.submit',
            hidden(formValues) {
              return (
                formValues?.look_state === 'PASS' ||
                formValues?.state === 'AUDIT'
              );
            },
          },

          formList: [
            {
              componentType: 'tabs',
              fieldProps: {
                items: (formValues: any) => {
                  return [
                    {
                      label: '营业执照',
                      key: 'license',
                      children: [
                        readcheckout('license', formValues) as DetailFormList,
                        {
                          componentType: 'form',
                          fieldProps: {
                            formList: transformData('license'),
                          },
                        },
                      ],
                    },
                    {
                      label: '食品经营许可证',
                      key: 'food_business',
                      children: [
                        readcheckout(
                          'food_business',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          fieldProps: {
                            formList: transformData('food_business'),
                          },
                        },
                      ],
                      forceRender: true,
                    },
                    {
                      label: '食品生产许可证',
                      key: 'food_production',
                      children: [
                        readcheckout(
                          'food_production',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          fieldProps: {
                            formList: transformData('food_production'),
                          },
                        },
                      ],
                      forceRender: true,
                    },
                    {
                      label: '经营生产环境',
                      key: 'business_production',
                      children: [
                        readcheckout(
                          'business_production',
                          formValues,
                        ) as DetailFormList,

                        {
                          componentType: 'form',
                          fieldProps: {
                            formList: [
                              {
                                label: '证件附件',
                                name: ['business_production', 'files'],
                                id: 'commonUpload',
                                fieldProps: {
                                  action:
                                    '/scm/hxl.scm.storefilemanage.file.upload',
                                  buttonModal: true,
                                  accept: ['image'],
                                  mode: 'textButton',
                                  listType: 'picture',
                                  data: {
                                    id: 123,
                                  },
                                },
                                rules: [
                                  {
                                    required: true,
                                    message: '请上传经营生产环境',
                                  },
                                ],
                              },
                            ],
                          },
                        },
                      ],
                      forceRender: true,
                    },
                  ];
                },
              },
            },
          ],
          extra({ values, onClose }: any) {
            onClose = onClose;
            return (
              <>
                {values?.look_state === 'PASS' &&
                values?.state === 'AUDIT' &&
                values?.data_state === 'TEMP' &&
                hasAuth(['供应商证件', '审核']) ? (
                  <XlbDropdownButton
                    trigger={['click']}
                    dropList={[{ label: '审核通过' }, { label: '审核拒绝' }]}
                    dropdownItemClick={(index) => {
                      processingResults(values, index, onClose);
                    }}
                    label={'审核'}
                  ></XlbDropdownButton>
                ) : null}
                {
                  <XlbButton
                    type="primary"
                    onClick={() => {
                      handleCheck(values, 'aduit');
                    }}
                  >
                    审核记录
                  </XlbButton>
                }
              </>
            );
          },
        }}
        searchFieldProps={{
          formList: (formValues) => {
            return [
              {
                label: '供应商',
                name: 'supplier_ids',
                id: ScmFieldKeyMap?.supplierIds,
                fieldProps: {
                  dialogParams: {
                    type: 'supplier',
                    dataType: 'lists',
                    isLeftColumn: true,
                    isMultiple: true,
                    primaryKey: 'id',
                    data: {
                      enabled: true,
                      query_producer:
                        userInfo.user_type === 'SUPPLIER' ? true : undefined,
                    },
                  },
                },
              },
              {
                label: '供应商类型',
                id: 'commonSelect',
                name: 'supplier_types',
                fieldProps: {
                  options: supplierOptions,
                  mode: 'multiple',
                },
              },
              {
                label: '营业执照',
                id: 'commonSelect',
                name: 'license_states',
                fieldProps: {
                  options: UPLOAD_TYPE,
                  mode: 'multiple',
                },
              },
              {
                label: '经营许可证',
                id: 'commonSelect',
                name: 'food_business_states',
                fieldProps: {
                  options: UPLOAD_TYPE,
                  mode: 'multiple',
                },
              },
              {
                label: '生产许可证',
                id: 'commonSelect',
                name: 'food_production_states',
                fieldProps: {
                  options: UPLOAD_TYPE,
                  mode: 'multiple',
                },
              },
              {
                label: '生产品种明细',
                id: 'commonSelect',
                name: 'category_detail_states',
                fieldProps: {
                  options: UPLOAD_STATE,
                  mode: 'multiple',
                },
              },
              {
                label: '经营生产环境',
                id: 'commonSelect',
                name: 'business_production_states',
                fieldProps: {
                  multiple: true,
                  options: UPLOAD_STATE,
                  mode: 'multiple',
                },
              },
              {
                label: '审核状态',
                id: 'commonSelect',
                name: 'states',
                fieldProps: {
                  multiple: true,
                  options: states,
                  mode: 'multiple',
                },
              },
            ];
          },
          initialValues: {
            store_ids: record ? record.store_ids : [],
          },
        }}
        exportFieldProps={{
          url: hasAuth(['供应商证件', '导出'])
            ? '/scm/hxl.scm.storefilemanage.export'
            : undefined,
          fileName: '供应商证件管理',
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.supplierfilemanage.page',
          tableColumn: tableList,
          immediatePost: true,
          primaryKey: 'fid',
          showColumnsSetting: false,
        }}
      />
    </div>
  );
};

export default Index;
