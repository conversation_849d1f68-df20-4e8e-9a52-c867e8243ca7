import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbBaseUpload, XlbTableColumnProps } from '@xlb/components';
import styles from './index.less';
const userInfo = LStorage.get('userInfo');
export const UPLOAD_STATE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '已上传', value: 'UPLOADED' },
];
export const UPLOAD_TYPE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '已上传', value: 'UPLOADED' },
  { label: '临期', value: 'TEMPORARY' },
  { label: '过期', value: 'EXPIRE' },
];
export const supplierOptions = [
  { value: 'TRADER', label: '贸易商' },
  { value: 'PRODUCER', label: '生产商' },
];
export const MISS_ITEM_STATE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '部分上传', value: 'PARTUPLOAD' },
  { label: '全部上传', value: 'ALLUPLOAD' },
];
export const FEE_LICENSE_TYPE = [
  { label: '审核中', value: 'AUDIT' },
  { label: '企业', value: 'COMPANY' },
];

export const baseList = [
  {
    label: '证件附件',
    name: ['business_licence', 'files'],
    id: 'commonUpload',
    rules: [{ required: true, message: '请上传附件' }],
    fieldProps: {
      action: '/scm/hxl.scm.storefilemanage.file.upload',
      buttonModal: true,
      accept: ['image'],
      mode: 'textButton',
      maxCount: 1,
      listType: 'picture',
      data: { id: '1' },
    },
  },
  {
    label: '名称',
    name: ['business_licence', 'name'],
    id: 'commonInput',
    rules: [{ required: true, message: '请输入名称' }],
  },
  {
    label: '社会信用代码',
    name: ['business_licence', 'credit_code'],
    id: 'commonInput',
    rules: [{ required: true, message: '请输入社会信用代码' }],
  },
  {
    label: '法人',
    name: ['business_licence', 'legal_person'],
    id: 'commonInput',
    rules: [{ required: true, message: '请输入法人' }],
  },
  {
    label: '有效期始',
    name: ['business_licence', 'start_date'],
    id: ScmFieldKeyMap.scmDate,
    rules: [{ required: true, message: '请输入有效期始' }],
  },
  {
    label: '有效期止',
    name: ['business_licence', 'end_date'],
    id: ScmFieldKeyMap.scmDate,
    dependencies: [['business_licence', 'start_date']],
  },
];
export const itemfiles = {
  label: '品种明细',
  name: ['health_licence', 'category_files'],
  id: 'commonUpload',
  rules: [{ required: true, message: '请上传品种附件' }],
  fieldProps: {
    action: '/scm/hxl.scm.storefilemanage.file.upload',
    buttonModal: true,
    accept: ['image'],
    mode: 'textButton',
    data: { id: 1 },
    // maxCount: 1,
  },
};
export const itemlook = {
  name: '品种明细',
  code: 'category_files',
  render: (text: any) => {
    return (
      <div
        onClick={(e) => {
          e?.stopPropagation();
        }}
      >
        <XlbBaseUpload
          className="danger"
          mode="look"
          hiddenControlerIcon={true}
          showUpload={false}
          listType={'text'}
          fileList={text}
        />
      </div>
    );
  },
};
// 查看归档
export const tableBaseList = [
  { name: '序号', code: '_index' },
  { name: '名称', code: 'name', width: 100 },
  { name: '社会信用代码', code: 'credit_code', width: 120 },
  { name: '法人', code: 'legal_person' },
  { name: '有效期始', code: 'start_date', width: 150 },
  { name: '有效期止', code: 'end_date', width: 150 },
  {
    name: '证件',
    code: 'files',
    render: (text: any) => {
      return (
        <div
          onClick={(e) => {
            e?.stopPropagation();
          }}
        >
          <XlbBaseUpload
            className="danger"
            mode="look"
            hiddenControlerIcon={true}
            showUpload={false}
            listType={'text'}
            fileList={text}
          />
        </div>
      );
    },
  },
  { name: '归档时间', code: 'archive_date', width: 160 },
];
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    align: 'center',
    lock: true,
  },
  {
    name: '供应商',
    // code: 'supplier_name',
    code: 'supplier_name',
    width: 160,
    features: { sortable: true, details: true },
    lock: true,
    render(text, record, index) {
      return (
        <div
          onClick={() => {
            record.data_state = 'PASS';
            record.look_state = 'PASS';
          }}
        >
          {text}
        </div>
      );
    },
  },
  {
    name: '供应商类型',
    code: 'supplier_type',
    width: 190,
    features: { sortable: true },
    lock: true,
    render(text, record, index) {
      return supplierOptions?.find((item) => item.value === text)?.label;
    },
  },
  {
    name: '营业执照',
    code: 'license_state',
    width: 130,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '食品经营许可证',
    code: 'food_business_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '食品生产许可证',
    code: 'food_production_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '生产品种明细',
    code: 'category_detail_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_STATE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '经营生产环境',
    code: 'business_production_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_STATE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '变更申请',
    code: '123',
    width: 100,
    features: { details: true },
    render(text, record, index) {
      return (
        <>
          {userInfo.user_type === 'SUPPLIER' ? (
            <div
              onClick={() => {
                record.data_state = 'TEMP';
                record.look_state = 'TEMP';
              }}
            >
              更新
            </div>
          ) : (
            <div className={styles.flex}>
              <div
                onClick={() => {
                  record.data_state = 'PASS';
                  record.look_state = 'PASS';
                }}
                style={{ flex: '1' }}
              >
                查看
              </div>
              {record?.state === 'AUDIT' && hasAuth(['供应商证件', '审核']) && (
                <div
                  style={{ flex: '1' }}
                  onClick={() => {
                    record.data_state = 'TEMP';
                    record.look_state = 'PASS';
                  }}
                >
                  审核
                </div>
              )}
            </div>
          )}
        </>
      );
    },
  },
  {
    name: '审核状态',
    code: 'state',
    width: 100,
    features: {},
    render: (text) => {
      return states.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '最近更新人',
    code: 'update_by',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '最近更新时间',
    code: 'update_time',
    width: 120,
    features: { sortable: true },
  },
];
export const aduitlist: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    align: 'center',
    lock: true,
  },
  {
    name: '申请人',
    code: 'apply_by',
    width: 100,
    features: {},
  },
  {
    name: '申请时间',
    code: 'apply_time',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
    features: {},
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核状态',
    code: 'state',
    width: 120,
    features: {},
    render(text, record, index) {
      return states?.find((item) => item.value === text)?.label;
    },
  },
  {
    name: '批注',
    code: 'memo',
    width: 120,
    features: {},
  },
];
export let states = [
  { label: '待审核', value: 'AUDIT' },
  { label: '审核通过', value: 'PASS' },
  { label: '审核拒绝', value: 'DENY' },
];
