import { XlbFetch } from '@xlb/utils';
export const BASE_URL = process.env.BASE_URL;
// 审核
export const supplierfilemanage = async (data: any) => {
  return await XlbFetch.post(
    `${BASE_URL}/scm/hxl.scm.supplierfilemanage.audit`,
    data,
  );
};
// 保存
export const save = async (data: any) => {
  return await XlbFetch.post(
    `${BASE_URL}/scm/hxl.scm.supplierfilemanage.save`,
    data,
  );
};

// 保存
export const ocr = async (data: any) => {
  return await XlbFetch.post(`${BASE_URL}/erp/hxl.erp.file.ocr`, data);
};

// 保存
export const savebusinessproduction = async (data: any) => {
  return await XlbFetch.post(
    `${BASE_URL}/scm/hxl.scm.supplierfilemanage.savebusinessproduction`,
    data,
  );
};
