import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import { FormInstance } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { type FC } from 'react';
import {
  allTableList,
  storeTableList,
  summartOptions,
  supplierTableList,
  TableEnumBySummary,
  tableList,
} from './data';

enum TabOptions {
  Item = 'item',
  Store = 'store',
  Supplier = 'supplier',
  All = 'all',
}

const getFilterList = (tableColumn, formValues) => {
  const list = cloneDeep(tableColumn);
  console.log(formValues.summary_type, '====');

  return list.map((i) => {
    const obj = TableEnumBySummary.find((j) => j.label === i.code);
    if (!obj || !formValues?.summary_type) return i;
    return {
      ...i,
      hidden: formValues?.summary_type?.indexOf(obj.value) === -1,
    };
  });
};

//政府抽检
const ProForm: FC<{ title: string }> = () => {
  return (
    <div style={{ height: 'calc(100vh - 80px)' }}>
      <XlbProPageContainer // 查询
        searchFieldProps={{
          formList: [
            {
              id: 'dateCommon',
              format: 'YYYY-MM-DD',
              name: 'dates',
              label: '日期选择',
            },
            {
              label: '汇总条件',
              id: ScmFieldKeyMap.spotCheckOrderDetailSum,
              name: 'summary_type',
              dependencies: ['tabs'],
              hidden: (formValues: any) => {
                return formValues.tabs !== TabOptions.All;
              },
              fieldProps: {
                options: summartOptions,
                mode: 'multiple',
              },
            },
            {
              id: ScmFieldKeyMap?.scmStoreIds,
              label: '执行门店',
              dependencies: ['tabs', 'summary_type'],
              hidden: (formValues: any) => {
                return (
                  formValues.tabs === TabOptions.Supplier ||
                  (formValues.tabs === TabOptions.All &&
                    !formValues.summary_type?.includes('STORE'))
                );
              },
            },
            {
              id: ScmFieldKeyMap.supplierIds,
              dependencies: ['tabs', 'summary_type'],
              hidden: (formValues: any) => {
                return (
                  formValues.tabs === TabOptions.Store ||
                  (formValues.tabs === TabOptions.All &&
                    !formValues.summary_type?.includes('SUPPLIER'))
                );
              },
            },
            {
              id: ScmFieldKeyMap.scmItemIds,
              dependencies: ['tabs', 'summary_type'],
              hidden: (formValues: any) => {
                return (
                  formValues.tabs === TabOptions.Item ||
                  (formValues.tabs === TabOptions.All &&
                    !formValues.summary_type?.includes('ITEM'))
                );
              },
            },
            {
              id: ScmFieldKeyMap.EXCEPTIONCATEGORY_PARENT,
              label: '异常类别',
              dependencies: ['tabs', 'summary_type'],
              hidden: (formValues: any) => {
                return (
                  formValues.tabs === TabOptions.Store ||
                  formValues.tabs === TabOptions.Supplier ||
                  (formValues.tabs === TabOptions.All &&
                    !formValues.summary_type?.includes('ABNORMAL'))
                );
              },
            },
            {
              id: ScmFieldKeyMap.EXCEPTIONCATEGORY,
              label: '异常二级',
              dependencies: ['tabs', 'summary_type', 'abnormal_one_level_id'],
              hidden: (formValues: any) => {
                return (
                  formValues.tabs === TabOptions.Store ||
                  formValues.tabs === TabOptions.Supplier ||
                  (formValues.tabs === TabOptions.All &&
                    !formValues.summary_type?.includes('ABNORMAL_Level_TWO'))
                );
              },
            },
            {
              id: ScmFieldKeyMap.scmDate,
              name: 'producing_date',
              label: '产品批号',
              dependencies: ['tabs', 'summary_type'],
              hidden: (formValues: any) => {
                return (
                  formValues.tabs !== TabOptions.All ||
                  (formValues.tabs === TabOptions.All &&
                    !formValues.summary_type?.includes('PRODUCING_DATE'))
                );
              },
            },
          ],
          initialValues: {
            dates: [
              dayjs().startOf('d').format('YYYY-MM-DD'),
              dayjs().endOf('d').format('YYYY-MM-DD'),
            ],
            tabs: TabOptions.Item,
            summary_type: ['ITEM'],
          },
        }}
        tabFieldProps={{
          name: 'tabs',
          items: [
            { label: '商品汇总', key: TabOptions.Item },
            { label: '门店汇总', key: TabOptions.Store },
            { label: '供应商汇总', key: TabOptions.Supplier },
            { label: '门店-商品-供应商汇总', key: TabOptions.All },
          ],
          onChange: (tabKey: any, form: FormInstance) => {
            const resetArr = [
              'item_ids',
              'abnormal_one_level_id',
              'abnormal_two_level_id',
            ];

            switch (tabKey) {
              case TabOptions.Store:
                return form.resetFields(['supplier_ids', ...resetArr]);
              case TabOptions.Supplier:
                return form.resetFields(['store_ids', ...resetArr]);
              case TabOptions.All:
                return form.resetFields([
                  'store_ids',
                  'supplier_ids',
                  ...resetArr,
                ]);
            }
          },
        }}
        tableFieldProps={{
          url: (formValues: any) => {
            if (!hasAuth(['抽检单合格率', '查询'])) return '';
            switch (formValues['tabs']) {
              case TabOptions.Item:
                return '/wms/hxl.wms.supplier.spotcheckorderdetail.page';
              case TabOptions.Store:
                return '/wms/hxl.wms.supplier.spotcheckorderdetail.storepage';
              case TabOptions.Supplier:
                return '/wms/hxl.wms.supplier.spotcheckorderdetail.supplierPage';
              case TabOptions.All:
                return '/wms/hxl.wms.supplier.spotcheckorderdetail.itemstoresupplierPage';
            }

            return '';
          },
          tableColumn: (formValues: any) => {
            let filterTable = [];
            switch (formValues['tabs']) {
              case TabOptions.Item:
                return tableList;
              case TabOptions.Store:
                return storeTableList;
              case TabOptions.Supplier:
                return supplierTableList;
              case TabOptions.All:
                filterTable = getFilterList(allTableList, formValues);
                return filterTable;
            }

            return tableList;
          },
          selectMode: 'single',
          immediatePost: false,
          showColumnsSetting: false,
          footerDataSource(data: any) {
            return {
              _index: '合计',
              number_of_failures: data?.number_of_failures || 0,
              pass_rate: data?.pass_rate || 0,
              sampling_frequency: data?.sampling_frequency || 0,
            };
          },
        }}
        exportFieldProps={{
          url: (formValues: any) => {
            if (!hasAuth(['抽检单合格率', '导出'])) return '';
            switch (formValues['tabs']) {
              case TabOptions.Item:
                return '/wms/hxl.wms.supplier.spotcheckorderdetail.export';
              case TabOptions.Store:
                return '/wms/hxl.wms.supplier.spotcheckorderstoredetail.export';
              case TabOptions.Supplier:
                return '/wms/hxl.wms.supplier.spotcheckordersupplierdetail.export';
              case TabOptions.All:
                return '/wms/hxl.wms.itemstoresupplier.spotcheckordersupplierdetail.export';
            }

            return '/wms/hxl.wms.supplier.spotcheckorderdetail.export';
          },
          fileName: '抽检单合格率',
        }}
      />
    </div>
  );
};

export default ProForm;
