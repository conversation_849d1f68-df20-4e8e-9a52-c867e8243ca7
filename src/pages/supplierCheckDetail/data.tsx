import { XlbTableColumnProps } from '@xlb/components';

export const summartOptions = [
  {
    label: '商品',
    value: 'ITEM',
  },
  {
    label: '门店',
    value: 'STORE',
  },
  {
    label: '供应商',
    value: 'SUPPLIER',
  },
  {
    label: '异常类别',
    value: 'ABNORMAL',
  },
  {
    label: '异常二级',
    value: 'ABNORMAL_Level_TWO',
  },
  {
    label: '产品批号',
    value: 'PRODUCING_DATE',
  },
];

// 根据汇总条件
export const TableEnumBySummary = [
  { label: 'store_id', value: 'STORE' },
  { label: 'item_code', value: 'ITEM' },
  { label: 'item_bar_code', value: 'ITEM' },
  { label: 'item_name', value: 'ITEM' },
  { label: 'code', value: 'SUPPLIER' },
  { label: 'abnormal_one_level_id', value: 'ABNORMAL' },
  { label: 'abnormal_two_level_id', value: 'ABNORMAL_Level_TWO' },
  { label: 'producing_date', value: 'PRODUCING_DATE' },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },

  {
    name: '商品代码',
    code: 'item_code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '申请数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '抽检次数',
    code: 'sampling_frequency',
    width: 100,
    features: {},
  },
  {
    name: '不合格次数',
    code: 'number_of_failures',
    width: 100,
    features: {},
  },
  {
    name: '合格率',
    code: 'pass_rate',
    width: 100,
    features: {},
  },

  {
    name: '产品批号',
    code: 'producing_date',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '异常类别',
    code: 'abnormal_one_level_id',
    width: 175,
    features: { sortable: true },
    render(text, record, index) {
      return record.abnormal_one_level_name;
    },
  },
  {
    name: '异常二级',
    code: 'abnormal_two_level_id',
    width: 175,
    features: { sortable: true },
    render(text, record, index) {
      return record.abnormal_two_level_name;
    },
  },
  {
    name: '不合格原因',
    code: 'reason',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '关联抽检单号',
    code: 'fid',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '抽检时间',
    code: 'audit_time',
    width: 175,
    features: { sortable: true },
  },
];

export const storeTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '抽检次数',
    code: 'sampling_frequency',
    width: 100,
    features: {},
  },
  {
    name: '不合格次数',
    code: 'number_of_failures',
    width: 100,
    features: {},
  },
  {
    name: '合格率',
    code: 'pass_rate',
    width: 100,
    features: {},
  },
];

export const supplierTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '抽检次数',
    code: 'sampling_frequency',
    width: 100,
    features: {},
  },
  {
    name: '不合格次数',
    code: 'number_of_failures',
    width: 100,
    features: {},
  },
  {
    name: '合格率',
    code: 'pass_rate',
    width: 100,
    features: {},
  },
];

export const allTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '执行门店',
    code: 'store_id',
    width: 175,
    hidden: true,
    features: { sortable: true },
    render(text, record, index) {
      return record.store_name;
    },
  },
  {
    name: '供应商',
    code: 'code',
    width: 175,
    hidden: true,
    features: { sortable: true },
    render(text, record, index) {
      return record.supplier_name;
    },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '异常类别',
    code: 'abnormal_one_level_id',
    width: 175,
    hidden: true,
    features: { sortable: true },
    render(text, record, index) {
      return record.abnormal_one_level_name;
    },
  },
  {
    name: '异常二级',
    code: 'abnormal_two_level_id',
    width: 175,
    hidden: true,
    features: { sortable: true },
    render(text, record, index) {
      return record.abnormal_two_level_name;
    },
  },
  {
    name: '产品批号',
    hidden: true,
    code: 'producing_date',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '抽检次数',
    code: 'sampling_frequency',
    width: 100,
    features: {},
  },
  {
    name: '不合格次数',
    code: 'number_of_failures',
    width: 100,
    features: {},
  },
  {
    name: '合格率',
    code: 'pass_rate',
    width: 100,
    features: {},
  },
];
