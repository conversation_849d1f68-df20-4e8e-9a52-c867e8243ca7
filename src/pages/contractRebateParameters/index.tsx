import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProDetail } from '@xlb/components';
import type { FC } from 'react';
import styles from './index.less';
const Index: FC = () => {
  let action: any;

  return (
    <div className={styles.paramsContent}>
      <XlbProDetail
        initialValues={{
          rebate_tip: true,
        }}
        queryFieldProps={{
          url: hasAuth(['合同与返利参数', '查询'])
            ? '/scm/hxl.scm.suppliercontractparam.read'
            : '',
          params: (row: any) => {
            return {
              id: 1,
            };
          },
          afterPost(res) {
            return {
              ...res,
              alone_fee_organizations: res?.alone_fee_organizations ?? [],
            };
          },
        }}
        formList={[
          {
            componentType: 'blueBar',
            fieldProps: {
              title: '期限设置',
            },
            children(formValues, readOnly, actions) {
              action = actions;
              return [
                {
                  componentType: 'form',
                  fieldProps: {
                    itemSpan: 24,
                    formList: [
                      {
                        id: ScmFieldKeyMap?.contractExpirationReminder,
                      },
                      {
                        itemSpan: 12,
                        id: ScmFieldKeyMap.aloneFeeOrganizations,
                        label: '合同不合并生成费用单',
                        name: 'alone_fee_organizations',
                      },
                    ],
                  },
                },
              ];
            },
          },
        ]}
        saveFieldProps={{
          url: hasAuth(['合同与返利参数', '编辑'])
            ? '/scm/hxl.scm.suppliercontractparam.save'
            : '',
        }}
        updateFieldProps={{
          url: hasAuth(['合同与返利参数', '编辑'])
            ? '/scm/hxl.scm.suppliercontractparam.save'
            : '',
          afterPost(res) {
            action?.refresh();
          },
        }}
      />
    </div>
  );
};
export default Index;
