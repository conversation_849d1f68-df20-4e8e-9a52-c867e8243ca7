import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { baseList, formList, tableList } from './data';
import styles from './index.less';
const Index: React.FC = () => {
  const pageRef = useRef<any>(null);
  const userInfo = LStorage.get('userInfo');

  return (
    <div className={styles.cardContent}>
      <XlbProPageContainer
        ref={pageRef}
        details={{
          itemSpan: 6,
          primaryKey: 'fid',
          readOnly: () => {
            return true;
          },

          queryFieldProps: {
            url: '/scm/hxl.scm.returnapplicationorder.read',
            params: (obj: any) => {
              return {
                fid: obj?.fid,
              };
            },
            afterPost: (data) => {
              const bool = hasAuth(['采购退货/价格', '查询']);
              if (data?.details && !bool) {
                data?.details.forEach((_) => {
                  _.price = '****';
                  _.money = '****';
                  _.actual_money = '****';
                });
              }
              return {
                ...data,
                data: data,
              };
            },
          },
          formList: [
            {
              componentType: 'blueBar',
              fieldProps: {
                title: '基本信息',
              },
              children: [
                {
                  componentType: 'form',
                  name: 'data',
                  fieldProps: {
                    formList: baseList,
                  },
                },
                {
                  componentType: 'table',
                  name: 'details',
                  fieldProps: {
                    columns: [
                      {
                        code: '_index',
                        name: '序号',
                        width: 100,

                        align: 'center',
                      },
                      {
                        code: 'item_code',
                        name: '商品代码',
                        width: 100,
                      },
                      {
                        code: 'item_bar_code',
                        name: '商品条码',
                        width: 100,
                      },
                      {
                        code: 'item_name',
                        name: '商品名称',
                        width: 100,
                      },
                      {
                        code: 'item_spec',
                        name: '采购规格',
                        width: 100,
                      },
                      {
                        code: 'unit',
                        name: '单位',
                        width: 100,
                      },
                      {
                        code: 'price',
                        name: '单价',
                        width: 100,
                      },
                      {
                        code: 'quantity',
                        name: '退货数量',
                        width: 100,
                      },
                      {
                        code: 'money',
                        name: '退货金额',
                        width: 100,
                      },
                      {
                        code: 'actual_quantity',
                        name: '实际退货数量',
                        width: 140,
                      },
                      // {
                      //   code: 'actual_money',
                      //   name: '实际退货金额',
                      //   width: 140,
                      // },
                      {
                        code: 'present_unit',
                        name: '赠品单位',
                        width: 100,
                      },
                      {
                        code: 'present_quantity',
                        name: '赠品退货数量',
                        width: 140,
                      },
                      {
                        code: 'actual_present_quantity',
                        name: '赠品实际退货数量',
                        width: 180,
                      },
                    ],
                  },
                },
              ],
            },
          ],
          extra({ values, onClose, fetchData, form }: any) {
            return <></>;
          },
        }}
        searchFieldProps={{
          formList: formList,
          initialValues: {
            create_date: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().endOf('day').format('YYYY-MM-DD'),
            ],
            supplier_ids: userInfo?.supplier?.id
              ? [userInfo?.supplier?.id]
              : undefined,
          },
        }}
        exportFieldProps={{
          url: hasAuth(['采购退货', '导出'])
            ? '/scm/hxl.scm.returnapplicationorder.export'
            : undefined,
          fileName: '供应商证件管理',
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.returnapplicationorder.page',
          tableColumn: tableList,
          footerDataSource: (data) => {
            const bool = hasAuth(['采购退货/价格', '查询']);
            const obj: any = {
              // actual_money: data?.total_actual_money,
              item_count: data?.total_item_count,
              money: data?.total_money,
              quantity: data?.total_quantity,
            };
            if (!bool) {
              Object.keys(obj).forEach((_) => {
                obj[_] = '****';
              });
            }
            return [
              {
                _index: '合计',
                ...obj,
              },
            ];
          },
          immediatePost: true,
          primaryKey: 'fid',
          showColumnsSetting: false,
          afterPost: (data) => {
            console.log(data);
            const bool = hasAuth(['采购退货/价格', '查询']);
            const obj: any = {
              // actual_money: '****',
              // item_count: '****',
              money: '****',
              // quantity: '****',
            };
            data?.content?.forEach((element: any) => {
              if (!bool) {
                Object.assign(element, obj);
              }
            });
            return data;
          },
          prevPost: (pagin) => {
            const company_id = userInfo.company_id;
            const formData =
              pageRef.current.formRef.current.getFieldsValue(true);
            const { create_date, ...rest } = formData;
            const [start_time, end_time] = create_date || [];
            return { ...pagin, ...rest, start_time, end_time, company_id };
          },
        }}
      />
    </div>
  );
};

export default Index;
