import { ScmFieldKeyMap } from '@/constants/config/scm';
import { LStorage } from '@/utils/storage';
import { XlbTableColumnProps } from '@xlb/components';
import { FormInstance } from 'antd';
import dayjs from 'dayjs';
const userInfo = LStorage.get('userInfo');
const ownerObj: any = {};
if (userInfo?.supplier?.id) {
  ownerObj.owner_type = 'ORGANIZATION';
} else {
  ownerObj.query_producer =
    userInfo.user_type === 'SUPPLIER' ? true : undefined;
}

export const UPLOAD_STATE = [
  { label: '未退货', value: 'UNRETURNED' },
  { label: '全部退货', value: 'RETURNED' },
];
export const UPLOAD_TYPE = [
  { label: '未退货', value: 'UNRETURNED' },
  { label: '全部退货', value: 'RETURNED' },
];
export const supplierOptions = [
  { value: 'TRADER', label: '贸易商' },
  { value: 'PRODUCER', label: '生产商' },
];
export const MISS_ITEM_STATE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '部分上传', value: 'PARTUPLOAD' },
  { label: '全部上传', value: 'ALLUPLOAD' },
];
export const FEE_LICENSE_TYPE = [
  { label: '审核中', value: 'AUDIT' },
  { label: '企业', value: 'COMPANY' },
];

export const ocrType = {
  license: 'BUSINESS_LICENSE',
  food_production: 'FOOD_PRODUCTION_LICENSE',
  food_business: 'FOOD_BUSINESS_LICENSE',
};
export const ocrname = {
  license: 'business_license',
  food_production: 'food_production_license',
  food_business: 'food_business_license',
};

export const baseList = [
  {
    label: '退货门店',
    name: 'store_name',
    value: 'store_name',
    id: 'commonInput',
    code: 'store_name',
  },
  {
    label: '退货仓库',
    name: 'storehouse_name',
    id: 'commonInput',
  },
  {
    label: '退货货主',
    name: 'cargo_owner_name',
    id: 'commonInput',
  },
  {
    label: '供应商',
    name: 'supplier_name',
    id: 'commonInput',
  },
  {
    label: '留言',
    name: 'memo',
    id: 'commonInput',
  },
];

export const itemEndDate = {
  label: '有效期止',
  name: 'end_date',
  id: ScmFieldKeyMap.scmDate,
  dependencies: ['start_date'],
  rules: [
    {
      required: true,
      message: '请输入有效期止',
    },
    ({ getFieldValue }: any) => ({
      validator: (_: any, value: any) => {
        const start = getFieldValue('start_date');
        if (!!value && start && dayjs(start).isAfter(value)) {
          return Promise.reject('有效期始不得大于有效期止');
        }
        return Promise.resolve();
      },
    }),
  ],
};

export const filesList = [];
// 查看归档
export const tableBaseList = [];
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    // code: 'supplier_name',
    code: 'fid',
    width: 190,
    features: { sortable: true, details: true },
    lock: true,
  },

  {
    name: '组织',
    code: 'org_name',
    width: 120,
    lock: true,
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 190,
  },
  {
    name: '退货门店',
    code: 'store_name',
    width: 130,
  },
  {
    name: '退货仓库',
    code: 'storehouse_name',
    width: 144,
  },
  {
    name: '所属货主',
    code: 'cargo_owner_name',
    width: 144,
  },
  {
    name: '退货状态',
    code: 'return_state',
    width: 144,

    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '最近预约时间',
    code: 'closest_appoint_time',
    width: 144,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '单据金额',
    code: 'money',
    width: 144,
  },
  // {
  //   name: '实际退货金额',
  //   code: 'actual_money',
  //   width: 144,
  // },
  {
    name: '数量',
    code: 'quantity',
    width: 94,
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 100,
  },
  {
    name: '备注留言',
    code: 'memo',
    width: 140,
    features: {},
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 120,
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 120,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 120,
    features: { sortable: true, format: 'TIME' },
  },
];
export const aduitlist: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    align: 'center',
    lock: true,
  },
  {
    name: '申请人',
    code: 'apply_by',
    width: 100,
    features: {},
  },
  {
    name: '申请时间',
    code: 'apply_time',
    width: 120,
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
    features: {},
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 120,
  },
  {
    name: '审核状态',
    code: 'state',
    width: 120,
    features: {},
    render(text, record, index) {
      return states?.find((item) => item.value === text)?.label;
    },
  },
  {
    name: '批注',
    code: 'memo',
    width: 120,
    features: {},
  },
];
export let states = [
  { label: '待审核', value: 'AUDIT' },
  { label: '审核通过', value: 'PASS' },
  { label: '审核拒绝', value: 'DENY' },
];

export const OPERATION_TYPE = {
  license: 'LICENSE',
  food_production: 'FOODPRODUCTION',
  food_business: 'FOODBUSINESS',
  business_production: 'BUSINESSPRODUCTION',
  quality_certificate: 'QUALITYCERTIFICATE',
  factory_gate: 'FACTORYGATE',
  factory_place: 'FACTORYPLACE',
  laboratory: 'LABORATORY',
};

export const formList = [
  {
    id: 'dateCommon',
    format: 'YYYY-MM-DD',
    name: 'create_date',
    label: '日期选择',
  },
  {
    id: ScmFieldKeyMap?.scmOrgIds,
    // id: 'commonSelect',
    label: '所属组织',
    name: 'org_ids',
    fieldProps: {
      mode: 'multiple',
    },
    disabled: () => userInfo?.supplier?.id,
  },

  {
    id: ScmFieldKeyMap?.scmStoreIds,
    label: '退货门店',
    name: 'store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {
          status: true,
          wait_assign: undefined,
          storeStatus: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    onChange: async (e: any, form: FormInstance) => {
      console.log(e, form, 23456789876543);
      form.setFieldsValue({
        storehouse_id: undefined,
      });
    },
  },
  {
    id: 'scmStorehouseId',
    label: '退货仓库',
    name: 'storehouse_id',
    dependencies: ['store_ids'],

    fieldProps: {
      options: [],
      mode: 'single',
    },
    linkId: 'storehouse_option',
  },
  {
    id: ScmFieldKeyMap.cargoOwner,
    label: '所属货主',
    name: 'cargo_owner_ids',
    fieldProps: {
      dialogParams: {
        url: userInfo?.supplier?.id
          ? '/erp/hxl.erp.cargo.owner.pageforinner'
          : '/erp/hxl.erp.cargo.owner.page',
        type: 'cargoOwner',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
          // ...ownerObj,
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'source_name',
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'source_name',
      },
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    id: ScmFieldKeyMap?.supplierIds,
    disabled: () => userInfo?.supplier?.id,

    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        initialValues: userInfo?.supplier?.id
          ? [userInfo?.supplier?.id]
          : undefined,
        data: {
          enabled: true,
          query_producer: userInfo.user_type === 'SUPPLIER' ? true : undefined,
        },
      },
    },
  },
  //
  {
    label: '商品档案',
    id: ScmFieldKeyMap.scmItemIds,
    name: 'item_ids',
    hidden: () => userInfo?.supplier?.id,

    fieldProps: {
      dialogParams: {
        type: 'goods',
        url: '/erp/hxl.erp.item.supplier.page',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          enabled: true,
        },
      },
    },
  },
  {
    label: '退货状态',
    id: 'commonSelect',
    name: 'return_state',
    fieldProps: {
      options: UPLOAD_STATE,
      mode: 'single',
    },
  },
  // {
  //   label: '经营生产环境',
  //   id: 'commonSelect',
  //   name: 'business_production_states',
  //   fieldProps: {
  //     multiple: true,
  //     options: UPLOAD_STATE,
  //     mode: 'multiple',
  //   },
  // },
];

export const QUALITY_TYPE = [];
export const qualityList = [];
