import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import { type FC } from 'react';
import { tableList } from './data';
//检测报告完成率
const ProForm: FC<{ title: string }> = () => {
  return (
    <div style={{ height: 'calc(100vh - 80px)' }}>
      <XlbProPageContainer // 查询
        searchFieldProps={{
          formList: [
            {
              id: 'dateCommon',
              format: 'YYYY-MM-DD',
              name: 'dates',
              label: '日期选择',
            },
            { id: ScmFieldKeyMap?.selectStoreIds, label: '所属门店' },
          ],
          initialValues: {
            dates: [
              dayjs().startOf('d').format('YYYY-MM-DD'),
              dayjs().endOf('d').format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: hasAuth(['检测报告完成率', '查询'])
            ? '/scm/hxl.scm.itemqualityrecord.complete.rate'
            : '',
          tableColumn: tableList,
          selectMode: 'single',
          immediatePost: false,
        }}
        exportFieldProps={{
          url: hasAuth(['检测报告完成率', '导出'])
            ? '/scm/hxl.scm.itemqualityrecord.complete.rate.export'
            : '',
          fileName: '检测报告完成率',
        }}
      />
    </div>
  );
};

export default ProForm;
