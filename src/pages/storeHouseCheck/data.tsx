import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { XlbTableColumnProps } from '@xlb/components';

export const stateType = [
  { label: '合格', value: 'QUALIFIED' },
  { label: '不合格', value: 'UNQUALIFIED' },
];
export const carState = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const ifOpenState = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const Columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: COLUMN_WIDTH_ESUM.INDEX,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 150,
    features: { sortable: true, details: true },
  },
  {
    name: '执行门店',
    code: 'store_id',
    width: 130,
    features: { sortable: true },
    render: (text, record) => {
      return record?.store_name;
    },
  },
  {
    name: '供应商',
    code: 'supplier_id',
    width: 130,
    features: { sortable: true },
    render: (text, record) => {
      return record?.supplier_name;
    },
  },
  {
    name: '商品档案',
    code: 'item_id',
    width: 130,
    features: { sortable: true },
    render: (text, record) => {
      return record?.item_name;
    },
  },
  {
    name: '送货量',
    code: 'delivery_count',
    width: 80,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '抽检数',
    code: 'check_count',
    width: 80,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '免检品抽检数',
    code: 'check_count',
    width: 80,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '不合格数',
    code: 'unqualified_count',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '合格率',
    code: 'qualified_ratio',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '抽检日期',
    code: 'check_date',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '异常类别',
    code: 'abnormal_one_level_id',
    width: 140,
    features: { sortable: true },
    render: (text, record) => {
      return record?.abnormal_one_level_name;
    },
  },
  {
    name: '异常二级',
    code: 'abnormal_two_level_id',
    width: 140,
    features: { sortable: true },
    render: (text, record) => {
      return record?.abnormal_two_level_name;
    },
  },
  {
    name: '抽检状态',
    code: 'state',
    width: COLUMN_WIDTH_ESUM.ORDER_STATE,
    features: { sortable: true },
    render: (text) => {
      return stateType.find((item) => item.value === text)?.label;
    },
  },
  {
    name: '关联入库申请单',
    code: 'in_order_fids',
    width: 130,
    features: { sortable: true },
    render(text, record, index) {
      return record?.in_order_fid_list?.join(',');
    },
  },
  {
    name: '提交人',
    code: 'create_by',
    width: COLUMN_WIDTH_ESUM.BY,
    features: { sortable: true },
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: COLUMN_WIDTH_ESUM.TIME,
    features: { sortable: true },
  },
];
