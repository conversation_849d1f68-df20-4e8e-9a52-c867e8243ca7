import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbProPageContainer, XlbTipsModal } from '@xlb/components';
import dayjs from 'dayjs';
import { FC } from 'react';
import { carState, Columns, ifOpenState, stateType } from './data';
import { itemproductspec } from './server';

const Index: FC = () => {
  // TODO总计
  return (
    <div style={{ height: 'calc(100vh - 80px)' }}>
      <XlbProPageContainer
        searchFieldProps={{
          formList: [
            {
              id: 'dateCommon',
              name: 'check_date',
              fieldProps: {
                allowClear: false,
              },
            },
            { id: ScmFieldKeyMap?.scmStoreIds, label: '执行门店' },
            { id: 'supplierIds', name: 'supplier_ids' },
            { id: 'itemIds', name: 'item_ids' },
            {
              name: 'states',
              label: '抽检状态',
              id: 'commonSelect',
              fieldProps: {
                mode: 'multiple',
                options: stateType,
              },
            },
            {
              label: '车辆是否合格',
              name: 'if_truck_qualified',
              id: 'commonSelect',
              fieldProps: {
                // mode: 'multiple',
                options: carState,
              },
            },
            { id: ScmFieldKeyMap.EXCEPTIONCATEGORY_PARENT, label: '异常类别' },
            { id: ScmFieldKeyMap.EXCEPTIONCATEGORY, label: '异常二级' },
            { id: 'commonInput', name: 'in_order_fids', label: '入库申请单' },
            { id: 'commonInput', name: 'create_by', label: '提交人' },
          ],
          initialValues: {
            check_date: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.storehouseitemspotcheck.page',
          tableColumn: Columns,
          primaryKey: 'fid',
          selectMode: 'multiple',
          immediatePost: false,
          footerDataSource(data: any) {
            return {
              _index: '合计',
              qualified_ratio: data?.qualified_ratio_average || 0,
            };
          },
        }}
        deleteFieldProps={{
          url: hasAuth(['仓库商品抽检', '删除'])
            ? '/scm/hxl.scm.storehouseitemspotcheck.batchdelete'
            : '',
          beforeTips: () => {
            return XlbTipsModal({
              tips: `是否删除所选中记录？`,
              isCancel: true,
              onOk: () => {
                return true;
              },
            });
          },
          params: (selectRowKeys: any) => {
            return {
              fids: selectRowKeys,
            };
          },
          name: '删除',
        }}
        exportFieldProps={{
          url: hasAuth(['仓库商品抽检', '导出'])
            ? '/scm/hxl.scm.storehouseitemspotcheck.export'
            : undefined,
          fileName: '仓库商品抽检',
        }}
        addFieldProps={{
          name: '新增',
          url: hasAuth(['仓库商品抽检', '编辑'])
            ? '/scm/hxl.scm.storehouseitemspotcheck.save'
            : '',
        }}
        details={{
          mode: 'page',
          hiddenSaveBtn: false,
          primaryKey: 'fid',
          hiddenDomValues: true,
          queryFieldProps: {
            url: '/scm/hxl.scm.storehouseitemspotcheck.read',
          },
          saveFieldProps: {
            url: hasAuth(['仓库商品抽检', '编辑'])
              ? '/scm/hxl.scm.storehouseitemspotcheck.save'
              : '',
            hidden: (formValues: any) => !!formValues?.fid,
          },
          initialValues: {
            store_id: LStorage.get('userInfo').store_id,
          },
          formList: [
            {
              componentType: 'blueBar',
              fieldProps: {
                title: '基本信息',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    formList: [
                      {
                        id: ScmFieldKeyMap?.scmStoreId,
                        label: '执行门店',
                        name: 'store_id',
                        rules: [{ required: true, message: '请选择执行门店' }],
                        fieldProps: {
                          dialogParams: {
                            type: 'store',
                            dataType: 'lists',
                            isMultiple: false,
                          },
                        },
                        onChange: (e, form, _) => {
                          if (!_?.length) return;

                          form?.setFieldsValue({
                            store_name: _[0]?.store_name ?? '',
                            store_number: _[0]?.store_number ?? '',
                          });
                        },
                      },
                      {
                        label: '车辆是否合格',
                        name: 'if_truck_qualified',
                        id: 'commonSelect',
                        rules: [{ required: true, message: '请选择是否合格' }],
                        fieldProps: {
                          options: carState,
                        },
                      },
                      {
                        id: ScmFieldKeyMap.scmCommonUpload,
                        name: 'truck_phones',
                        label: '车辆不合格照片',
                        rules: [
                          { required: true, message: '请上传不合格照片' },
                        ],
                        dependencies: ['if_truck_qualified'],
                        hidden(formValues) {
                          return formValues?.if_truck_qualified;
                        },
                        fieldProps: {
                          mode: 'textButton',
                          listType: 'text',
                          action:
                            '/scm/hxl.scm.storehouseitemspotcheck.file.upload',
                          accept: ['image'],
                        },
                      },
                      {
                        label: '供应商',
                        name: 'supplier_id',
                        id: ScmFieldKeyMap?.scmSupplierId,
                        rules: [{ required: true, message: '请选择供应商' }],
                        onChange: (e, form, _) => {
                          if (!_?.length) return;
                          form?.setFieldsValue({
                            supplier_name: _[0]?.name ?? '',
                          });
                        },
                      },
                      {
                        label: '商品',
                        name: 'item_id',
                        id: ScmFieldKeyMap?.scmItemId,
                        rules: [{ required: true, message: '请选择商品' }],
                        fieldProps: {},
                        onChange: async (e, form, _) => {
                          if (!_?.length) return;
                          const res = await itemproductspec({
                            ids: [_?.[0]?.id],
                          });
                          if (res?.code === 0 && res?.data?.[0]?.files) {
                            form?.setFieldsValue({
                              item_Specification_files: res?.data
                                ?.map((item) => item?.files)
                                .flat(Infinity),
                            });
                          }
                          form?.setFieldsValue({
                            item_code: _[0]?.code ?? '',
                            purchase_spec: _[0]?.purchase_spec ?? '',
                          });
                        },
                      },
                      {
                        label: '商品代码',
                        name: 'item_code',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '零售规格',
                        name: 'purchase_spec',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '入库申请单',
                        name: 'in_order_fid_list',
                        id: ScmFieldKeyMap?.scmInOrderFid,
                        itemSpan: 8,
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请选择入库申请单',
                            };
                          },
                        ],
                        onChange: (e, form, list) => {
                          const itemId = form?.getFieldValue('item_id');
                          const temp = list
                            ?.flatMap((item: any) => item?.details)
                            .filter((val: any) => val.item_id === itemId);
                          const itemCount = temp.reduce(
                            (acc: any, cur: any) => acc + cur?.quantity,
                            0,
                          );
                          form?.setFieldsValue({
                            delivery_count: itemCount ?? '',
                            batch_count: list?.length ?? '',
                            item_quality_report_files: temp
                              .filter((item: any) => item.files)
                              .map((item: any) => item.files)
                              .flat(Infinity),
                          });
                        },
                      },
                      {
                        label: '送货量',
                        name: 'delivery_count',
                        id: 'commonInput',
                      },
                      {
                        label: '抽检数',
                        name: 'check_count',
                        id: ScmFieldKeyMap.scmInputcheckcount,
                        disabled: true,
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请输入抽检数',
                            };
                          },
                        ],
                      },
                      {
                        label: '不合格数',
                        name: 'unqualified_count',
                        id: ScmFieldKeyMap.scmInputNumber,
                        fieldProps: {
                          min: 0,
                          controls: false,
                        },
                        rules: [
                          ({ getFieldValue }) => {
                            const checkCount = getFieldValue('check_count');
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              validator: (_, value) => {
                                if (!isQualified) {
                                  return Promise.resolve();
                                }
                                if (!value) {
                                  return Promise.reject('请输入不合格数');
                                }
                                if (value > checkCount) {
                                  return Promise.reject(
                                    '不合格数不能大于抽检数',
                                  );
                                }
                                return Promise.resolve();
                              },
                            };
                          },
                        ],
                      },
                      {
                        label: '免检品抽检数',
                        name: 'exempt_check_num',
                        id: ScmFieldKeyMap.scmexemptcheckInput,
                        disabled: true,
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请输入免检品抽检数',
                            };
                          },
                        ],
                      },
                      {
                        id: ScmFieldKeyMap.scmqualifiedRatio,
                      },
                      {
                        id: 'createTime',
                        label: '生产日期',
                        name: 'producing_dates',
                        fieldProps: {
                          format: 'YYYY-MM-DD',
                          maxTagCount: 'responsive',
                          multiple: true,
                        },
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请选择生产日期',
                            };
                          },
                        ],
                      },
                      {
                        label: '来货批次',
                        name: 'batch_count',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '抽检状态',
                        name: 'state',
                        id: 'commonSelect',
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请选择抽检状态',
                            };
                          },
                        ],
                        fieldProps: {
                          options: stateType,
                        },
                      },
                      {
                        id: ScmFieldKeyMap.scmCommonUpload,
                        name: 'files',
                        label: '不合格照片',
                        rules: [
                          ({ getFieldValue }) => {
                            const state = getFieldValue('state');
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: state !== 'QUALIFIED' && isQualified,
                              validator: (_, value) => {
                                if (!isQualified) {
                                  return Promise.resolve();
                                }
                                if (
                                  state !== 'QUALIFIED' &&
                                  (!value || !value?.length)
                                ) {
                                  return Promise.reject('请上传不合格照片');
                                }
                                return Promise.resolve();
                              },
                            };
                          },
                        ],
                        fieldProps: {
                          mode: 'textButton',
                          listType: 'text',
                          action:
                            '/scm/hxl.scm.storehouseitemspotcheck.file.upload',
                        },
                      },
                      {
                        id: ScmFieldKeyMap.scmCommonUpload,
                        name: 'item_quality_report_files',
                        label: '检测报告',
                        fieldProps: {
                          mode: 'look',
                          listType: 'text',
                          action:
                            '/scm/hxl.scm.storehouseitemspotcheck.file.upload',
                        },
                      },
                      {
                        id: ScmFieldKeyMap.scmCommonUpload,
                        name: 'item_Specification_files',
                        label: '商品规格书',
                        fieldProps: {
                          mode: 'look',
                          listType: 'text',
                          action:
                            '/scm/hxl.scm.storehouseitemspotcheck.file.upload',
                        },
                      },
                      {
                        id: ScmFieldKeyMap.EXCEPTIONCATEGORY_PARENT,
                        label: '异常类别',
                        onChange: (e, form, _) => {
                          form?.setFieldsValue({
                            abnormal_one_level_name: _?.name ?? '',
                            abnormal_two_level_id: undefined,
                          });
                        },
                        rules: [
                          ({ getFieldValue }) => {
                            const state = getFieldValue('state');
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: state !== 'QUALIFIED' && isQualified,
                              validator: (_, value) => {
                                if (!isQualified) {
                                  return Promise.resolve();
                                }
                                if (state !== 'QUALIFIED' && !value) {
                                  return Promise.reject('请选择异常类别');
                                }
                                return Promise.resolve();
                              },
                            };
                          },
                        ],
                      },
                      {
                        id: ScmFieldKeyMap.EXCEPTIONCATEGORY,
                        label: '异常二级',
                        onChange: (e, form, _) => {
                          form?.setFieldsValue({
                            abnormal_two_level_name: _?.name ?? '',
                          });
                        },
                        rules: [
                          ({ getFieldValue }) => {
                            const state = getFieldValue('state');
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: state !== 'QUALIFIED' && isQualified,
                              validator: (_, value) => {
                                if (!isQualified) {
                                  return Promise.resolve();
                                }
                                if (state !== 'QUALIFIED' && !value) {
                                  return Promise.reject('请选择异常二级');
                                }
                                return Promise.resolve();
                              },
                            };
                          },
                        ],
                      },
                      {
                        label: '是否需要拆包检',
                        name: 'if_open_box_check',
                        id: 'commonSelect',
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请选择是否需要拆包检',
                            };
                          },
                        ],
                        fieldProps: {
                          options: ifOpenState,
                        },
                      },
                      {
                        id: 'commoneTextArea',
                        label: '质检结果',
                        name: 'check_result',
                        itemSpan: 24,
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请输入质检结果',
                            };
                          },
                        ],
                        fieldProps: {
                          autoSize: {
                            minRows: 1,
                          },
                        },
                      },
                      {
                        id: 'commoneTextArea',
                        label: '收货建议',
                        name: 'receive_advice',
                        itemSpan: 24,
                        rules: [
                          ({ getFieldValue }) => {
                            const isQualified =
                              getFieldValue('if_truck_qualified');
                            return {
                              required: isQualified ? true : false,
                              message: '请输入收货建议',
                            };
                          },
                        ],
                        fieldProps: {
                          autoSize: {
                            minRows: 1,
                          },
                        },
                      },
                    ],
                  },
                },
              ],
            },
          ],
        }}
      />
    </div>
  );
};
export default Index;
