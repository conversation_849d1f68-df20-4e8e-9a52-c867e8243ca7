import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import { type FC } from 'react';
import { tableList } from '../data';
//政府抽检
const ProForm: FC<{ title: string }> = () => {
  return (
    <div style={{ height: 'calc(100vh - 78px)' }}>
      <XlbProPageContainer // 查询
        searchFieldProps={{
          formList: [
            {
              id: 'dateCommon',
              format: 'YYYY-MM-DD',
              name: 'create_date',
              label: '日期选择',
              fieldProps: {
                allowClear: false,
              },
            },
            'keyword',
            {
              id: 'commonInput',
              name: 'notifier',
              label: '通知人',
            },
          ],
          initialValues: {
            create_date: [
              dayjs().startOf('d').format('YYYY-MM-DD'),
              dayjs().endOf('d').format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: hasAuth(['政府抽检汇总', '查询'])
            ? '/fsms/hxl.fsms.governmentrandomcheckstoredetail.page'
            : '',
          tableColumn: tableList,
          selectMode: 'multiple',
          immediatePost: false,
        }}
        exportFieldProps={{
          url: hasAuth(['政府抽检汇总', '导出'])
            ? '/fsms/hxl.fsms.governmentrandomcheckstoredetail.export'
            : '',
          fileName: '政府抽检汇总',
        }}
      />
    </div>
  );
};

export default ProForm;
