import { XlbBaseUpload, XlbTableColumnProps } from '@xlb/components';

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'bar_code',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '规格',
    code: 'purchase_spec',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 175,
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '问题报告',
    code: 'file',
    width: 175,
    features: { sortable: false },
    render: (text, record) => {
      return (
        <div
          onClick={(e) => {
            e?.stopPropagation();
          }}
        >
          <XlbBaseUpload
            className="danger"
            mode="look"
            hiddenControlerIcon={true}
            showUpload={false}
            listType={'text'}
            fileList={record?.problem_files}
          />
        </div>
      );
    },
  },
  {
    name: '通知人',
    code: 'notifier',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '通知时间',
    code: 'notification_time',
    width: 175,
    features: { sortable: true, format: 'TIME' },
  },
];
