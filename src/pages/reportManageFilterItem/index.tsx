import { hasAuth } from '@/utils/kit';
import {
  XlbBasicData,
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import dayjs from 'dayjs';
import { FC } from 'react';
import { Columns } from './data';

const Index: FC = () => {
  const addItem = async (setLoading: Function, fetchData: Function) => {
    const list = await XlbBasicData({
      dataType: 'lists',
      isMultiple: true,
      type: 'goods',
      url: '/erp/hxl.erp.item.supplier.page',
    });
    if (list?.length) {
      const data = { item_ids: list.map((v) => v.id) };
      setLoading(true);
      const res = await XlbFetch.post(
        `${process.env.BASE_URL}` +
          '/scm/hxl.scm.supplierqualityrecordfilteritem.save',
        {
          ...data,
        },
      );
      setLoading(false);
      //@ts-ignore
      if (res?.code === 0) {
        fetchData();
        message.success('操作成功');
      }
    }
  };
  // TODO总计
  return (
    <XlbProPageContainer
      searchFieldProps={{
        formList: [
          { id: 'dateCommon', name: 'create_date' },
          'itemIds',
          { id: 'commonInput', name: 'keyword', label: '关键字' },
        ],
        initialValues: {
          create_date: [
            dayjs().format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
          ],
        },
      }}
      tableFieldProps={{
        url: '/scm/hxl.scm.supplierqualityrecordfilteritem.page',
        tableColumn: Columns,
        primaryKey: 'item_id',
        selectMode: 'multiple',
        immediatePost: false,
      }}
      deleteFieldProps={{
        url: hasAuth(['外检报告过滤商品', '删除'])
          ? '/scm/hxl.scm.supplierqualityrecordfilteritem.batchdelete'
          : '',
        beforeTips: (selectRowKeys: string[]) => {
          return XlbTipsModal({
            tips: `是否删除所选中商品？`,
            isCancel: true,
            onOk: () => {
              return true;
            },
          });
        },
        name: '删除',
      }}
      exportFieldProps={{
        url: hasAuth(['外检报告过滤商品', '导出'])
          ? '/scm/hxl.scm.supplierqualityrecordfilteritem.export'
          : undefined,
        fileName: '外检报告过滤商品',
      }}
      uploadFieldProps={{
        url: hasAuth(['外检报告过滤商品', '导入'])
          ? '/scm/hxl.scm.supplierqualityrecordfilteritem.import'
          : '',
        templateUrl: '/scm/hxl.scm.supplierqualityrecordfilteritem.download',
      }}
      extra={({ setLoading, fetchData }) => {
        return hasAuth(['外检报告过滤商品', '编辑']) ? (
          <XlbButton
            type="primary"
            label="新增"
            onClick={() => addItem(setLoading as Function, fetchData)}
            icon={<XlbIcon name="jia" />}
          />
        ) : null;
      }}
    />
  );
};
export default Index;
