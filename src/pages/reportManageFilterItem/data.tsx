import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { XlbTableColumnProps } from '@xlb/components';

export const stateType = [
  { label: '合格', value: 'QUALIFIED' },
  { label: '不合格', value: 'UNQUALIFIED' },
];

export const Columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: COLUMN_WIDTH_ESUM.INDEX,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_id',
    width: 130,
    features: { sortable: true },
    render: (text, record) => {
      return record?.name;
    },
  },
  {
    name: '提交人',
    code: 'create_by',
    width: COLUMN_WIDTH_ESUM.BY,
    features: { sortable: true },
  },
  {
    name: '提交时间',
    code: 'create_time',
    width: COLUMN_WIDTH_ESUM.TIME,
    features: { sortable: true },
  },
];
