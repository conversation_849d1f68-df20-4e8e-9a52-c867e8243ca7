import XlbFetch from '@/utils/XlbFetch';
//获取数据

//审核
export const auditInfo = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storeorder.audit', data);
};
export const storeorderCopy = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storeorder.copy', data);
};
export const batchaudit = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storeorder.batchaudit', data);
};
export const deletePhoto = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.file.delete', data);
};
// 确认
export const confirmItem = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storeorder.confirm', data);
};
