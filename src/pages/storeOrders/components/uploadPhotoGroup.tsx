import { EyeOutlined } from '@ant-design/icons';
import {
  XlbBaseUpload,
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbMessage,
  XlbModal,
} from '@xlb/components';
import { Modal } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import { deletePhoto } from './server';
import styles from './style.less';

let uuid = 0;

const UploadPhotoGroup: FC = (props: any) => {
  const [form] = XlbBasicForm.useForm();
  const { details, onChangePhotoGruop } = props;
  const [visible, setVisible] = useState(false);

  const readOnly = details.state === 'AUDIT';

  const emptyPhoto = { id: uuid };
  const [photoGroups, setPhotoGroups] = useState([emptyPhoto] as any);

  useEffect(() => {
    if (details?.photo_groups?.length) {
      setPhotoGroups(details?.photo_groups);
    }
  }, [details.photo_groups]);

  const scrollableRef = useRef(null);
  const addPhotoGroups = () => {
    uuid = uuid + 1;
    setPhotoGroups([
      ...photoGroups,
      {
        id: uuid,
      },
    ]);
  };

  useEffect(() => {
    if (scrollableRef.current) {
      scrollableRef.current.scrollTop = scrollableRef.current.scrollHeight;
    }
  }, [photoGroups]);

  const deletePhotoGroups = async (index) => {
    const before_photo = photoGroups[index].before_photo;
    const after_photo = photoGroups[index].after_photo;

    if (before_photo && before_photo[0]?.id) {
      await deletePhoto({
        id: before_photo[0]?.id,
      });
    }

    if (after_photo && after_photo[0]?.id) {
      await deletePhoto({
        id: after_photo[0]?.id,
      });
    }

    XlbMessage.success('删除成功');

    if (photoGroups.length === 1) {
      setPhotoGroups([]);
    } else {
      const newList = [...photoGroups];
      newList.splice(index, 1);
      setPhotoGroups([...newList]);
    }
  };

  const [preViewVisible, setPreViewVisible] = useState(false);
  const [beforePhoto, setBeforePhoto] = useState('');
  const [afterPhoto, setAfterPhoto] = useState('');

  const viewPhotoGroups = (before_photo, after_photo) => {
    setBeforePhoto(before_photo[0].url);
    setAfterPhoto(after_photo[0].url);
    setPreViewVisible(true);
    console.log(before_photo[0].url, after_photo[0].url);
  };

  const handleChangeFile = (file, index, key) => {
    let photoItem = photoGroups[index];
    photoItem = {
      ...photoItem,
      [key]: file,
    };
    const newList = photoGroups;
    newList.splice(index, 1, photoItem);
    setPhotoGroups([...newList]);
  };

  const handleDeleteFile = async (file) => {
    await deletePhoto({
      id: file?.id,
    });
  };

  const handleSubmit = () => {
    const empty = photoGroups.find((item) => {
      return !item.before_photo || !item.after_photo;
    });
    if (empty) {
      return XlbMessage.error('请完善图片组');
    }
    onChangePhotoGruop(photoGroups);
    setVisible(false);
  };

  return (
    <>
      <XlbButton.Group>
        <XlbModal
          title="上传附件"
          isCancel={true}
          width={520}
          open={visible}
          onCancel={() => setVisible(false)}
          onOk={() => handleSubmit()}
        >
          <div>
            <div
              style={{ fontSize: '14px', lineHeight: '14px', margin: '16px 0' }}
            >
              *为增加审核通过率，
              <span style={{ color: '#FF7D01' }}>
                请对应上传补货前后的对比图片
              </span>
            </div>
            <div className={styles.line}>
              <div className={styles.before}>补货前</div>
              <div className={styles.center}>VS</div>
              <div className={styles.right}>补货后</div>
            </div>
            <div className={styles.group_box} ref={scrollableRef}>
              {photoGroups?.map((item, index) => {
                return (
                  <div key={item.id}>
                    {!readOnly ? (
                      <div className={styles.titleline}>
                        {' '}
                        <div
                          onClick={() => {
                            deletePhotoGroups(index);
                          }}
                          className={styles.titlelineChild}
                        >
                          <XlbIcon name="shanchu" color="#F53F3F" />
                          <span className={styles.title}> 组{index + 1}</span>
                        </div>
                        <div className={styles.titlelineChild}>
                          <XlbButton
                            type="text"
                            size="small"
                            label="对比"
                            disabled={!item.before_photo || !item.after_photo}
                            onClick={() =>
                              viewPhotoGroups(
                                item.before_photo,
                                item.after_photo,
                              )
                            }
                            icon={<EyeOutlined />}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className={styles.titleline}>
                        <span className={styles.title}> 组{index + 1}</span>
                      </div>
                    )}
                    <div
                      style={{
                        display: 'flex',
                        marginBottom: 12,
                        justifyContent: 'space-around',
                      }}
                    >
                      <XlbBaseUpload
                        style={{ background: '#e8f1ff !important' }}
                        wrapperClassName="wrapper-left"
                        disabled={readOnly}
                        width={228}
                        height={128}
                        listType="picture-card"
                        name=""
                        maxCount={1}
                        fileList={item.before_photo}
                        onChange={(file) => {
                          handleChangeFile(file, index, 'before_photo');
                        }}
                        beforeDelete={(file) => {
                          handleDeleteFile(file);
                          return true;
                        }}
                        multiple={false}
                        size={10240000}
                        action={`${process.env.BASE_URL}/erp/hxl.erp.storeorder.file.upload`}
                        accept={['image']}
                        data={{
                          type: 'REQUEST',
                          fid: details?.fid ?? '',
                          group_order: index,
                          order: 0,
                        }}
                      ></XlbBaseUpload>

                      <XlbBaseUpload
                        style={{ background: '#e8f1ff !important' }}
                        wrapperClassName="wrapper-right"
                        disabled={readOnly}
                        width={228}
                        height={128}
                        listType="picture-card"
                        name=""
                        maxCount={1}
                        fileList={item.after_photo}
                        onChange={(file) => {
                          handleChangeFile(file, index, 'after_photo');
                        }}
                        beforeDelete={(file) => {
                          handleDeleteFile(file);
                          return true;
                        }}
                        multiple={false}
                        size={10240000}
                        action={`${process.env.BASE_URL}/erp/hxl.erp.storeorder.file.upload`}
                        accept={['image']}
                        data={{
                          type: 'REQUEST',
                          fid: details?.fid ?? '',
                          group_order: index,
                          order: 1,
                        }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>

            {photoGroups?.length < 10 && !readOnly && (
              <XlbButton
                type="text"
                onClick={addPhotoGroups}
                icon={<XlbIcon name="jia" />}
              >
                添加图片组
              </XlbButton>
            )}
          </div>
        </XlbModal>
        <XlbButton
          type="primary"
          icon={<XlbIcon name="fujian" />}
          onClick={() => setVisible(true)}
        >
          附件
          {details?.photo_groups?.length > 0 &&
            `(${details?.photo_groups?.length})`}
        </XlbButton>
      </XlbButton.Group>
      {/* 图片预览组 */}
      <Modal
        visible={preViewVisible}
        footer={null}
        centered
        onCancel={() => setPreViewVisible(false)}
        width="100%"
        className="custom-preview-modal" // 应用自定义样式
      >
        <div className="custom-preview-content">
          <img src={beforePhoto} className="custom-preview-image" />
          <img src={afterPhoto} className="custom-preview-image" />
        </div>
      </Modal>
    </>
  );
};
export default UploadPhotoGroup;
