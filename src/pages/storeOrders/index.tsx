import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { history } from '@@/core/history';
import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import {
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbMessage,
  XlbProPageContainer,
  XlbTipsModal,
} from '@xlb/components';

import { FormInstance } from 'antd';
import dayjs from 'dayjs';
import Decimal from 'decimal.js';
import { FC, useEffect, useRef, useState } from 'react';
import BatchOrderIndex from './components/batchOrder/batchOrder';
import UploadPhotoGroup from './components/uploadPhotoGroup';
import {
  Columns,
  isReadOnly,
  OrderType,
  otherFormList,
  renderBasicFormList,
  renderProductDetailsTableList,
  searchFormList,
} from './data';
import { confirmItem, storeorderCopy } from './server';
const Index: FC = () => {
  let fetchDataFunc = () => {};
  const pageRef = useRef(null);
  const inVoiceForm = useRef<any>();
  const formInstance = useRef<FormInstance<any>>();
  const [batchOrderVisible, setBatchOrderVisible] = useState<boolean>(false);

  const importStores = async (context: {
    dataSource: any[];
    selectRow: any[];
    form?: FormInstance;
  }) => {
    const { dataSource, form } = context;

    const res = await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.storearea.store.import`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeorder.template.download`,
      templateName: '模板下载',
    });
    if (res.code !== 0) return;
    let data = res.data?.stores;
    const errInfo = [];
    if (dataSource?.length) {
      const ids = dataSource.map((i) => i.id);
      errInfo.push(
        ...data
          .filter((i: any) => ids.includes(i.id))
          .map((i: any) => `【${i.store_name}】`),
      );
      data = data.filter((i: any) => !ids.includes(i.id));
    }
    if (errInfo.length) {
      await XlbTipsModal({
        tips: '以下门店已存在，系统已自动过滤',
        tipsList: errInfo,
      });
    }
    form?.setFieldValue('details', [...dataSource, ...data]);
  };

  const copyItem = (chooseList: any) => {
    XlbTipsModal({
      tips: `是否确认复制${chooseList[0].fid}?`,
      isCancel: true,
      onOk: async () => {
        const res = await storeorderCopy({ fid: chooseList[0].fid });
        if (res.code === 0) {
          XlbMessage.success('复制成功');
          fetchDataFunc();
        }
      },
    });
  };

  const deleteItem = (dataSource, selectRow, form) => {
    console.log(dataSource, selectRow, form.getFieldsValue());

    // 提取 selectRow 中的 id，存储为 Set
    const selectRowIds = new Set(selectRow.map((item) => item.id));

    // 过滤掉 dataSource 中与 selectRow 中 id 相同的数据
    const afterDeleteDataSource =
      dataSource.filter((dataItem) => !selectRowIds.has(dataItem.id)) || [];

    XlbMessage.success('删除成功');

    form?.setFieldValue('details', afterDeleteDataSource);
  };

  // 确认
  const requestConfirm = async (obj: any) => {
    XlbTipsModal({
      tips: `是否确认确认${obj.fid}数据?`,
      isCancel: true,
      onOk: async () => {
        const data = {
          ...obj,
          details: obj.details.map((item: any) => {
            return {
              ...item,
              basic_receive_quantity: new Decimal(item.receive_quantity ?? 0)
                .mul(new Decimal(item.ratio ?? 0))
                .toNumber(),
            };
          }),
        };
        const res = await confirmItem(data);
        if (res.code === 0) {
          XlbMessage.success('确认成功');
        }
      },
    });
  };

  const handleChangePhotoGruop = (photoGroup) => {
    formInstance.current?.setFieldValue('photo_groups', photoGroup);
  };
  const userInfo = LStorage.get('userInfo');
  useEffect(() => {
    const record = history.location.state as any;
    if (record?.type === 'form_erp') {
      Array.from(document.getElementsByTagName('span')).forEach((_) => {
        if (_.textContent === '新增') {
          _.click();
        }
      });
    }

    // ! https://gaor4awyz1u.feishu.cn/wiki/PHR4wZ2KbihkpBkLYsacKQ5sn1e  参照这个需求
  }, []);
  return (
    <div style={{ height: '100%' }}>
      <XlbProPageContainer
        ref={pageRef}
        searchFieldProps={{
          formList: searchFormList,
          initialValues: {
            create_date: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
            supplier_ids: userInfo.supplier ? [userInfo.supplier.id] : void 0,
          },
        }}
        addFieldProps={{
          url: hasAuth(['门店订单', '编辑'])
            ? '/erp/hxl.erp.storeorder.save'
            : undefined,
        }}
        deleteFieldProps={{
          name: '删除',
          url: hasAuth(['门店订单', '删除'])
            ? '/erp/hxl.erp.storeorder.batchdelete'
            : '',
          params: (data: any, v: any) => {
            return { fids: v?.map((j: { fid: any }) => j?.fid) };
          },
        }}
        exportFieldProps={{
          url: hasAuth(['门店订单', '导出'])
            ? '/erp/hxl.erp.storeorder.export'
            : '',
          fileName: '门店订单.xlsx',
        }}
        extra={(context) => {
          const {
            fetchData,
            selectRowKeys,
            selectRow,
            dataSource,
            requestForm,
          } = context;
          fetchDataFunc = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['门店订单', '编辑']) && (
                <XlbButton
                  label="复制"
                  type="primary"
                  disabled={selectRowKeys?.length !== 1}
                  onClick={() => copyItem(selectRow)}
                  icon={<CopyOutlined />}
                />
              )}
              {hasAuth(['门店订单', '编辑']) && (
                <XlbButton
                  label="批量制单"
                  type="primary"
                  onClick={() => setBatchOrderVisible(true)}
                  icon={<XlbIcon name="piliang" />}
                />
              )}
              {hasAuth(['门店订单', '导入']) && (
                <XlbButton
                  label="导入"
                  type="primary"
                  onClick={async () => {
                    const res = await XlbImportModal({
                      templateUrl: `/erp/hxl.erp.storeorder.template.direct.download`,
                      importUrl: `/erp/hxl.erp.storeorder.direct.import`,
                      templateName: '下载导入模板',
                      params: {
                        type: 'DIRECT_SUPPLY_CAR_SALE',
                      },
                      callback: (res) => {
                        console.log(res);
                      },
                    });
                  }}
                  icon={<XlbIcon name="daoru" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.storeorder.page',
          tableColumn: Columns,
          primaryKey: 'fid',
          selectMode: 'multiple',
          immediatePost: false,
        }}
        details={{
          mode: 'page',
          title: '门店订单详情',
          primaryKey: 'fid',
          queryFieldProps: {
            url: '/erp/hxl.erp.storeorder.read',
            params: (obj: any) => {
              return {
                fid: obj?.fid,
              };
            },
            afterPost: (data) => {
              formInstance.current?.setFieldValue(
                'photo_groups',
                data?.photo_groups,
              );
              formInstance.current?.setFieldsValue({
                photo_groups: data?.photo_groups,
              });
              let ref_order_fid = '';
              let ref_order_type = '';
              data?.details?.forEach((item) => {
                if (item.ref_order_fid) {
                  ref_order_fid = item.ref_order_fid;
                }
                if (item.ref_order_type) {
                  ref_order_type = item.ref_order_type;
                }
                item.id = item.item_id; // 详情查出的商品没有id，导致新增弹窗里没有勾上
              });
              // console.log({
              //   ...data,
              //   ref_order_fid,
              //   ref_order_type,
              // });

              return {
                ...data,
                ref_order_fid,
                ref_order_type,
              };
            },
          },

          readOnly: (formValues: any) => {
            return isReadOnly(formValues);
          },
          updateFieldProps: {
            url: hasAuth(['门店订单', '编辑'])
              ? '/erp/hxl.erp.storeorder.update'
              : '',
            beforePost: (data) => {
              if (!data.details?.length) {
                XlbMessage.error('请选择商品');
                return false;
              }
              const invalidItem = data.details.find((item) => {
                return (
                  (!item.price && item.price != 0) ||
                  (!item.quantity && item.quantity != 0)
                );
              });
              if (invalidItem) {
                XlbMessage.error('请完善商品数据');
                return false;
              }
              return {
                ...data,
                details: data.details.map((item) => {
                  return {
                    ...item,
                    temperature_info: {
                      temperature: item.temperature,
                      image_urls: item.image_urls,
                    },
                  };
                }),
              };
            },
            hidden(formValues) {
              return isReadOnly(formValues);
            },
          },

          saveFieldProps: {
            url: hasAuth(['门店订单', '编辑'])
              ? '/erp/hxl.erp.storeorder.save'
              : '',
            beforePost: (data) => {
              if (!data.details?.length) {
                XlbMessage.error('请选择商品');
                return false;
              }
              const invalidItem = data.details.find((item) => {
                return (
                  (!item.price && item.price != 0) ||
                  (!item.quantity && item.quantity != 0)
                );
              });
              if (invalidItem) {
                XlbMessage.error('请完善商品数据');
                return false;
              }
              return {
                ...data,
                type: data?.type || OrderType.DIRECT_SUPPLY_CAR_SALE,
                details: data.details.map((item) => {
                  return {
                    ...item,
                    temperature_info: {
                      temperature: item.temperature,
                      image_urls: item.image_urls,
                    },
                  };
                }),
              };
            },
            hidden(formValues) {
              return isReadOnly(formValues);
            },
          },

          exportFieldProps: {
            url: hasAuth(['门店订单', '导出'])
              ? '/erp/hxl.erp.storeorder.export'
              : '',
            name: '导出',
            fileName: '门店订单.xlsx',
          },
          extra: ({ values, form, ...args }) => {
            console.log('vvvv:', values);
            inVoiceForm.current = values;
            formInstance.current = form;
            return (
              <XlbButton.Group>
                <UploadPhotoGroup
                  onChangePhotoGruop={handleChangePhotoGruop}
                  details={values}
                />
                {hasAuth(['门店订单', '确认']) && (
                  <XlbButton
                    label="确认"
                    type="primary"
                    disabled={
                      values?.confirm_state === 'SUPPLIER_CONFIRM' &&
                      values?.state === 'AUDIT'
                        ? false
                        : true
                    }
                    onClick={() => requestConfirm(values)}
                    icon={<XlbIcon name="shengchengXXdan" />}
                  />
                )}
              </XlbButton.Group>
            );
          },
          formList: [
            {
              componentType: 'tabs',
              fieldProps: {
                items: [
                  {
                    key: '1',
                    label: '基本信息',
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          itemSpan: 6,
                          width: '100%',
                          formList: renderBasicFormList(inVoiceForm),
                        },
                      },
                    ],
                  },
                  {
                    key: '2',
                    label: '其他信息',
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          itemSpan: 6,
                          width: '100%',
                          formList: otherFormList,
                        },
                      },
                    ],
                  },
                ],
              },
            },
            {
              componentType: 'blueBar',
              fieldProps: {
                title: '商品明细',
              },
              dependencies: ['store_id', 'supplier_id', 'details'],
              children: (obj) => {
                const getTotal = (key: string) =>
                  obj?.details?.reduce(
                    (acc: any, curr: any) => acc + (curr?.[key] || 0),
                    0,
                  );
                const isAllEmpty = (key: string) =>
                  obj?.details.every(
                    (item: any) => !item[key] && item[key] !== 0,
                  );
                const footerDataSource = obj?.details?.length
                  ? [
                      {
                        _index: '合计',
                        quantity: getTotal('quantity'),
                        actual_delivered_quantity: getTotal(
                          'actual_delivered_quantity',
                        ),
                        receive_money: isAllEmpty('receive_money')
                          ? '-'
                          : getTotal('receive_money'),
                        receive_quantity: isAllEmpty('receive_quantity')
                          ? '-'
                          : getTotal('receive_quantity'),
                        money: hasAuth(['门店订单/采购价', '查询'])
                          ? getTotal('money')?.toFixed(4)
                          : '****',
                        no_tax_money: hasAuth(['门店订单/采购价', '查询'])
                          ? obj?.details
                              ?.reduce(
                                (acc: any, curr: any) =>
                                  acc +
                                  (new Decimal(curr?.money ?? 0)
                                    .div(
                                      new Decimal(curr?.input_tax_rate ?? 0)
                                        .div(new Decimal(100))
                                        .add(new Decimal(1)),
                                    )
                                    .toNumber() || 0),
                                0,
                              )
                              ?.toFixed(4)
                          : '****',
                        basic_quantity: getTotal('basic_quantity')?.toFixed(3),
                      },
                    ]
                  : [];
                return [
                  {
                    componentType: 'editTable',
                    name: 'details',
                    disabled: !obj.store_id,
                    fieldProps: {
                      columns: renderProductDetailsTableList(obj),
                      footerDataSource: footerDataSource,
                      dialogParams: {
                        type: 'goods',
                        url: '/erp/hxl.erp.storeorder.item.page',
                        dataType: 'lists',
                        immediatePost: false,
                        isLeftColumn: true,
                        isMultiple: true,
                        primaryKey: 'id',
                        data: {
                          type: 'DIRECT_SUPPLY_CAR_SALE',
                          enabled: true,
                          store_id: obj.store_id,
                          supplier_id: obj.supplier_id,
                        },
                      },
                      afterPost: (newData, oldData) => {
                        console.log(newData, oldData);

                        // 初始化选中数据的价格
                        const newDataInitPriceList = newData.map((item) => {
                          return {
                            ...item,
                            price: new Decimal(item.basic_price ?? 0)
                              .mul(
                                item?.store_item_supplier_default_unit ===
                                  'BASIC'
                                  ? 1
                                  : new Decimal(item.purchase_ratio ?? 0),
                              )
                              .toNumber(),
                            item_id: item.id,
                            item_code: item.code,
                            item_bar_code: item.bar_code,
                            item_name: item.name,
                            ratio:
                              item?.store_item_supplier_default_unit === 'BASIC'
                                ? 1
                                : item.purchase_ratio,
                            basic_unit: item.unit,
                            unit:
                              item?.store_item_supplier_default_unit === 'BASIC'
                                ? item?.unit
                                : item.purchase_unit,
                          };
                        });

                        // 已选中列的数据不能被替换
                        const updatedData = newDataInitPriceList.map(
                          (newItem) => {
                            const oldItem = oldData.find(
                              (oldItem) => oldItem.id === newItem.id,
                            );
                            return oldItem ? oldItem : newItem;
                          },
                        );

                        return updatedData;
                      },
                      addConfig: {
                        label: '批量新增',
                        dependencies: ['store_id'],
                        hidden() {
                          return (
                            !hasAuth(['门店订单', '编辑']) || isReadOnly(obj)
                          );
                        },
                      },
                      delConfig: {
                        hidden: true,
                        // hidden() {
                        //   return (
                        //     !hasAuth(['门店订单', '编辑']) ||
                        //     obj.state === 'AUDIT'
                        //   );
                        // },
                      },
                      extra: (context) => {
                        const { dataSource, selectRow, form } = context;

                        // 更新表格后dataSource是最新的，selectRow没有重置，导致disabled不起作用，所以要过滤
                        const isExist = selectRow.some((selectItem) =>
                          dataSource.some(
                            (dataItem) => dataItem.id === selectItem.id,
                          ),
                        );

                        return !hasAuth(['门店订单', '编辑']) ||
                          isReadOnly(obj) ? (
                          ''
                        ) : (
                          <XlbButton
                            label="删除"
                            type="primary"
                            disabled={selectRow?.length === 0 || !isExist}
                            onClick={() =>
                              deleteItem(dataSource, selectRow, form)
                            }
                            icon={<DeleteOutlined />}
                          />
                        );
                      },
                    },
                  },
                ];
              },
            },
          ],
          initialValues: {
            supplier_id: userInfo.supplier ? userInfo.supplier.id : void 0,
          },
          // updateFieldProps: {
          //   url: '/erp/hxl.erp.paymentmethod.update'
          // }
        }}
      />
      <BatchOrderIndex
        open={batchOrderVisible}
        setOpen={setBatchOrderVisible}
      />
    </div>
  );
};
export default Index;
