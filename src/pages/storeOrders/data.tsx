import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { ProForm } from '@ant-design/pro-components';
import {
  XlbBaseUpload,
  XlbInput,
  XlbInputNumber,
  XlbSelect,
  XlbTableColumnProps,
} from '@xlb/components';
import Decimal from 'decimal.js';

export enum OrderType {
  DIRECT_SUPPLY_CAR_SALE = 'DIRECT_SUPPLY_CAR_SALE', // 直供车销
  DIRECT_SUPPLY_RESERVE = 'DIRECT_SUPPLY_RESERVE', // 直供预定
}

export const receiptStatus = [
  { label: '制单', value: 'INIT', type: 'info' },
  { label: '审核', value: 'AUDIT', type: 'warning' },
  { label: '作废', value: 'INVALID', type: 'error' },
];
// 确认状态option
const CONFIRM_STATUS = Object.freeze([
  { label: '未确认', value: 'INIT', type: 'info' },
  { label: '供应商已确认', value: 'SUPPLIER_CONFIRM', type: 'warning' },
  { label: '门店已确认', value: 'STORE_CONFIRM', type: 'success' },
  { label: '已作废', value: 'INVALID', type: 'danger' },
]);

export const refOrderType = [
  {
    label: '采购订单',
    value: 'PURCHASE',
  },
];

const userInfo = LStorage.get('userInfo');
export const searchFormList = [
  {
    id: 'dateCommon',
    label: '日期范围',
    name: 'create_date',
    format: 'YYYY-MM-DD',
  },

  'scmTimeType',
  {
    id: 'commonSelect',
    label: '单据状态',
    name: 'state',
    fieldProps: { options: receiptStatus },
  },
  {
    id: 'commonSelect',
    label: '确认状态',
    name: 'confirm_state',
    fieldProps: { options: CONFIRM_STATUS },
  },
  'scmDocumentNumber',
  'scmDeliveryType',
  'scmDownstreamDocuments',
  {
    id: ScmFieldKeyMap?.scmDocumentNumber,
    label: '下游单据号',
    name: 'ref_order_fid',
  },
  {
    id: ScmFieldKeyMap?.scmOutOrgIds,
    label: '收货组织',
    name: 'org_ids',
  },

  {
    id: ScmFieldKeyMap?.scmSupplierIds,
    label: '供应商',
    name: 'supplier_ids',
    disabled: () => !!userInfo.supplier,
  },
  {
    id: ScmFieldKeyMap?.billStoreCenterId,
    label: '收货门店',
    name: 'store_id',
    dependencies: ['supplier_ids'],
    disabled(formValues: any) {
      return !formValues.supplier_ids;
    },
    fieldProps(formValue: any) {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          url: '/erp/hxl.erp.store.short.page',
          data: {
            supplier_id: formValue.getFieldValue('supplier_ids')?.[0] || '',
            query_supply: true,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
  },
  {
    id: ScmFieldKeyMap?.outOrgIds,
    label: '发货组织',
    name: 'out_org_ids',
  },

  { id: 'scmStoreOrderItemIds', label: '商品档案', name: 'items' },
  {
    id: ScmFieldKeyMap?.scmCreateBy,
    label: '制单人',
    name: 'create_by',
  },
  { id: ScmFieldKeyMap?.scmAuditBy, label: '审核人', name: 'audit_by' },
  { id: ScmFieldKeyMap?.scmCommitBy, label: '确认人', name: 'commit_by' },
];

export const isReadOnly = (formValues: any) =>
  formValues.fid &&
  (formValues.state === 'AUDIT' ||
    formValues.type !== OrderType.DIRECT_SUPPLY_CAR_SALE);
export const renderBasicFormList = (inVoiceForm) => {
  return [
    {
      id: ScmFieldKeyMap?.storeSupplierId,
      label: '供应商',
      name: 'supplier_id',
      disabled: () => !!userInfo.supplier,
    },
    {
      id: ScmFieldKeyMap?.billStoreCenterId,
      label: '收货门店',
      name: 'store_id',
      dependencies: ['supplier_id'],
      rules: [{ required: true, message: '请选择收货门店' }],
      disabled(formValues) {
        return !formValues.supplier_id;
      },
      handleOnChange: (_data: any, form: any) => {
        form.setFieldsValue({
          storehouse_id: void 0,
          storehouse_name: void 0,
        });
      },
      hidden: () => isReadOnly(inVoiceForm.current),
      fieldProps(formValue: any) {
        return {
          dialogParams: {
            type: 'store',
            dataType: 'lists',
            isMultiple: false,
            url: '/erp/hxl.erp.store.short.page',
            data: {
              supplier_id: formValue.getFieldValue('supplier_id') || '',
              query_supply: true,
            },
          },
          fieldNames: {
            idKey: 'id',
            nameKey: 'store_name',
          },
        };
      },
    },
    {
      id: 'commonInput',
      label: '收货门店',
      name: 'store_name',
      dependencies: ['supplier_id'],
      disabled(formValues: any) {
        return !formValues.supplier_id;
      },
      hidden: () => !isReadOnly(inVoiceForm.current),
    },
    {
      id: ScmFieldKeyMap?.scmStoreStorehouseId,
      label: '收货仓库',
      name: 'storehouse_id',
      dependencies: ['store_id'],
      disabled(formValues) {
        return !formValues.store_id;
      },
      hidden: () => isReadOnly(inVoiceForm.current),
    },
    {
      id: 'commonInput',
      label: '收货仓库',
      name: 'storehouse_name',
      dependencies: ['store_id'],
      disabled(formValues) {
        return !formValues.store_id;
      },
      hidden: () => !isReadOnly(inVoiceForm.current),
    },
    {
      id: ScmFieldKeyMap?.scmDocumentNumber,
      label: '单据号',
      name: 'fid',
      disabled: true,
    },
    {
      id: ScmFieldKeyMap?.deliveryDate,
      label: '收货日期',
      name: 'date',
      disabled: true,
    },
    {
      id: ScmFieldKeyMap?.scmDownstreamDocuments,
      label: '下游单据',
      name: 'ref_order_type',
      disabled: true,
    },
    {
      id: ScmFieldKeyMap?.scmDocumentNumber,
      label: '下游单据号',
      name: 'ref_order_fid',
      disabled: true,
    },
    {
      id: 'commonInput',
      label: '联系门店',
      name: 'store_tel',
      disabled: true,
    },
    {
      id: 'commonInput',
      label: '留言备注',
      name: 'memo',
      itemSpan: 12,
    },
    {
      id: ScmFieldKeyMap?.scmStoreStoreCommonUpload,
      label: '到货单',
      name: 'receive_order_image_urls',
      itemSpan: 6,
      rules: [{ required: true, message: '请上传文件' }],
      fieldProps: {
        mode: 'textButton',
        multiple: true,
        maxCount: 10,
        listType: 'picture',
        action: '/erp/hxl.erp.storeorder.file.upload',
        accept: ['image'],
        data: {
          fid: inVoiceForm.current?.fid || '',
          type: 'RECEIVE',
        },
      },
    },
  ];
};

export const otherFormList = [
  {
    id: ScmFieldKeyMap?.scmCreateBy,
    label: '制单人',
    name: 'create_by',
    disabled: true,
  },
  {
    id: ScmFieldKeyMap?.deliveryDate,
    label: '制单时间',
    name: 'create_time',
    disabled: true,
  },
  {
    id: ScmFieldKeyMap?.scmAuditBy,
    label: '审核人',
    name: 'audit_by',
    disabled: true,
  },
  {
    id: ScmFieldKeyMap?.deliveryDate,
    label: '审核时间',
    name: 'audit_time',
    disabled: true,
  },
];

const handleKeyDown = (e) => {
  if (e.key === 'Enter') {
    e.preventDefault(); // 阻止 Enter 键的默认行为
  }
};

const getBasicQuantity = (ratio, quantity) => {
  return new Decimal(quantity ?? 0).mul(new Decimal(ratio ?? 0)).toNumber();
};

const getBasicPrice = (ratio, price) => {
  return new Decimal(price ?? 0).div(new Decimal(ratio ?? 0)).toNumber();
};

const getMoney = (price, quantity) => {
  return new Decimal(price ?? 0).mul(new Decimal(quantity ?? 0)).toNumber();
};

const getPrice = (ratio, basicPrice) => {
  return new Decimal(basicPrice ?? 0).mul(new Decimal(ratio ?? 0)).toNumber();
};

const getQuantity = (ratio, basicQuantity) => {
  return new Decimal(basicQuantity ?? 0)
    .div(new Decimal(ratio ?? 0))
    .toNumber();
};

// 单价 / （（税率/100）+ 1））
const getNoTaxPrice = (input_tax_rate, price) => {
  return new Decimal(price ?? 0)
    .div(
      new Decimal(input_tax_rate ?? 0)
        .div(new Decimal(100))
        .add(new Decimal(1)),
    )
    .toNumber();
};

// 金额 / （（税率/100）+ 1））
const getNoTaxMoney = (input_tax_rate, money) => {
  return new Decimal(money ?? 0)
    .div(
      new Decimal(input_tax_rate ?? 0)
        .div(new Decimal(100))
        .add(new Decimal(1)),
    )
    .toNumber();
};

// 更新基本单价、金额、金额（去税）
const changePrice = (form, e, row, index) => {
  const basicPrice = getBasicPrice(row.ratio, e);
  form.setFieldValue(['details', index['index'], 'basic_price'], basicPrice);

  const money = getMoney(e, row.quantity);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

const changeRatio = (form, e, row, index) => {
  let unitName = row.unit;
  row.units.forEach((item) => {
    if (item.value === e) {
      unitName = item.label;
    }
  });

  form.setFieldValue(['details', index['index'], 'unit'], unitName);
  form.setFieldValue(['details', index['index'], 'ratio'], e);

  const price = getPrice(e, row.basic_price);
  form.setFieldValue(['details', index['index'], 'price'], price);

  const quantity = getQuantity(e, row.basic_quantity);
  form.setFieldValue(['details', index['index'], 'quantity'], quantity);

  const money = getMoney(price, quantity);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 更新基本数量、金额
const changeQuantity = (form, e, row, index) => {
  const basicQuantity = getBasicQuantity(row.ratio, e);
  form.setFieldValue(
    ['details', index['index'], 'basic_quantity'],
    basicQuantity,
  );

  const money = getMoney(row.price, e);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 基本单价更新后，更新单价和金额
const changeBasicPrice = (form, e, row, index) => {
  const price = getPrice(row.ratio, e);
  form.setFieldValue(['details', index['index'], 'price'], price);

  const money = getMoney(price, row.quantity);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 基本数量更新后，更新数量和金额
const changeBasicQuantity = (form, e, row, index) => {
  const quantity = getQuantity(row.ratio, e);
  form.setFieldValue(['details', index['index'], 'quantity'], quantity);

  const money = getMoney(quantity, row.price);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 生产日期更新后，更新到期日
// const changeProducingDate = (form, e, row, index) => {
//   const quantity = getQuantity(row.ratio, e);
//   form.setFieldValue(['details', index['index'], 'quantity'], quantity);
//   changeQuantity(form, quantity, row, index);
// };

export const Columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: COLUMN_WIDTH_ESUM.INDEX,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 180,
    features: { sortable: true, details: true },
  },
  {
    name: '配送日期',
    code: 'audit_time',
    width: 130,
    features: { sortable: false },
    render(text, record) {
      return text || '/';
    },
  },
  {
    name: '收货门店',
    code: 'store_id',
    width: 130,
    features: { sortable: true },
    render(text, record, index) {
      return record.store_name;
    },
  },
  {
    name: '发货门店',
    code: 'out_store_id',
    width: 100,
    features: { sortable: true },
    render(text, record, index) {
      return record.out_store_name;
    },
  },
  {
    name: '供应商',
    code: 'supplier_id',
    width: 150,
    features: { sortable: true },
    render(text, record, index) {
      return record.supplier_name;
    },
  },
  {
    name: '下游单据',
    code: 'ref_order_type',
    width: 150,
    features: { sortable: false },
  },
  {
    name: '下游单据号',
    code: 'ref_order_fid',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '单据金额(含税)',
    code: 'money',
    width: 150,
    features: { sortable: true },
    render(text) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  {
    name: '单据金额(去税)',
    code: 'no_tax_money',
    width: 150,
    render(text) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '实收数量',
    code: 'actually_received_quantity',
    width: 150,
    render: (text: any) => text ?? '-',
  },
  {
    name: '实收金额',
    code: 'actually_received_total_money',
    width: 150,
    render: (text: any) => text ?? '-',
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '单据状态',
    code: 'state',
    width: 150,
    features: { sortable: true },
    render(text, record) {
      const item = receiptStatus.find((v) => v.value === text);
      return (
        <div className={`${item ? item.type : ''}`}>
          {item ? item.label : ''}
        </div>
      );
    },
  },
  // 确认状态
  {
    name: '确认状态',
    code: 'confirm_state',
    width: 150,
    features: { sortable: true },
    render(text, record) {
      const stateItem = CONFIRM_STATUS.find((v) => v.value === text);
      if (record?.type === 'STOREHOUSE_DELIVERY') {
        return <div>-</div>;
      }
      return <div className={stateItem?.type}>{stateItem?.label || '-'}</div>;
    },
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '确认人',
    code: 'confirm_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '确认时间',
    code: 'confirm_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '留言备注',
    code: 'all_memo',
    width: 150,
    features: { sortable: false },
  },
];

export const renderProductDetailsTableList = (obj) => {
  return [
    {
      name: '序号',
      code: '_index',
      width: COLUMN_WIDTH_ESUM.INDEX,
      align: 'center',
    },
    {
      name: '商品代码',
      code: 'item_code',
      align: 'center',
      width: 120,
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: 120,
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 120,
    },
    {
      name: '采购规格',
      code: 'purchase_spec',
      width: 120,
    },
    {
      name: '采购单位',
      code: 'ratio',
      width: 120,
      render(text, row, index) {
        row.units = Array.from(
          new Set([
            JSON.stringify({
              label: row.delivery_unit,
              value: row.delivery_ratio,
            }),
            JSON.stringify({ label: row.basic_unit, value: 1 }),
            JSON.stringify({
              label: row.purchase_unit,
              value: row.purchase_ratio,
            }),
            JSON.stringify({ label: row.stock_unit, value: row.stock_ratio }),
            JSON.stringify({
              label: row.wholesale_unit,
              value: row.wholesale_ratio,
            }),
          ]),
        ).map((item) => {
          return JSON.parse(item);
        });

        return row._edit && obj.state !== 'AUDIT' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    noStyle
                    name={['details', index['index'], 'unit']}
                  >
                    <XlbSelect
                      width={80}
                      options={row.units || []}
                      // defaultValue={row.purchase_unit || 1}
                      allowClear={false}
                      onChange={(e) => {
                        changeRatio(form, e, row, index);
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : (
          row.unit
        );
      },
    },
    {
      name: '数量',
      code: 'quantity',
      width: 120,
      render(text, row, index) {
        return row._edit && obj.state !== 'AUDIT' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                // if (!row.quantity && row.basic_quantity) {
                //   const quantity = getQuantity(row.ratio, row.basic_quantity);
                //   form.setFieldValue(
                //     ['details', index['index'], 'quantity'],
                //     quantity,
                //   );
                // }
                return (
                  <ProForm.Item
                    name={['details', index['index'], 'quantity']}
                    noStyle
                  >
                    <XlbInputNumber
                      onKeyDown={handleKeyDown}
                      min={0}
                      precision={4} // 设置小数点最多为 4 位
                      formatter={(value) => {
                        // 格式化显示的值，确保只能是数字和小数点
                        return `${value}`.replace(/[^0-9.]/g, '');
                      }}
                      parser={(value) => {
                        // 解析输入的值，确保只能是数字和小数点
                        return value ? value.replace(/[^0-9.]/g, '') : '';
                      }}
                      onChange={(e) => {
                        changeQuantity(form, e, row, index);
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : (
          text
        );
      },
    },
    {
      name: '实发数量',
      code: 'actual_delivered_quantity',
      width: 120,
      align: 'right',
    },
    {
      name: '实收数量',
      code: 'receive_quantity',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return row._edit &&
          obj?.confirm_state === 'SUPPLIER_CONFIRM' &&
          obj?.state === 'AUDIT' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    name={['details', index['index'], 'receive_quantity']}
                    noStyle
                  >
                    <XlbInputNumber
                      onKeyDown={handleKeyDown}
                      min={0}
                      precision={4} // 设置小数点最多为 4 位
                      formatter={(value) => {
                        // 格式化显示的值，确保只能是数字和小数点
                        return `${value}`.replace(/[^0-9.]/g, '');
                      }}
                      parser={(value) => {
                        // 解析输入的值，确保只能是数字和小数点
                        return value ? value.replace(/[^0-9.]/g, '') : '';
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : (
          (text ?? '-')
        );
      },
    },
    {
      name: '单价',
      code: 'price',
      width: 120,
      render(text: number) {
        return hasAuth(['门店订单/采购价', '查询']) ? text?.toFixed(4) : '****';
      },
    },
    {
      name: '金额',
      code: 'money',
      width: 120,
      render(text, row, index) {
        return hasAuth(['门店订单/采购价', '查询']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item
              noStyle
              shouldUpdate
              dependencies={['price', 'quantity']}
            >
              {({ getFieldValue }) => {
                const price = getFieldValue([
                  'details',
                  index['index'],
                  'price',
                ]);
                const quantity = getFieldValue([
                  'details',
                  index['index'],
                  'quantity',
                ]);

                const money = getMoney(price, quantity);

                return money;
              }}
            </ProForm.Item>
          </div>
        ) : (
          '****'
        );
      },
    },
    {
      name: '实收金额',
      code: 'receive_money',
      width: 120,
      render(text: any) {
        return hasAuth(['门店订单/采购价', '查询']) ? (text ?? '-') : '****';
      },
    },
    {
      name: '进项税率%',
      code: 'input_tax_rate',
      width: 120,
    },
    {
      name: '单价（去税）',
      code: 'no_tax_price',
      width: 120,
      render(text, row, index) {
        return hasAuth(['门店订单/采购价', '查询']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate dependencies={['price']}>
              {({ getFieldValue }) => {
                const price = getFieldValue([
                  'details',
                  index['index'],
                  'price',
                ]);

                const no_tax_price = getNoTaxPrice(row.input_tax_rate, price);
                return no_tax_price?.toFixed(4);
              }}
            </ProForm.Item>
          </div>
        ) : (
          '****'
        );
      },
    },
    {
      name: '金额（去税）',
      code: 'no_tax_money',
      width: 120,
      render(text, row, index) {
        return hasAuth(['门店订单/采购价', '查询']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate dependencies={['price']}>
              {({ getFieldValue }) => {
                const money = getFieldValue([
                  'details',
                  index['index'],
                  'money',
                ]);

                const no_tax_money = getNoTaxMoney(row.input_tax_rate, money);
                return no_tax_money?.toFixed(4);
              }}
            </ProForm.Item>
          </div>
        ) : (
          '****'
        );
      },
    },
    {
      name: '换算率',
      code: 'ratio',
      width: 120,
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 120,
    },
    {
      name: '基本数量',
      code: 'basic_quantity',
      width: 120,
      render(text, row, index) {
        return text;
        // row._edit && obj.state !== 'AUDIT' ? (
        //   <div onClick={(e) => e.stopPropagation()}>
        //     <ProForm.Item noStyle shouldUpdate>
        //       {(form) => {
        //         return (
        //           <ProForm.Item
        //             name={['details', index['index'], 'basic_quantity']}
        //             rules={[{ required: true, message: '请输入基本数量' }]}
        //             noStyle
        //           >
        //             <XlbInputNumber
        //               onKeyDown={handleKeyDown}
        //               precision={4} // 设置小数点最多为 4 位
        //               formatter={(value) => {
        //                 // 格式化显示的值，确保只能是数字和小数点
        //                 return `${value}`.replace(/[^0-9.]/g, '');
        //               }}
        //               parser={(value) => {
        //                 // 解析输入的值，确保只能是数字和小数点
        //                 return value ? value.replace(/[^0-9.]/g, '') : '';
        //               }}
        //               onChange={(e) => {
        //                 changeBasicQuantity(form, e, row, index);
        //               }}
        //             />
        //           </ProForm.Item>
        //         );
        //       }}
        //     </ProForm.Item>
        //   </div>
        // ) :
      },
    },
    {
      name: '基本单价',
      code: 'basic_price',
      width: 120,
      render(text: number) {
        return hasAuth(['门店订单/采购价', '查询']) ? text?.toFixed(4) : '****';
      },
    },
    {
      name: '保质期',
      code: 'period',
      width: 120,
    },
    {
      name: '备注',
      code: 'memo',
      width: 120,
      render(text, row, index) {
        return row._edit && obj.state !== 'AUDIT' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle name={['details', index['index'], 'memo']}>
              <XlbInput width={80} />
            </ProForm.Item>
          </div>
        ) : (
          text
        );
      },
    },
    {
      name: '商品报告',
      code: 'item_report',
      width: 150,
      render(value: any, row: any, index: number): React.ReactNode {
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item
              noStyle
              name={['details', index['index'], 'item_report']}
            >
              <XlbBaseUpload
                uploadText="附件"
                multiple={false}
                mode={obj.state === 'AUDIT' ? 'look' : 'textButton'}
                action="/erp/hxl.erp.storeorder.file.upload"
                listType={'picture'}
                accept={'image'}
                data={{
                  fid: row.fid || '',
                  type: 'REPORT',
                  item_id: row.item_id || '',
                }}
                maxCount={1}

                // fileList={uploadData}
              />
            </ProForm.Item>
          </div>
        );
      },
    },
    {
      name: '温度',
      code: 'temperature',
      width: 150,
      render(text: any, row: any, index: number): React.ReactNode {
        return row._edit && obj.state !== 'AUDIT' ? (
          <>
            <div onClick={(e) => e.stopPropagation()}>
              <ProForm.Item
                noStyle
                name={['details', index['index'], 'temperature']}
                initialValue={row.temperature_info?.temperature}
              >
                <XlbInputNumber
                  onKeyDown={handleKeyDown}
                  min={-100}
                  max={200}
                />
              </ProForm.Item>
            </div>
          </>
        ) : (
          (row.temperature_info?.temperature ?? text)
        );
      },
    },
    {
      name: '温度图片',
      code: 'image_urls',
      width: 150,
      render(value: any, row: any, index: number): React.ReactNode {
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item
              noStyle
              name={['details', index['index'], 'image_urls']}
              initialValue={row.temperature_info?.image_urls}
            >
              <XlbBaseUpload
                uploadText="附件"
                multiple={false}
                mode={obj.state === 'AUDIT' ? 'look' : 'textButton'}
                action="/erp/hxl.erp.storeorder.file.upload"
                listType={'picture'}
                accept={'image'}
                data={{
                  fid: row.fid || '',
                  type: 'TEMPERATURE',
                  item_id: row.item_id || '',
                }}
                maxCount={1}
                // fileList={uploadData}
              />
            </ProForm.Item>
          </div>
        );
      },
    },
  ];
};
