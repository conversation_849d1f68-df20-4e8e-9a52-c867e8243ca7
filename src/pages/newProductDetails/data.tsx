import { XlbTableColumnProps } from '@xlb/components';

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 175,
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 175,
  },
  {
    name: '商品档案',
    code: 'item_name',
    width: 175,
  },
  {
    name: '收货门店',
    code: 'store_name',
    width: 175,
  },
  {
    name: '收货仓库',
    code: 'storehouse_name',
    width: 175,
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 175,
  },
  {
    name: '单位',
    code: 'unit',
    width: 175,
  },
  {
    name: '数量',
    code: 'quantity',
    width: 175,
  },
  {
    name: '操作时间',
    code: 'create_time',
    width: 175,
  },
];
