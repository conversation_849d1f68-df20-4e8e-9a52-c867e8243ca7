import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import { type FC } from 'react';
import { tableList } from './data';
// 新品明细
const ProForm: FC<{ title: string }> = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [
          {
            id: 'dateCommon',
            format: 'YYYY-MM-DD',
            label: '日期范围',
            name: 'create_date',
          },
          {
            id: ScmFieldKeyMap?.scmItemIds,
            label: '商品档案',
            name: 'item_ids',
          },
          {
            id: ScmFieldKeyMap?.scmStoreIds,
            label: '收货门店',
            name: 'store_ids',
          },
          {
            id: ScmFieldKeyMap?.scmStorehouseId,
            label: '收货仓库',
            name: 'storehouse_ids',
            fieldProps: {
              mode: 'multiple',
            },
          },
          {
            id: ScmFieldKeyMap?.supplierIds,
            label: '供应商',
            name: 'supplier_ids',
          },
        ],
        initialValues: {
          create_date: [
            dayjs().startOf('day').format('YYYY-MM-DD'),
            dayjs().endOf('day').format('YYYY-MM-DD'),
          ],
        },
      }}
      tableFieldProps={{
        url: '/scm/hxl.scm.newitempurchasedetail.page',
        tableColumn: tableList,
        selectMode: 'multiple',
        immediatePost: true,
      }}
      exportFieldProps={{
        url: hasAuth(['新品申请/新品明细', '导出'])
          ? '/scm/hxl.scm.newitemdetail.export'
          : '',
        fileName: '新品明细',
      }}
    />
  );
};

export default ProForm;
