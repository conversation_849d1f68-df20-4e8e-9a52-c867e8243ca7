import { useIRouter } from '@/qiankun/utils';
import { Button } from 'antd';
import { useState } from 'react';
import { useAliveController } from 'react-activation';

interface PathItem {
  path: string;
  name: string;
}

export default function SideAndTab() {
  const { navigate } = useIRouter();

  // https://github.com/CJY0208/react-activation/blob/master/README_CN.md#%E5%8E%9F%E7%90%86%E6%A6%82%E8%BF%B0
  const { dropScope } = useAliveController();

  const [routerList] = useState<PathItem[]>([
    {
      path: '/xlb_tms/roleManage/index',
      name: '/roleManage/index',
    },
    {
      path: '/xlb_tms/roleManage/add',
      name: '/roleManage/add',
    },
    {
      path: '/xlb_sms/chart',
      name: '/xlb_sms/chart',
    },
    {
      path: '/xlb_sms/setting',
      name: '/xlb_sms/setting',
    },
  ]);

  return (
    <main style={{ display: 'flex' }}>
      <div>
        <Button
          onClick={() => {
            dropScope('/chart');
          }}
        >
          卸载 chart
        </Button>
      </div>
      <main style={{ display: 'flex' }}>
        {routerList.map((i: PathItem) => {
          return (
            <Button
              size={'small'}
              key={i.path}
              style={{ margin: '15px' }}
              onClick={() => navigate(i.path)}
            >
              {i.name}
            </Button>
          );
        })}
      </main>
    </main>
  );
}
