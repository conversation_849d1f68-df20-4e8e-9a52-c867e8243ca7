import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { XlbTableColumnProps } from '@xlb/components';

export const Columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: COLUMN_WIDTH_ESUM.INDEX,
    align: 'center',
  },
  {
    name: '供应商',
    code: 'supplier_id',
    width: 130,
    features: { sortable: true, details: true },
    render(text, record) {
      return record?.supplier_name;
    },
  },
  {
    name: '所属组织',
    code: 'org_id',
    width: 130,
    features: { sortable: true },
    render(text, record) {
      return record?.org_name;
    },
  },
  {
    name: '所属门店',
    code: 'store_id',
    width: 130,
    features: { sortable: false },
    render(text, record) {
      return record?.stores?.map((v: any) => v.store_name).join(',');
    },
  },
  {
    name: '交货周期',
    code: 'purchase_period_day',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '交货开始期限',
    code: 'start_date',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '交货结束期限',
    code: 'end_date',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '最近更新人',
    code: 'update_by',
    width: 130,
    features: { sortable: true },
  },
  {
    name: '最近更新时间',
    code: 'update_time',
    width: 150,
    features: { sortable: true },
  },
];
