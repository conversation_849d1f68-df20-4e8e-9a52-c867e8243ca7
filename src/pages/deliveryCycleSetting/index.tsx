import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer, XlbTipsModal } from '@xlb/components';
import { FC } from 'react';
import { Columns } from './data';

const Index: FC = () => {
  // TODO总计
  return (
    <XlbProPageContainer
      searchFieldProps={{
        formList: [
          { id: ScmFieldKeyMap.supplierIds },
          { id: ScmFieldKeyMap?.selectStoreIds },
        ],
      }}
      tableFieldProps={{
        url: '/scm/hxl.scm.purchaseperiod.page',
        tableColumn: Columns,
        primaryKey: 'id',
        selectMode: 'multiple',
        immediatePost: false,
      }}
      addFieldProps={{
        url: hasAuth(['交货周期设置', '编辑'])
          ? '/scm/hxl.scm.purchaseperiod.save'
          : undefined,
      }}
      deleteFieldProps={{
        url: hasAuth(['交货周期设置', '删除'])
          ? '/scm/hxl.scm.purchaseperiod.batchdelete'
          : undefined,
        name: '删除',
        beforeTips: () => {
          return XlbTipsModal({
            tips: `是否删除所选中商品？`,
            isCancel: true,
            onOk: () => {
              return true;
            },
          });
        },
      }}
      details={{
        primaryKey: 'id',
        mode: 'modal',
        isCancel: true,
        hiddenSaveBtn: true,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>;
        },
        itemSpan: 24,
        queryFieldProps: {
          url: '/scm/hxl.scm.purchaseperiod.read',
          params: (row: any) => {
            return {
              ...row,
            };
          },
          afterPost(data) {
            return {
              ...data,
              store_ids: data?.stores?.map((v: any) => v.store_id),
            };
          },
        },
        saveFieldProps: {
          url: hasAuth(['交货周期设置', '编辑'])
            ? '/scm/hxl.scm.purchaseperiod.save'
            : '',
        },
        updateFieldProps: {
          url: hasAuth(['交货周期设置', '编辑'])
            ? '/scm/hxl.scm.purchaseperiod.update'
            : '',
        },
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ScmFieldKeyMap.scmSupplierId,
                  label: '供应商',
                  name: 'supplier_id',
                  itemSpan: 24,
                  onChange: (e, form, options) => {
                    form?.setFieldsValue({
                      supplier_name: options?.[0]?.name ?? null,
                    });
                  },
                  rules: [{ required: true, message: '请选择供应商' }],
                },
                {
                  id: ScmFieldKeyMap?.scmOrgIds,
                  name: 'org_id',
                  label: '所属组织',
                  itemSpan: 24,
                  fieldProps: {
                    mode: undefined,
                  },
                  onChange: (e, form, options) => {
                    form?.setFieldsValue({
                      org_name: options?.label ?? null,
                    });
                  },
                  rules: [{ required: true, message: '请选择所属组织' }],
                },
                {
                  id: ScmFieldKeyMap.selectStoreIds,
                  label: '门店',
                  name: 'store_ids',
                  itemSpan: 24,
                  rules: [{ required: true, message: '请选择门店' }],
                },
                {
                  id: ScmFieldKeyMap.scmInputNumber,
                  label: '交货周期(天)',
                  name: 'purchase_period_day',
                  itemSpan: 24,
                  rules: [{ required: true, message: '请填写交货周期' }],
                  fieldProps: {
                    min: 1,
                    precision: 0,
                  },
                },
                {
                  id: ScmFieldKeyMap.billDate,
                  name: 'start_date',
                  label: '交货开始日期',
                  itemSpan: 24,
                  fieldProps: {
                    picker: 'date',
                    resultFormat: 'YYYY-MM-DD',
                    format: 'YYYY-MM-DD',
                  },
                  rules: [{ required: true, message: '请选择交货开始日期' }],
                },
                {
                  id: ScmFieldKeyMap.billDate,
                  name: 'end_date',
                  label: '交货结束日期',
                  itemSpan: 24,
                  fieldProps: {
                    picker: 'date',
                    resultFormat: 'YYYY-MM-DD',
                    format: 'YYYY-MM-DD',
                  },
                  rules: [{ required: true, message: '请选择交货结束日期' }],
                },
              ],
            },
          },
        ],
      }}
    />
  );
};
export default Index;
