import { XlbProPageContainer, XlbTableColumnProps } from '@xlb/components';
import { XlbTipsModal } from '@xlb/components/dist/components';
import { message, Space } from 'antd';
import dayjs from 'dayjs';
import { FC } from 'react';
import { typeArry } from './data';
// import { XlbFetch } from '@/utils';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import XlbFetch from '@/utils/XlbFetch';
import QRCode from 'qrcode.react';
const Index: FC = () => {
  // <QRCode id={'key'} value={url} />;
  const onQrclick = (recode: any) => {
    XlbTipsModal({
      title: '盲测投票二维码',
      bordered: true,
      width: 500,
      tips: (
        <div style={{ padding: 16 }}>
          <QRCode
            id={'key'}
            value={`https://pga-test.xlbsoft.com?fid=${recode?.fid}&vote_type=${'BLIND'}&source=${'SCM'}`}
          />
          <p>打开微信扫一扫</p>
        </div>
      ),
      // onOk: ()=>{
      //   onChange(list.map(item=>({id: item.id, name: item.name, pid: item.id, approver_type: item.approver_type})))
      // },
      isCancel: true,
      style: { textAlign: 'center' },
    });
  };
  const danganColumns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      align: 'center',
    },
    {
      name: '单据号',
      code: 'fid',
      width: 190,
      features: { details: true },
    },
    {
      name: '标题',
      code: 'title',
      width: 130,
      features: {},
    },
    {
      name: '状态',
      code: 'state',
      width: 130,
      // features: { sortable: true },
      render(text, record, index) {
        const obj = typeArry.find((item) => item.value === text);
        return <div className={obj?.type}>{obj?.label}</div>;
      },
    },
    {
      name: '创建人',
      code: 'create_by',
      width: 130,
      features: {},
    },
    {
      name: '创建时间',
      code: 'create_time',
      width: 120,
      // features: { sortable: true },
    },
    {
      name: '操作',
      code: 'quantity',
      features: {},
      width: 300,
      render(text, record, index: any) {
        return (
          <Space
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {record.state !== 'FINISH' && (
              <div
                style={{ color: '#3D66FE' }}
                onClick={() => {
                  onQrclick(record);
                }}
              >
                二维码
              </div>
            )}
            {record.state !== 'FINISH' && (
              <div
                style={{ color: '#3D66FE' }}
                onClick={() => {
                  return XlbTipsModal({
                    tips: `是否对${record?.fid}终止投票`,
                    isCancel: true,
                    onOk: async () => {
                      const res = await XlbFetch(
                        '/scm/hxl.scm.blindvoteorder.update',
                        {
                          fid: record.fid,
                          state: 'FINISH',
                        },
                      );
                      if (res?.code === 0) {
                        index?.fetchData();
                        message.success('操作成功');
                      }
                      // console.log(record);
                    },
                  });
                }}
              >
                投票终止
              </div>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <>
      <XlbProPageContainer
        details={{
          primaryKey: 'fid',
          // readOnly: true,
          //   hiddenSaveBtn: true,
          //   isCancel: true,
          queryFieldProps: {
            url: '/scm/hxl.scm.blindvoteorder.read',
          },
          // 里面的保存按钮
          saveFieldProps: {
            url: hasAuth(['盲测投票', '编辑'])
              ? '/scm/hxl.scm.blindvoteorder.save'
              : '',
            hidden(formValues) {
              return formValues?.fid;
            },
          },
          formList: [
            {
              componentType: 'blueBar',
              fieldProps: { title: '基本信息' },
              children: (formValues) => {
                return [
                  {
                    componentType: 'form',
                    fieldProps: {
                      width: '80%',
                      formList: [
                        {
                          label: '标题',
                          id: 'commonInput',
                          name: 'title',
                          itemSpan: 24,
                          rules: [{ required: true, message: '请输入标题' }],
                          readOnly: (val) => {
                            return formValues?.fid;
                          },
                        },
                        {
                          id: ScmFieldKeyMap.fromList,
                          name: 'basic_infos',
                          readOnly: (val) => {
                            return formValues?.fid;
                          },
                          render(formValues) {
                            console.log(formValues, 'formValues');
                            return (
                              <div>
                                {formValues?.basic_infos?.map((item) => (
                                  <div>{item?.name}</div>
                                ))}
                              </div>
                            );
                          },
                        },
                      ],
                    },
                  },
                ];
              },
            },
            {
              componentType: 'blueBar',
              fieldProps: { title: '投票信息' },
              children: (formValues) => {
                return [
                  {
                    componentType: 'customer',
                    render: () => {
                      return (
                        <div>
                          <span>投票人数：{formValues?.number}</span>
                          {formValues?.basic_infos?.map((item) => (
                            <span style={{ marginLeft: 20 }} key={item?.name}>
                              {item?.name}：{item?.rate}
                            </span>
                          ))}
                        </div>
                      );
                    },
                  },
                  {
                    componentType: 'table',
                    name: 'vote_infos',
                    fieldProps: {
                      columns: [
                        {
                          code: '_index',
                          name: '序号',
                          width: 100,
                          features: {
                            sortable: true,
                          },
                          align: 'center',
                        },
                        {
                          code: 'name',
                          name: '投票结果',
                          width: 500,
                          features: {
                            sortable: true,
                          },
                        },
                        {
                          code: 'create_time',
                          name: '投票时间',
                          width: 500,
                          features: {
                            sortable: true,
                          },
                        },
                      ],
                    },
                  },
                ];
              },
              hidden(formValues) {
                // console.log(formValues, 'formValues');
                return !formValues?.vote_infos?.length;
              },
              dependencies: ['vote_infos', 'basic_infos'],
            },
          ],
        }}
        searchFieldProps={{
          formList: (formValues) => {
            let temp = [
              { id: 'dateCommon', label: '日期范围', name: 'create_date' },
              {
                label: '状态',
                id: 'commonSelect',
                name: 'state',
                fieldProps: {
                  options: typeArry,
                },
                // onChange(e, form) {
                //   form?.setFieldsValue({ ids: undefined });
                // },
              },
              { label: '关键字', id: 'keyword' },
              //   { id: 'company', label: '所属公司', fieldProps: { mode: 'multiple' }, name: 'company_ids' },
            ];
            return temp;
          },
          initialValues: {
            // query_model: 'ITEM',
            create_date: [
              dayjs().startOf('month').format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.blindvoteorder.page',
          tableColumn: danganColumns,
          immediatePost: true,
          selectMode: 'single',
          primaryKey: 'fid',
        }}
        // 外面的新增按钮
        addFieldProps={{
          name: '新增',
          url: hasAuth(['盲测投票', '编辑'])
            ? '/scm/hxl.scm.blindvoteorder.save'
            : '',
        }}
        exportFieldProps={{
          url: hasAuth(['盲测投票', '导出'])
            ? '/scm/hxl.scm.blindvoteorder.export'
            : '',
          fileName: '盲测投票.xlsx',
        }}
        // extra={(context) => {
        //   console.log(context, 'context');
        //   return (
        //     <>
        //       {hasAuth(['盲测投票', '导出']) ? (
        //         <XlbDropdownButton
        //           // @ts-ignore
        //           disabled={!context.selectRow.length}
        //           trigger="click"
        //           dropList={[{ label: '处理通过' }, { label: '处理拒绝' }]}
        //           dropdownItemClick={(index) => {
        //             // console.log(index);
        //             handleResults(index, context);
        //           }}
        //           label={'处理'}
        //         ></XlbDropdownButton>
        //       ) : null}
        //     </>
        //   );
        // }}
      />
    </>
  );
};
export default Index;
