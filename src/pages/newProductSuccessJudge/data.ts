export const TIME_TYPE = [
  { label: '创建时间', value: 'create_date' },
  { label: '审核时间', value: 'audit_date' },
];
export enum StateType {
  auditing = 'PURCHASING_MANAGER_AUDIT_ING',
  auditRefuse = 'PURCHASING_MANAGER_AUDIT_REJECTION',
  handling = 'PURCHASING_DIRECTOR_AUDIT_ING',
  handleRefuse = 'PURCHASING_DIRECTOR_AUDIT_REJECTION',
  end = 'END',
}
export const STATE_TYPE = [
  {
    label: '采购经理审核中',
    value: StateType.auditing,
    color: 'warning',
  },
  {
    label: '采购经理拒绝',
    value: StateType.auditRefuse,
    color: 'danger',
  },
  {
    label: '采购总监处理中',
    value: StateType.handling,
    color: 'warning',
  },
  {
    label: '采购总监拒绝',
    value: StateType.handleRefuse,
    color: 'danger',
  },
  {
    label: '已结束',
    value: StateType.end,
    color: 'danger',
  },
];
