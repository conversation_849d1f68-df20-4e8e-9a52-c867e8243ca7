import Container from '@/components/indexContainer';
import { MAX_INT } from '@/constants/common';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import {
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbProPageContainer,
  XlbSelect,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import OperateDrawer from './component/operateDrawer';
import { STATE_TYPE, StateType, TIME_TYPE } from './data';
import style from './index.less';
import {
  AuditType,
  getDeliveryCenter,
  newItemSuccessAudit,
  newItemSuccessExport,
  recordfind,
} from './server';

const Index = () => {
  const { TextArea } = XlbInput;

  // 操作记录
  const [operateVisible, setOperateVisible] = useState(false);
  const [recordData, setRecordData] = useState([]);
  const viewOperateRecord = async (e: any, row: any) => {
    e.stopPropagation();
    const res = await recordfind({ fid: row.fid });
    if (res.code === 0) {
      setOperateVisible(true);
      setRecordData(res.data);
    }
  };
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 80,
      align: 'center',
    },
    {
      name: '单据号',
      code: 'fid',
      width: 175,
    },
    {
      name: '商品名称',
      code: 'name',
      width: 175,
    },
    {
      name: '商品代码',
      code: 'code',
      width: 175,
    },
    {
      name: '商品条码',
      code: 'bar_code',
      width: 175,
    },
    {
      name: '商品销量PSD',
      code: 'psd_num',
      width: 175,
    },
    {
      name: '商品金额PSD',
      code: 'psd_money',
      width: 175,
    },
    {
      name: '品类中位销量PSD',
      code: 'psd_median_num',
      width: 175,
    },
    {
      name: '品类中位金额PSD',
      code: 'psd_median_money',
      width: 175,
    },
    {
      name: '状态',
      code: 'state',
      width: 175,
      render: (text: string) => (
        <span>
          {STATE_TYPE.find((item) => item.value === text)?.label || ''}
        </span>
      ),
    },
    {
      name: '创建时间',
      code: 'create_time',
      width: 175,
      features: { format: 'TIME' },
    },
    {
      name: '操作',
      code: 'operate',
      width: 175,
      align: 'center',
      render: (_text: string, record: any) => (
        <div
          className={style.clickable}
          onClick={(e) => viewOperateRecord(e, record)}
        >
          操作记录
        </div>
      ),
    },
  ];

  const [storeList, setStoreList] = useState<any[]>([]);
  const getStoreList = async () => {
    const res = await getDeliveryCenter({
      page_number: 0,
      page_size: MAX_INT,
      center_flag: true,
      store_house_filter: true,
      wait_assign: false,
    });
    if (res.code === 0) {
      const formatList = res.data?.content?.map((item: any) => ({
        ...item,
        label: item.store_name,
        value: item.id,
      }));
      setStoreList(formatList);
    }
  };
  useEffect(() => {
    getStoreList();
  }, []);

  // 导出
  const exportItem = async (requestForm: any) => {
    const res = await newItemSuccessExport(requestForm);
    if (res.code === 0) {
      message.success(res.data);
    }
  };
  // 审核
  enum OperateType {
    resolve = '通过',
    reject = '拒绝',
  }
  interface OperateParams {
    operateType: OperateType;
    rows: any[];
    fetchData: any;
    auditType: AuditType;
  }
  const [auditForm] = XlbBasicForm.useForm();
  const auditItem = async ({
    operateType,
    rows,
    fetchData,
    auditType,
  }: OperateParams) => {
    if (operateType === OperateType.reject) {
      await XlbTipsModal({
        title: '审核结果',
        width: 500,
        tips: (
          <XlbBasicForm form={auditForm} className={style.auditForm}>
            <XlbBasicForm.Item
              label="需停购的配送中心"
              name="store_ids"
              rules={[{ required: true, message: '请至少选择一个配送中心' }]}
            >
              <XlbSelect width={294} mode="multiple" options={storeList} />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              label="备注"
              name="Memo"
              rules={[{ required: true, message: '请输入备注' }]}
            >
              <TextArea style={{ width: 294, minHeight: 100 }} autoSize />
            </XlbBasicForm.Item>
          </XlbBasicForm>
        ),
        onCancel: () => auditForm.resetFields(),
        isCancel: true,
        onOkBeforeFunction: async () => {
          return auditForm
            .validateFields()
            .then(async () => {
              const allstoreIds = storeList.map((item) => item.value);
              const exclude_store_ids = allstoreIds.filter(
                (item) => !auditForm.getFieldValue('store_ids')?.includes(item),
              );
              const res = await newItemSuccessAudit({
                audit_type: auditType,
                fid: rows?.[0]?.fid,
                if_pass: false,
                memo: auditForm.getFieldValue('Memo'),
                org_and_store: auditForm
                  .getFieldValue('store_ids')
                  ?.map((item: number) => ({
                    erp_store_id: item,
                  })),
                exclude_store_ids,
              });
              if (res.code === 0) {
                message.success('操作成功');
                fetchData();
                auditForm.resetFields();
                return true;
              }
              return false;
            })
            .catch(() => false);
        },
      });
      return;
    }
    const res = await newItemSuccessAudit({
      audit_type: auditType,
      fid: rows?.[0]?.fid,
      if_pass: true,
    });
    if (res.code === 0) {
      message.success('操作成功');
      fetchData();
    }
  };

  return (
    <Container>
      <XlbProPageContainer
        searchFieldProps={{
          formList: [
            {
              id: 'bizday',
              format: 'YYYY-MM-DD',
              label: '日期范围',
              name: 'create_date',
            },
            {
              id: 'timeType',
              name: 'time_type',
              label: '时间类型',
              fieldProps: {
                options: TIME_TYPE,
              },
            },
            {
              id: ScmFieldKeyMap?.scmItemIds,
              label: '商品档案',
              name: 'item_ids',
            },
            {
              label: '状态',
              name: 'state',
              id: 'commonSelect',
              fieldProps: {
                options: STATE_TYPE,
              },
            },
            'keyword',
          ],
          initialValues: {
            time_type: 'create_date',
            create_date: [
              dayjs().startOf('day').format('YYYY-MM-DD'),
              dayjs().endOf('day').format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.newitemsuccessdetermination.page',
          tableColumn,
          selectMode: 'single',
          immediatePost: true,
        }}
        extra={(context) => {
          const { fetchData, selectRow, requestForm } = context;
          return (
            <XlbButton.Group>
              {hasAuth(['新品成功判定', '审核']) && (
                <XlbDropdownButton
                  icon={<XlbIcon name="shenhe" size={16} />}
                  disabled={
                    selectRow?.length !== 1 ||
                    selectRow?.some((item) => item.state !== StateType.auditing)
                  }
                  dropList={[
                    { label: '审核通过', value: OperateType.resolve },
                    { label: '审核拒绝', value: OperateType.reject },
                  ]}
                  dropdownItemClick={(_index, item) => {
                    if (!item) return;
                    auditItem({
                      operateType: item.value as OperateType,
                      rows: selectRow || [],
                      fetchData,
                      auditType: AuditType.purchasingManager,
                    });
                  }}
                  label="审核"
                />
              )}
              {hasAuth(['新品成功判定', '审核']) && (
                <XlbDropdownButton
                  icon={<XlbIcon name="shenhe" size={16} />}
                  disabled={
                    selectRow?.length !== 1 ||
                    selectRow?.some((item) => item.state !== StateType.handling)
                  }
                  dropList={[
                    { label: '处理通过', value: OperateType.resolve },
                    { label: '处理拒绝', value: OperateType.reject },
                  ]}
                  dropdownItemClick={(_index, item) => {
                    if (!item) return;
                    auditItem({
                      operateType: item.value as OperateType,
                      rows: selectRow || [],
                      fetchData,
                      auditType: AuditType.purchasingDirector,
                    });
                  }}
                  label="处理"
                />
              )}
              {hasAuth(['新品成功判定', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  icon={<XlbIcon name="daochu" />}
                  onClick={() => exportItem(requestForm)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      />
      {/* 操作记录 */}
      <OperateDrawer
        data={recordData}
        visible={operateVisible}
        handleVisible={setOperateVisible}
      />
    </Container>
  );
};

export default Index;
