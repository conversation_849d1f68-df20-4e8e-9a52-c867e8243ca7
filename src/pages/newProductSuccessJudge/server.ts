import { default as XlbFetch } from '@/utils/XlbFetch';

// 导出
export const newItemSuccessExport = async (data: any) => {
  return await XlbFetch(
    `/scm/hxl.scm.newitemsuccessdetermination.exports`,
    data,
  );
};

// 查找操作记录
export const recordfind = async (data: { fid: string }) => {
  return await XlbFetch(
    `/scm/hxl.scm.newitemsuccessdetermination.recordfind`,
    data,
  );
};

export enum AuditType {
  purchasingManager = 1,
  purchasingDirector,
}
interface OrgAndStoreItem {
  erp_store_id: number;
  org_id: number;
  org_name: string;
  store_name: string;
}
interface AuditParams {
  audit_type: AuditType;
  fid: string;
  if_pass: boolean;
  memo?: string;
  org_and_store?: OrgAndStoreItem[];
  exclude_store_ids?: number[];
}
// 审核
export const newItemSuccessAudit = async (data: AuditParams) => {
  return await XlbFetch(`/scm/hxl.scm.newitemsuccessdetermination.audit`, data);
};

// 配送中心
export const getDeliveryCenter = async (data: any) => {
  return await XlbFetch(`/erp/hxl.erp.store.short.page`, data);
};
