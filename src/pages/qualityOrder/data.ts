export const stateEmail = [
  { label: '制单', value: 'INIT', type: 'info' },
  { label: '审核中', value: 'AUDITING', type: 'warning' },
  { label: '处理中', value: 'HANDLING', type: 'warning' },
  { label: '审核拒绝', value: 'AUDIT_REJECT', type: 'danger' },
  { label: '处理通过', value: 'HANDLE_PASS', type: 'success' },
  { label: '处理拒绝', value: 'HANDLE_REJECT', type: 'danger' },
  { label: '已撤回', value: 'ROLLBACK', type: 'invalid' },
]

export const operation = [
  { value: '提交审核', type: 'primary' },
  { value: '撤销', type: 'danger' },
  { value: '审核通过', type: 'success' },
  { value: '审核拒绝', type: 'danger' },
  { value: '处理通过', type: 'success' },
  { value: '处理拒绝', type: 'danger' },
]

export const channelEmail = [
  { label: '400', value: 'TEL' },
  { label: '抖音', value: 'TIKTOK' },
  { label: '公众号', value: 'PUBLIC' },
]

export const qualityTypeEmail = [
  { label: '客诉', value: 'CUSTOMER' },
  { label: '自检自查', value: 'SELF' },
]

export const filesEmail = [
  { label: 'problem', value: 'PROBLEM', name: '问题商品照片' },
  { label: 'product', value: 'PRODUCT', name: '生产日期照片' },
  { label: 'destory', value: 'DESTORY', name: '问题销毁照片' },
  { label: 'problem_item', value: 'PROBLEM_ITEM', name: '问题附件' },
  { label: 'handle_attachments', value: 'HANDLE_ATTACHMENTS', name: '附件' },
]

export const handleTypeEmail = [
  { label: '罚款', value: 'FINE' },
  { label: '赔付', value: 'COMPENSATION' },
  { label: '整改', value: 'RECTIFICATION' },
]

