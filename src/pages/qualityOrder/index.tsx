import { ScmFieldKeyMap } from '@/constants/config/scm';
import { LStorage } from '@/utils/storage';
import { XlbProPageContainer, XlbTableColumnProps } from '@xlb/components';
import dayjs from 'dayjs';
import { type FC } from 'react';
import { filesEmail, qualityTypeEmail } from './data';

const Index: FC = () => {
  const userInfo = LStorage.get('userInfo');
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 70,
      align: 'center',
      lock: true,
    },
    {
      name: '门店',
      code: 'store_id',
      width: 144,
      features: { sortable: true },
      render(text, record, index) {
        return record.store_name;
      },
    },
    {
      name: '供应商',
      code: 'supplier_id',
      width: 116,
      features: { sortable: true },
      render(text, record, index) {
        return record.supplier_name;
      },
    },

    {
      name: '商品名称',
      code: 'item_id',
      width: 256,
      features: { sortable: true },
      render(text, record, index) {
        return record.item_name;
      },
    },

    {
      name: '生产日期',
      code: 'productor_date',
      width: 102,
      features: { sortable: true },
    },
    {
      name: '生产批次',
      code: 'batch_number',
      width: 102,
      features: { sortable: true },
    },

    {
      name: '上游配送中心',
      code: 'upstream_center_name',
      width: 116,
      features: { sortable: false },
    },

    {
      name: '质量类型',
      code: 'quality_type',
      width: 88,
      features: { sortable: true },
      render: (value: any) => (
        <div>
          {qualityTypeEmail.find((item) => item.value === value)?.label}
        </div>
      ),
    },
    {
      name: '问题类型',
      code: 'problem_id',
      width: 116,
      features: { sortable: true },
      render(text, record, index) {
        return record?.problem_name;
      },
    },
    {
      name: '一级类目问题',
      code: 'one_category_id',
      width: 116,
      features: { sortable: true },
      render(text, record, index) {
        return record?.one_category_name;
      },
    },

    {
      name: '通知时间',
      code: 'create_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '操作',
      code: 'option',
      width: 88,
      align: 'center',
      features: { details: true },
      render: (_, record: any) => {
        return '详情';
      },
    },
  ];

  return (
    <>
      <XlbProPageContainer
        details={{
          refreshAndClose: false, // 点击保存不关闭
          primaryKey: 'fid',
          readOnly: true,
          mode: 'page',
          queryFieldProps: {
            url: '/fsms/hxl.fsms.qualityreport.read',
            params: (row: any) => {
              return {
                fid: row?.fid,
                state: 'INIT',
                quality_type: row?.quality_type || 'SELF',
                create_time: row?.create_time || dayjs().format('YYYY-MM-DD'),
              };
            },
            afterPost(data) {
              const val = { ...data, handle_memo: data?.handle_memo || ' ' };
              if (val?.files?.length) {
                val.files.map((item: any) => {
                  const obj = filesEmail.find((i) => i.value == item.ref_type);
                  if (!obj) return undefined;
                  val[obj.label] = item?.files;
                });
              }
              return val;
            },
          },
          formList: [
            {
              componentType: 'blueBar',
              fieldProps: { title: '基本信息' },
              children: [
                {
                  componentType: 'form',

                  fieldProps: {
                    width: '100%',
                    readOnly: true,
                    formList: [
                      {
                        label: '',
                        name: 'state',
                        id: 'commonInput',
                        hidden: true,
                      },
                      {
                        label: '质量类型',
                        name: 'quality_type',
                        id: 'commonSelect',
                        options: qualityTypeEmail,
                        rules: [
                          { required: true, message: '质量类型不能为空' },
                        ],
                      },
                      {
                        label: '门店',
                        id: ScmFieldKeyMap?.scmStoreIds,
                        name: 'store_id',
                        rules: [{ required: true, message: '门店不能为空' }],
                      },

                      {
                        label: '上游配送中心',
                        id: 'commonInput',
                        name: 'upstream_center_name',
                        rules: [{ required: true }],
                        disabled: true,
                      },
                      {
                        label: '提报日期',
                        id: 'createTime',
                        name: 'create_time',
                        rules: [{ required: true }],
                        fieldProps: { format: 'YYYY-MM-DD' },
                        disabled: true,
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'blueBar',
              fieldProps: { title: '商品信息' },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    readOnly: true,
                    formList: [
                      {
                        label: '问题类型',
                        name: 'problem_name',
                        id: 'commonInput',
                        // rules: [{ required: true, message: '问题类型不能为空' }]
                      },
                      {
                        label: '一级类目问题',
                        name: 'one_category_name',
                        id: 'commonInput',
                      },

                      {
                        label: '商品名称',
                        name: 'item_name',
                        id: 'commonInput',
                      },

                      {
                        label: '产地',
                        name: 'origin_place',
                        id: 'commonInput',
                        disabled: true,
                        rules: [{ required: true }],
                      },
                      {
                        label: '商品数量',
                        name: 'item_quantity',
                        id: 'commonInputNumber',
                        disabled: true,
                        rules: [{ required: true, message: '请输入商品数量' }],
                      },
                      {
                        label: '生产日期',
                        name: 'productor_date',
                        id: 'createTime',
                        fieldProps: {
                          format: 'YYYY-MM-DD',
                          disabledDate: (date) => date && date.isAfter(dayjs()),
                        },
                      },
                      {
                        label: '产品批次',
                        name: 'batch_number',
                        id: 'commonInput',
                      },
                      {
                        label: '问题商品照片',
                        name: 'problem',
                        id: ScmFieldKeyMap.scmCommonUpload,
                        fieldProps: {
                          buttonModal: true,
                          mode: 'look',
                          accept: ['image'],
                          data: {
                            refId: 'TEMP1',
                            refType: filesEmail.find(
                              (i) => i.label === 'problem',
                            )?.value,
                          },
                        },
                      },
                      {
                        label: '生产日期照片',
                        name: 'product',
                        id: ScmFieldKeyMap.scmCommonUpload,
                        fieldProps: {
                          buttonModal: true,
                          mode: 'look',
                          accept: ['image'],
                          data: {
                            refId: 'TEMP2',
                            refType: filesEmail.find(
                              (i) => i.label === 'product',
                            )?.value,
                          },
                        },
                      },
                      {
                        label: '问题销毁照片',
                        name: 'destory',
                        id: ScmFieldKeyMap.scmCommonUpload,
                        fieldProps: {
                          buttonModal: true,
                          mode: 'look',
                          accept: ['image'],
                          data: {
                            refId: 'TEMP3',
                            refType: filesEmail.find(
                              (i) => i.label === 'destory',
                            )?.value,
                          },
                        },
                      },

                      {
                        label: '问题详细描述',
                        name: 'detail_memo',
                        itemSpan: 8,
                        id: 'commoneTextArea',
                        fieldProps: { autoSize: { minRows: 2, maxRows: 4 } },
                        rules: [
                          { required: true, message: '问题详细描述不能为空' },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'blueBar',
              fieldProps: { title: '相关人信息' },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    formList: [
                      {
                        label: '发现人姓名',
                        name: 'find_by',
                        id: 'commonInput',
                      },

                      {
                        label: '奖励',
                        name: 'reward_money',
                        id: 'commonInputNumber',
                        fieldProps: { suffix: '元' },
                      },
                    ],
                  },
                },
              ],
            },
          ],
        }}
        searchFieldProps={{
          formList: [
            {
              id: 'dateCommon',
              name: 'date',
            },
            {
              label: '商品档案',
              id: ScmFieldKeyMap.scmItemIds,
              name: 'item_ids',
              fieldProps: {
                dialogParams: {
                  type: 'goods',
                  url: '/erp/hxl.erp.item.supplier.page',
                  dataType: 'lists',
                  isLeftColumn: true,
                  isMultiple: true,
                  data: {
                    enabled: true,
                  },
                },
              },
            },
            {
              id: ScmFieldKeyMap.supplierIds,
              name: 'supplier_ids',
              label: '供应商',
              fieldProps: {
                dialogParams: {
                  type: 'supplier',
                  dataType: 'lists',
                  isMultiple: true,
                },
              },
            },
            {
              id: ScmFieldKeyMap.DETAILPROBLEM,
              name: 'problem_id',
              label: '问题类型',
            },
          ],
          initialValues: {
            date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
            quality_type: 'SELF',
            state: 'HANDLE_PASS',
            query_source: 'SCM',
            supplier_ids: userInfo?.supplier
              ? [userInfo?.supplier?.id]
              : undefined,
          },
        }}
        tableFieldProps={{
          url: '/fsms/hxl.fsms.qualityreport.page',
          tableColumn: tableList,
          immediatePost: true,
          selectMode: 'single',
          primaryKey: 'fid',
        }}
      />
    </>
  );
};

export default Index;
