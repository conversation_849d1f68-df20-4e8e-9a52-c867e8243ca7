import { LStorage } from '@/utils/storage';
import { XlbTableColumnProps } from '@xlb/components';

export const TIME_TYPE = [
  {
    label: '制单时间',
    value: 0,
  },
  { label: '过会时间', value: 1 },
  { label: '食安审核时间', value: 2 },
];

export const SUPPLIER_ATTRIBUTES = [
  { label: '贸易商', value: 'TRADER' },
  { label: '生产商', value: 'PRODUCER' },
];

export const ITEM_SOURCE = [
  { label: '新商新品', value: 'NEW_SUPPLIER' },
  { label: '老商提报', value: 'OLD_SUPPLIER' },
];

export const PROCESS_TYPE = [
  { label: '自生产', value: 'SELF_PRODUCE' },
  { label: '代理商', value: 'AGENT' },
];

export const payment_method = [
  {
    label: '货到票到',
    value: 'CASH_ON_DELIVERY',
  },
  {
    label: '预付款',
    value: 'PREPAYMENT',
  },
];

export const Retail_method = [
  {
    label: '散称',
    value: 0,
  },
  {
    label: '条码',
    value: 1,
  },
];

export const SALES_CHANNELS = [
  {
    label: '便利店',
    value: 'CONVENIENCE_STORE',
  },
  {
    label: '卖场',
    value: 'MALL',
  },
  {
    label: '零食系统',
    value: 'SNACK_SYSTEM',
  },
  {
    label: '特殊渠道',
    value: 'SPECIAL_CHANNEL',
  },
  {
    label: '线上平台',
    value: 'ONLINE_PLATFORM',
  },
];

//货到票到、预付款
export const PAY_TYPE = [
  {
    label: '货到票到',
    value: 'CASH_ON_DELIVERY',
  },
  {
    label: '预付款',
    value: 'PREPAYMENT',
  },
];

export const CHANNEL_TYPE = [
  {
    label: '招募平台',
    value: 'HIRE_SPHERE',
  },
  { label: '新零帮', value: 'XLB' },
];

export const ItemByBoolean = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

export const SUPPLIER_STATE = [
  { label: '未关联', value: 0, color: 'info' },
  { label: '部分关联', value: 1, color: 'info' },
  { label: '全部关联', value: 2, color: 'info' },
];
export enum FoodSafeType {
  noStart = 'NO_START',
  waitAudit = 'WAIT_AUDIT',
  audit = 'AUDIT',
  reject = 'REJECT',
  oppose = 'OPPOSE',
}
export const FOOD_SAFE_TYPE = Object.freeze([
  { label: '未开始', value: FoodSafeType.noStart, color: 'info' },
  { label: '待审核', value: FoodSafeType.waitAudit, color: 'warning' },
  { label: '审核通过', value: FoodSafeType.audit, color: 'success' },
  { label: '审核拒绝', value: FoodSafeType.reject, color: 'danger' },
  { label: '驳回', value: FoodSafeType.oppose, color: 'danger' },
]);

export const CERTIFICATE_MAINTAINED = {
  label: '证件信息维护中',
  value: 'CERTIFICATE_MAINTAINED',
  color: 'warning',
};

//初审中、初审拒绝、品评中、品评拒绝、二次谈判中、采购总监审核中、审核拒绝、集团商品中心处理中、处理通过、处理拒绝、食安批复中、批复通过、批复拒绝
export const STATE_TYPE = [
  { label: '制单', value: 'INIT', color: 'info' },
  { label: '初审中', value: 'REVIEW_AUDIT_ING', color: 'warning' },
  { label: '初审拒绝', value: 'FIRST_REVIEW_REJECT', color: 'danger' },
  { label: '初审驳回', value: 'REVIEW_AUDIT_OPPOSE', color: 'danger' },
  { label: '品评中', value: 'APPRAISE_AUDIT_ING', color: 'warning' },
  { label: '品评拒绝', value: 'APPRAISE_AUDIT_REJECT', color: 'danger' },
  { label: '二次谈判中', value: 'NEGOTIATE_AUDIT_ING', color: 'warning' },
  { label: '二次谈判拒绝', value: 'NEGOTIATE_AUDIT_REJECT', color: 'danger' },

  { label: '已终止', value: 'TERMINATE', color: 'danger' },

  {
    label: '公司采购总监审核中',
    value: 'COMPANY_CPO_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团品类经理审核中',
    value: 'GROUP_PRODUCT_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团采购总监审核中',
    value: 'PURCHASING_DIRECTOR_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团采购总监审核通过',
    value: 'PURCHASING_DIRECTOR_AUDIT_PASS',
    color: 'success',
  },
  {
    label: '集团总经理审核中',
    value: 'GROUP_MANAGER_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团总经理审核拒绝',
    value: 'GROUP_MANAGER_AUDIT_REJECT',
    color: 'danger',
  },
  // {
  //   label: '公司商品主档审核中',
  //   value: 'COMPANY_ITEM_AUDIT_ING',
  //   color: 'warning',
  // },
  // {
  //   label: '公司商品主档审核拒绝',
  //   value: 'COMPANY_ITEM_AUDIT_REJECT',
  //   color: 'danger',
  // },
  {
    label: '集团商品主档审核中',
    value: 'GROUP_ITEM_AUDIT_ING',
    color: 'warning',
  },
  {
    label: '集团商品主档审核拒绝',
    value: 'GROUP_ITEM_AUDIT_REJECT',
    color: 'danger',
  },
  { label: '审核通过', value: 'AUDIT_PASS', color: 'success' },

  { label: '已结束', value: 'END', color: 'info' },
];

const getIndex = STATE_TYPE.findIndex(
  (item) => item.value === 'APPRAISE_AUDIT_ING',
);
const SUPPLIER_STATE_TYPE = [
  ...STATE_TYPE.slice(0, getIndex),
  CERTIFICATE_MAINTAINED,
  ...STATE_TYPE.slice(getIndex, STATE_TYPE.length),
];
const isSupplier = () => !!LStorage.get('userInfo')?.supplier;

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: 175,
    features: { sortable: true, details: true },
    lock: true,
  },
  {
    name: '公司',
    code: 'org_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'apply_item_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品档案',
    code: 'item_name',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '商品品类',
    code: 'item_public_category_name',
    width: 175,
    features: { sortable: true },
    render: (text, record) => {
      return <span>{record?.item_info?.item_public_category_name}</span>;
    },
  },
  {
    name: '品类经理',
    code: 'category_manager_name',
    width: 175,
    features: { sortable: true },
    render: (text, record) => {
      return <span>{record?.item_info?.category_manager_name}</span>;
    },
  },
  {
    name: '渠道',
    code: 'apply_mode',
    width: 175,
    features: { sortable: true },
    render: (text) => {
      return (
        <span>{CHANNEL_TYPE.find((item) => item.value == text)?.label}</span>
      );
    },
  },
  {
    name: '供应商准入',
    code: 'supplier_access',
    width: 175,
    features: { sortable: true },
    render: (text) => {
      return (
        <span>{ItemByBoolean.find((item) => item.value == text)?.label}</span>
      );
    },
  },
  {
    name: '关联生产商状态',
    code: 'associated_supplier_code',
    width: 175,
    render: (text) => {
      return (
        <span>{SUPPLIER_STATE.find((item) => item.value == text)?.label}</span>
      );
    },
  },
  {
    name: '过会日期',
    code: 'meetings_date',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '状态',
    code: 'state',
    width: 175,
    features: { sortable: true },
    render: (text) => {
      return (
        <span
          className={
            SUPPLIER_STATE_TYPE.find((item) => item.value === text)?.color
          }
        >
          {SUPPLIER_STATE_TYPE.find((item) => item.value === text)?.label}
        </span>
      );
    },
  },
  {
    name: '终止原因',
    code: 'terminate_reason',
    width: 240,
    features: { sortable: true },
  },
  {
    name: '食安审核意见',
    code: 'food_safe_audit_memo',
    width: 240,
    features: { sortable: true },
  },
  {
    name: '食安审核状态',
    code: 'food_safe_audit_state',
    width: 175,
    features: { sortable: true },
    render: (text) => {
      return (
        <span
          className={FOOD_SAFE_TYPE.find((item) => item.value === text)?.color}
        >
          {FOOD_SAFE_TYPE.find((item) => item.value === text)?.label}
        </span>
      );
    },
  },
  {
    name: '提交人',
    code: 'submit_by',
    width: 175,
    features: { sortable: true },
  },
  {
    name: '创建时间',
    code: 'create_time',
    width: 175,
    features: { sortable: true },
  },
];

// 核算方式
export const countType = [
  {
    label: '移动加权平均',
    value: '移动加权平均',
  },
  {
    label: '中心手工批次',
    value: '中心手工批次',
  },
];
// 采购范围
export const buyLimits = [
  {
    label: '不限',
    value: '不限',
  },
  {
    label: '总部购配',
    value: '总部购配',
  },
  {
    label: '门店采购',
    value: '门店采购',
  },
];
// 商品类型
export const goodsType = [
  {
    label: '标准商品',
    value: 'STANDARD',
    title: '标准',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
    title: '组合',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
    title: '成分',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
    title: '制单',
  },
  {
    label: '分级商品',
    value: 'GRADING',
    title: '分级',
  },
];

// 类型
export const ITEM_TYPE = [
  { label: '按比例', value: 0 },
  { label: '按金额', value: 1 },
  { label: '固定金额', value: 2 },
];

// 临期提醒
export const EXPIRE_TYPE = [
  {
    label: '按天数',
    value: 0,
  },
  {
    label: '按比例',
    value: 1,
  },
];

// 收货规则
export const RECEIVE_RULE_TYPE = [
  {
    label: '按天数',
    value: 0,
  },
  {
    label: '按比例',
    value: 1,
  },
];
// 日期录入规则
export const makeDate = [
  {
    label: '无日期',
    value: 0,
  },
  {
    label: '生产日期',
    value: 1,
  },
  {
    label: '到期日期',
    value: 2,
  },
];
// 税率
export const rateList = [
  {
    label: '9',
    value: 9,
  },
  {
    label: '13',
    value: 13,
  },
];
// 进项税率
export const inputRateList = [
  {
    label: '9',
    value: 9,
  },
  {
    label: '13',
    value: 13,
  },
  {
    label: '1',
    value: 1,
  },
  {
    label: '3',
    value: 3,
  },
  {
    label: '6',
    value: 6,
  },
  {
    label: '0',
    value: 0,
  },
];
// 入库抽检
export const inCheckList = [
  {
    label: '免检',
    value: 0,
  },
  {
    label: '普通检验',
    value: 1,
  },
  {
    label: '重点检验',
    value: 2,
  },
];

// 保质期
export const dayOrMohth = [
  {
    label: '按天数',
    value: 1,
  },
  {
    label: '按月份',
    value: 2,
  },
];

export const voteFormList = [
  {
    code: '_index',
    name: '序号',
    features: {
      sortable: true,
    },
  },
  {
    code: 'wx_nickname',
    name: '微信昵称',
  },
  {
    code: 'vote_time',
    name: '投票时间',
  },
  {
    code: 'look_like',
    name: '包装颜值',
  },
  {
    code: 'taste_like',
    name: '口感风味',
  },
  {
    code: 'price_like',
    name: '价格',
  },
  {
    code: 'final_like',
    name: '通过评定',
  },
];

export const ITEM_ATTRIBUTE = [
  // {
  //   label: '正常',
  //   value: 'normal',
  // },
  {
    label: '停购',
    value: 'stop_purchase',
  },
  {
    label: '停售',
    value: 'stop_sale',
  },
  {
    label: '停止要货',
    value: 'stop_request',
  },
  {
    label: '停止批发',
    value: 'stop_wholesale',
  },
  {
    label: '是否积分',
    value: 'point',
  },
  {
    label: '是否称重',
    value: 'weigh',
  },
  {
    label: '预定',
    value: 'reserve',
  },
  {
    label: '强配',
    value: 'force_deliver',
  },
  {
    label: '直配',
    value: 'direct_delivery',
  },
  {
    label: '是否新品',
    value: 'open_news_cycle',
  },
  {
    label: '前台折扣',
    value: 'pos_discount',
  },
  {
    label: '前台议价',
    value: 'pos_bargain',
  },
  {
    label: '拆零',
    value: 'demolition_state',
  },
  {
    label: '必卖品',
    value: 'must_sell',
  },
  {
    label: '外箱码',
    value: 'package_bar_code',
    disabled: true,
  },
];

export const deliveryTypeMap = {
  0: 'delivery_type_ratio',
  1: 'delivery_type_price',
  2: 'delivery_price',
};
export const wholesaleTypeMap = {
  0: 'wholesale_type_ratio',
  1: 'wholesale_type_price',
  2: 'wholesale_price',
};
export const centerExpireTypeMap = {
  0: 'center_expire_type_num',
  1: 'center_expire_type_ratio',
};
export const storeExpireTypeMap = {
  0: 'store_expire_type_num',
  1: 'store_expire_type_ratio',
};

export const searchFormList = [
  {
    id: 'bizday',
    name: 'create_date',
    label: '日期范围',
  },
  {
    id: 'timeType',
    name: 'time_type',
    label: '时间类型',
    fieldProps: {
      options: TIME_TYPE,
    },
  },
  {
    label: '公司',
    id: 'contactCompanys',
    name: 'org_id',
  },
  {
    id: 'commonSelect',
    name: 'apply_mode',
    label: '渠道',
    fieldProps: {
      options: CHANNEL_TYPE,
    },
  },
  {
    id: 'commonSelect',
    name: 'supplier_access_state',
    label: '供应商准入',
    fieldProps: {
      options: ItemByBoolean,
    },
  },
  {
    id: 'goodsCategoryId',
    name: 'item_public_category_ids',
    label: '商品品类',
    fieldProps: {
      mode: 'multiple',
    },
  },
  {
    label: '关联生产商状态',
    name: 'associated_supplier_code',
    id: 'commonSelect',
    fieldProps: {
      options: SUPPLIER_STATE,
    },
  },
  {
    id: 'purchaseManager',
    name: 'category_manager_id',
    label: '品类经理',
  },
  {
    label: '状态',
    name: 'states',
    id: 'commonSelect',
    fieldProps: {
      mode: 'multiple',
      options: isSupplier() ? SUPPLIER_STATE_TYPE : STATE_TYPE,
    },
  },
  {
    label: '食安审核状态',
    name: 'food_safe_audit_states',
    id: 'commonSelect',
    fieldProps: {
      mode: 'multiple',
      options: FOOD_SAFE_TYPE,
    },
  },
  'keyword',
];

export const timeTypeValueEnum = {
  0: 'create_date',
  1: 'meetings_date',
  2: 'food_safe_audit_date',
};

export const unitArr = [
  {
    name: '库存',
    unit: 'stock_unit',
    ratio: 'stock_ratio',
  },
  {
    name: '采购',
    unit: 'purchase_unit',
    ratio: 'purchase_ratio',
  },
  {
    name: '批发',
    unit: 'wholesale_unit',
    ratio: 'wholesale_ratio',
  },
  {
    name: '配送',
    unit: 'delivery_unit',
    ratio: 'delivery_ratio',
  },
];

export const purchaseType = [
  { label: '集采品', value: 'COLLECTIVE_PURCHASE' },
  { label: '集售品', value: 'COLLECTIVE_SALE' },
  { label: '地采品', value: 'GROUND_PURCHASE' },
  { label: '店采品', value: 'SHOP_PURCHASE' },
];

export const MONTHS_OPTION = [
  {
    label: '半年',
    value: 6,
  },
  {
    label: '1年',
    value: 12,
  },
  {
    label: '2年',
    value: 24,
  },
  {
    label: '3年',
    value: 36,
  },
];

// 非食商品id固定为14
export const NO_FOOD_CATEGORY_ID = 14;

export const EXPORT_TEMPLATE_TYPE = Object.freeze([
  {
    value: 'negotiateaudit',
    apply_mode: 'HIRE_SPHERE',
    state: 'NEGOTIATE_AUDIT_ING',
    templateName: '采购谈判导入模版.xlsx',
  },
  {
    value: 'negotiateaudit',
    apply_mode: 'XLB',
    state: 'NEGOTIATE_AUDIT_ING',
    templateName: '采购谈判导入模版.xlsx',
  },
  {
    value: 'hire_reviewaudit',
    apply_mode: 'HIRE_SPHERE',
    state: 'REVIEW_AUDIT_ING',
    templateName: '采购初审导入模板(招募平台).xlsx',
  },
  {
    value: 'xlb_reviewaudit',
    apply_mode: 'XLB',
    state: 'REVIEW_AUDIT_ING',
    templateName: '采购初审导入模板(新零帮).xlsx',
  },
]);

const colomnsHeader = [
  { code: 'store_code', name: '配送中心仓代码' },
  {
    code: 'store_name',
    name: '配送中心名称',
    width: 200,
  },
  {
    code: 'avg_psd',
    name: '（仓）店均销量PSD',
    width: 150,
    features: {
      sortable: true,
    },
  },
  {
    code: 'allocated_quantity',
    name: '店均统配量',
    width: 120,
    features: {
      sortable: true,
    },
  },
  {
    code: 'store_entry_rate',
    name: '进店率',
    width: 120,
    features: {
      sortable: true,
    },
  },
  {
    code: 'earliest_arrival_date',
    name: '最早到货日期',
    width: 200,
    features: {
      sortable: true,
    },
  },
  {
    code: 'org_name',
    name: '公司',
    width: 120,
  },
  {
    code: 'province',
    name: '省',
    width: 120,
    features: {
      sortable: true,
    },
  },
  {
    code: 'mid_psd',
    name: '仓中位销量PSD',
    width: 150,
    features: {
      sortable: true,
    },
  },
  {
    code: 'psd',
    name: '店均psd',
    width: 120,
    features: {
      sortable: true,
    },
  },
  {
    code: 'store_count',
    name: '仓统配门店数',
    width: 120,
    features: {
      sortable: true,
    },
  },
  {
    code: 'open_store_count',
    name: '仓营业门店数',
    width: 120,
    features: {
      sortable: true,
    },
  },
];
const newMap = new Map();
colomnsHeader.forEach((item) => {
  const code = item.code;
  const name = item.name;
  newMap.set(code, name);
});

export const colomnsHeaderMap = newMap;
