import { executiveStandardType } from '@/constants/config/data';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbModalForm,
  XlbProPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { getFirstWord } from '@xlb/utils';
import { message } from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import goodsInfoList from './component/goodInfoList';
import OperateDrawer from './component/operateDrawer';
import PreviewModal from './component/previewModal';
import procurementAuditList from './component/procurementAuditList';
import purchaseNegotiationList from './component/purchaseNegotiationList';
import qualityAuditList from './component/qualityAuditList';
import {
  centerExpireTypeMap,
  CERTIFICATE_MAINTAINED,
  deliveryTypeMap,
  EXPORT_TEMPLATE_TYPE,
  FoodSafeType,
  ITEM_ATTRIBUTE,
  searchFormList,
  storeExpireTypeMap,
  tableList,
  timeTypeValueEnum,
  unitArr,
  voteFormList,
  wholesaleTypeMap,
} from './data';
import './index.module.less';
import styles from './index.module.less';
import Api from './server';

const { TextArea } = XlbInput;
// 新品核签
const ProForm = () => {
  const [auditForm] = XlbBasicForm.useForm();
  const [visible, setVisible] = useState(false);
  const [operateVisible, setOperateVisible] = useState(false);
  const [perviewData, setPerviewData] = useState([]);
  const [recordData, setRecordData] = useState([]);
  const [orders, setOrders] = useState(null);
  const ref = useRef<any>();
  const userInfo = LStorage.get('userInfo');

  const fetchDataRef = useRef<any>(() => {});

  // 关联供应商
  const relatedSupplier = async (record: any) => {
    const list = await XlbBasicData({
      dataType: 'tree',
      type: 'supplier',
      data: {
        enabled: true,
      },
    });
    if (list?.length) {
      const params = {
        fid: record.fid,
        supplier_code: list[0].code,
        supplier_id: list[0].id,
        supplier_name: list[0].name,
        supplier_type: list[0].supplier_type,
      };
      const res = await Api.relatedSupplier(params);
      if (res?.code === 0) {
        fetchDataRef.current?.();
        message.success('关联成功');
      }
    }
  };
  const list = [
    ...tableList,
    {
      name: '操作',
      code: 'operate',
      width: 300,
      lock: true,
      render: (text: any, record: any) => {
        return (
          <XlbButton.Group>
            <XlbButton
              type="text"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                getData(record);
              }}
            >
              操作记录
            </XlbButton>
            <XlbModalForm
              formProps={{
                queryFieldProps: {
                  url: '/scm-mdm/hxl.scm.newitemapplyorder.voteresultfind',
                  params: { fid: record.fid },
                },
                primaryKey: 'fid',
                initialValues: {},
              }}
              title="投票结果"
              isCancel={true}
              width={800}
              formList={[
                {
                  componentType: 'form',
                  fieldProps: {
                    readOnly: true,
                    itemSpan: 8,
                    formList: [
                      {
                        id: 'commonInput',
                        name: 'vote_num',
                        label: '投票人数',
                        readOnly: true,
                      },
                      {
                        id: 'commonInput',
                        name: 'vote_result',
                        label: '投票均分',
                        readOnly: true,
                      },
                    ],
                  },
                },
                {
                  componentType: 'table',
                  name: 'vote_record',
                  fieldProps: {
                    columns: voteFormList,
                    hideOnSinglePage: true,
                    height: '400px',
                  },
                },
              ]}
            >
              <XlbButton type="text" size="small">
                投票结果
              </XlbButton>
            </XlbModalForm>
            {record?.supplier_access === false && (
              <XlbButton
                type="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  applyDown(record);
                }}
              >
                供应商准入
              </XlbButton>
            )}
            {!record?.supplier_access && (
              <XlbButton
                type="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  relatedSupplier(record);
                }}
              >
                关联供应商
              </XlbButton>
            )}
          </XlbButton.Group>
        );
      },
    },
  ];

  const getData = async (data: any) => {
    const res = await Api.recordfind({
      fid: data.fid,
    });
    setOperateVisible(true);
    if (res.code === 0) {
      setRecordData(res.data);
    }
  };

  const utf8ToBase64 = (str: any) => {
    return window.btoa(
      encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (match, p1) {
        return String.fromCharCode('0x' + p1);
      }),
    );
  };

  // 复制
  const applyDown = (record: any) => {
    const company_id = window.btoa(`${LStorage.get('userInfo').company_id}`);
    const fid = utf8ToBase64(`${record?.fid || ''}`);
    const name = utf8ToBase64(`${record?.supplier_name || ''}`);
    const supplier_type = utf8ToBase64(
      `${record?.item_info?.supplier_type || ''}`,
    );
    const contact = utf8ToBase64(`${record?.item_info?.contact || ''}`);
    const phone = utf8ToBase64(`${record?.item_info?.phone || ''}`);
    copy(
      `${process.env.PUBLIC_URL}/join/supplierAccessPhone?${company_id}&&${fid}&&${name}&&${supplier_type}&&${contact}&&${phone}`,
    );
    message.success('复制成功');
  };

  // 审核拒绝前保存表单数据
  const handleBeforeSubmit = (form: any) => {
    // 审核拒绝前调用保存接口需提前，否则保存失败
    return new Promise((resolve) => {
      handleSubmit(form, 'refuse');
      setTimeout(() => {
        resolve(true);
      }, 1000);
    });
  };

  const handleAudit = async (
    form: any,
    type: any,
    value: any,
    fetchData: any,
    onClose: any,
    exartstate?: string,
  ) => {
    if (type == 'pass') {
      let params = {
        fid: value.fid,
        if_pass: true,
      };
      let res;
      //采购总监审核
      if (value.state == 'COMPANY_CPO_AUDIT_ING') {
        res = await Api.cpoaudit(params);
      }
      if (res?.code === 0) {
        message.success('操作成功');
        fetchData();
        onClose();
      }
    } else {
      await XlbTipsModal({
        title: '备注',
        keyboard: false,
        tips: (
          <XlbBasicForm form={auditForm}>
            <XlbBasicForm.Item
              label="备注"
              name={'Memo'}
              rules={[{ required: true, message: '请输入备注' }]}
            >
              <TextArea style={{ width: 294, height: 100 }} />
            </XlbBasicForm.Item>
          </XlbBasicForm>
        ),
        onCancel: () => auditForm.setFieldValue('Memo', null),
        onOkBeforeFunction: async () => {
          // 审核拒绝保存数据
          if (type === 'refuse') await handleBeforeSubmit(form);
          return auditForm
            .validateFields()
            .then(async () => {
              let params = {
                fid: value.fid,
                if_pass: false,
                memo: auditForm.getFieldValue('Memo'),
              };
              let res;
              if (
                value.food_safe_audit_state === FoodSafeType.waitAudit &&
                value?.isFoodSafe
              ) {
                // 食安审核驳回
                if (type === 'oppose') {
                  res = await Api.companyfoodsafeoppose(params);
                } else {
                  //食安审核拒绝
                  res = await Api.companyfoodsafeaudit(params);
                }
              } else {
                // 初审驳回
                if (value.state == 'REVIEW_AUDIT_ING' && exartstate) {
                  res = await Api.reviewoppose(params);
                }
                // 初审拒绝
                if (value.state == 'REVIEW_AUDIT_ING' && !exartstate) {
                  res = await Api.reviewAudit(params);
                }
                //品评拒绝
                if (value.state == 'APPRAISE_AUDIT_ING') {
                  res = await Api.appraiseaudit(params);
                }
                //二次谈判
                if (value.state == 'NEGOTIATE_AUDIT_ING') {
                  res = await Api.negotiateaudit(params);
                }
                //采购总监审核
                if (value.state == 'COMPANY_CPO_AUDIT_ING') {
                  res = await Api.cpoaudit(params);
                }
                //公司商品主档审核
                if (value.state == 'COMPANY_ITEM_AUDIT_ING') {
                  res = await Api.companyitemaudit(params);
                }
              }
              if (res?.code === 0) {
                message.success('操作成功');
                auditForm.setFieldValue('Memo', null);
                fetchData();
                onClose();
                return true;
              }
            })
            .catch(() => {
              return false;
            });
        },
      });
    }
  };
  const isFoodSafeAudit = useRef<boolean>(false);

  const negotiateStop = async (value: any, fetchData: any, onClose: any) => {
    await XlbTipsModal({
      title: '终止原因',
      keyboard: false,
      tips: (
        <XlbBasicForm form={auditForm}>
          <XlbBasicForm.Item label="终止原因" name={'terminate_reason'}>
            <TextArea style={{ width: 294, height: 100 }} />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      onCancel: () => auditForm.setFieldValue('terminate_reason', null),
      onOkBeforeFunction: async () => {
        return auditForm
          .validateFields()
          .then(async () => {
            let params = {
              fid: value.fid,
              terminate_reason: auditForm.getFieldValue('terminate_reason'),
            };
            let res;
            // 初审驳回
            if (value.state == 'NEGOTIATE_AUDIT_ING') {
              res = await Api.negotiateterminate(params);
              if (res?.code === 0) {
                message.success('操作成功');
                auditForm.setFieldValue('terminate_reason', null);
                onClose();
                return true;
              }
            }
          })
          .catch(() => {
            return false;
          });
      },
    });
  };

  const [submitLoading, setSubmitLoading] = useState(false);
  const handleSubmit = (form: any, type: string, isFoodSafe = false) => {
    console.log('===============form:form:form:', form);
    form.submit();
    ref.current = type;
    isFoodSafeAudit.current = isFoodSafe;
  };

  //反审核
  const handleReAudit = async (values: any, fetchData: any, onClose: any) => {
    const res = await Api.reappraiseaudit({ fid: values?.fid });
    if (res?.code == 0) {
      message.success('操作成功');
      fetchData();
      onClose();
    }
  };

  //撤销
  const handleCpoaudit = async (values: any, fetchData: any, onClose: any) => {
    const res = await Api.withdrawcpoaudit({ fid: values?.fid });
    if (res?.code == 0) {
      message.success('操作成功');
      fetchData();
      onClose();
    }
  };
  const getPreviewData = async (selectRow: any) => {
    if (selectRow?.length == 0) {
      message.warning('请选择数据');
      return;
    }
    const fids = selectRow?.map((item: any) => {
      return item.fid;
    });
    const org_ids = selectRow?.map((item: any) => item.org_id);
    const res = await Api.previewItem({ fids: fids, orders, org_ids });
    if (res?.code == 0) {
      setPerviewData(res?.data);
      setVisible(true);
    }
  };
  // 导入
  const handleImport = async (values: any, fetchData: any) => {
    const importItem = EXPORT_TEMPLATE_TYPE.find(
      (item) =>
        item.apply_mode === values.apply_mode && item.state === values.state,
    );
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/scm/hxl.scm.newitemapplyorder.${values.state === 'REVIEW_AUDIT_ING' ? 'reviewaudit' : 'negotiateaudit'}.import`,
      templateUrl: `${process.env.BASE_URL}/scm/hxl.scm.file.download.${importItem?.value}`,
      templateName: importItem?.templateName,
      params: { fid: values.fid },
    });
    if (res.code === 0) {
      fetchData();
    }
  };
  // 导出
  const handleExport = async (values: any) => {
    const handleApi =
      values.state === 'REVIEW_AUDIT_ING'
        ? Api.reviewauditExport
        : Api.negotiateauditExport;
    const res = await handleApi({ fid: values.fid });
    if (res.code === 0) {
      message.success(res.data);
    }
  };
  return (
    <div className={styles.newProductSign}>
      <PreviewModal
        data={perviewData}
        visible={visible}
        handleVisible={(val: any) => {
          setVisible(val);
        }}
      />
      <OperateDrawer
        data={recordData}
        visible={operateVisible}
        handleVisible={(val: any) => {
          setOperateVisible(val);
        }}
      ></OperateDrawer>
      <XlbProPageContainer
        timeTypeValueEnum={timeTypeValueEnum}
        searchFieldProps={{
          formList: searchFormList,
          initialValues: {
            time_type: 0,
            Data_Compact_RangeType_create_date: 'month',
            create_date: [
              dayjs().startOf('month').format('YYYY-MM-DD'),
              dayjs().endOf('month').format('YYYY-MM-DD'),
            ],
          },
        }}
        tableFieldProps={{
          url: '/scm-mdm/hxl.scm.newitemapplyorder.page',
          tableColumn: list,
          selectMode: 'multiple',
          immediatePost: true,
          keepDataSource: false,
          onChange: (params: any) => {
            setOrders(params?.orders);
          },
        }}
        addFieldProps={{
          name: '新建',
          url: hasAuth(['新品申请', '编辑'])
            ? `/scm-mdm/hxl.scm.newitemapplyorder.save`
            : '',
        }}
        exportFieldProps={{
          url: hasAuth(['新品申请', '导出'])
            ? '/scm-mdm/hxl.scm.newitemapplyorder.export'
            : '',
          fileName: '新品核签',
        }}
        extra={(context: any) => {
          const { fetchData, selectRow, formValues } = context || {};
          const { state } = formValues || {};
          fetchDataRef.current = fetchData;
          return (
            <XlbButton
              label="预览"
              type="primary"
              icon={<XlbIcon name="sousuo" />}
              onClick={(e) => {
                e.stopPropagation();
                getPreviewData(selectRow);
              }}
            />
          );
        }}
        details={{
          isCancel: true,
          primaryKey: 'fid',
          hiddenDomValues: true,
          itemSpan: 8,
          onFinish: async (name, info, actions, oldFormValues) => {
            let orglist = [];
            const orgRes = await Api.getOrgList();
            console.log('!!!!!!orglist', orgRes);
            if (orgRes?.code === 0) {
              orglist = JSON.parse(JSON.stringify(orgRes.data));
            }

            let itemV = true;
            let reviewAuditV = true;
            let appraiseAuditV = true;
            let negotiateAuditV = true;
            //审核按钮校验必填
            if (ref.current == 'audit') {
              if (
                !oldFormValues?.fid ||
                oldFormValues?.state == 'INIT' ||
                oldFormValues?.state == CERTIFICATE_MAINTAINED.value || // 证件信息维护中
                oldFormValues?.state == 'REVIEW_AUDIT_ING' ||
                // oldFormValues?.food_safe_audit_state ===
                //   FoodSafeType.waitAudit
                //   ||
                oldFormValues.state == 'REVIEW_AUDIT_OPPOSE' || //  初审驳回
                oldFormValues?.food_safe_audit_state === FoodSafeType.oppose // 食安状态是驳回
                // ||
                // (oldFormValues?.state == 'NEGOTIATE_AUDIT_ING' &&
                //   oldFormValues?.apply_mode !== 'HIRE_SPHERE')
              ) {
                itemV = await info?.forms?.item_info?.validateFields();
                if (!itemV) {
                  return;
                }
              }

              if (oldFormValues.state == 'REVIEW_AUDIT_ING') {
                reviewAuditV =
                  await info?.forms?.review_audit_info?.validateFields();
                if (!reviewAuditV) {
                  return;
                }
              }
              if (oldFormValues.state == 'APPRAISE_AUDIT_ING') {
                appraiseAuditV =
                  await info?.forms?.appraise_audit_info?.validateFields();
                if (!appraiseAuditV) {
                  return;
                }
              }
              if (
                oldFormValues.state == 'NEGOTIATE_AUDIT_ING' ||
                oldFormValues.state == 'COMPANY_ITEM_AUDIT_ING'
              ) {
                negotiateAuditV =
                  await info?.forms?.negotiate_audit_info?.validateFields();
                if (!negotiateAuditV) {
                  return;
                }
              }
            }
            const { fetchData, onClose } = actions;
            let negotiate_params: { [key: string]: any } = {};
            const item_info = info?.forms?.item_info?.getFieldsValue(true);
            const orgs = JSON.parse(JSON.stringify(orglist));
            orglist.forEach((e) => {
              if (e.id === item_info.org_id) {
                item_info.org_name = e.name;
              }
            });

            const review_audit_info =
              info?.forms?.review_audit_info?.getFieldsValue(true);
            const appraise_audit_info =
              info?.forms?.appraise_audit_info?.getFieldsValue(true);
            const negotiate_audit_info =
              info?.forms?.negotiate_audit_info?.getFieldsValue(true);
            if (
              ref.current == 'audit' &&
              oldFormValues.state == 'REVIEW_AUDIT_ING'
            ) {
              if (
                review_audit_info?.eliminate &&
                !review_audit_info?.replace_items?.length
              ) {
                message.warning('请选择替换商品');
                return;
              }
            }

            if (
              ref.current == 'evaluate' &&
              oldFormValues.state == 'APPRAISE_AUDIT_ING'
            ) {
              if (!appraise_audit_info?.org_and_store?.length) {
                message.warning('请选择配送中心');
                return;
              }
            }
            if (
              ref.current == 'audit' &&
              (oldFormValues.state == 'NEGOTIATE_AUDIT_ING' ||
                oldFormValues.state == 'COMPANY_ITEM_AUDIT_ING')
            ) {
              for (const key in unitArr) {
                if (
                  negotiate_audit_info.unit ===
                    negotiate_audit_info[unitArr[key].unit] &&
                  Number(negotiate_audit_info[unitArr[key].ratio]) !== 1
                ) {
                  XlbTipsModal({
                    tips: `${unitArr[key].name}单位换算率不合法，请检查！`,
                  });
                  return;
                }
                if (
                  unitArr.some(
                    (v) =>
                      negotiate_audit_info[v.unit] &&
                      negotiate_audit_info[v.unit] ===
                        negotiate_audit_info[unitArr[key].unit] &&
                      Number(negotiate_audit_info[v.ratio]) !==
                        Number(negotiate_audit_info[unitArr[key].ratio]),
                  )
                ) {
                  XlbTipsModal({ tips: '单位一样，换算率必须一致！' });
                  return;
                }
              }

              // if (
              //   negotiate_audit_info?.initial_order_size_per_shop %
              //     negotiate_audit_info?.delivery_ratio !==
              //   0
              // ) {
              //   message.warning('单店首单率需为配送换算率整数倍');
              //   return;
              // }

              if (!negotiate_audit_info?.purchase_price) {
                message.warning('采购价必填且大于0');
                return;
              }
              if (!negotiate_audit_info?.delivery_type_item) {
                message.warning('配送价必填且大于0');
                return;
              }
              if (!negotiate_audit_info?.wholesale_type_item) {
                message.warning('批发价必填且大于0');
                return;
              }

              const sale_max_price = Number(
                negotiate_audit_info?.sale_max_price,
              );
              const sale_min_price = Number(
                negotiate_audit_info?.sale_min_price,
              );
              const sale_price = Number(negotiate_audit_info?.sale_price);
              const sale_price_s = Number(negotiate_audit_info?.sale_price_s);
              const sale_price_t = Number(negotiate_audit_info?.sale_price_t);
              const sale_price_f = Number(negotiate_audit_info?.sale_price_f);
              // 校验价格异常规则： 最高价不为0，有一个大于最高价。最低价不为0，有一个小于最低价 。
              const arr = [
                sale_price,
                sale_price_s,
                sale_price_t,
                sale_price_f,
              ];
              const maxLimit = arr.some(
                (v, i) =>
                  ((i !== 0 && v != 0) || i === 0) && v > sale_max_price,
              );
              const minLimit = arr.some(
                (v, i) =>
                  ((i !== 0 && v != 0) || i === 0) && v < sale_min_price,
              );
              if (
                (sale_max_price != 0 && maxLimit) ||
                (sale_min_price != 0 && minLimit) ||
                (sale_min_price != 0 &&
                  sale_max_price != 0 &&
                  (minLimit || maxLimit))
              ) {
                //最高价和最低价都不为0
                XlbTipsModal({
                  tips: '零售价设置与最高售价、最低售价存在冲突，请检查！',
                });
                return;
              }
            }

            if (
              oldFormValues.state == 'NEGOTIATE_AUDIT_ING' ||
              oldFormValues.state == 'COMPANY_ITEM_AUDIT_ING'
            ) {
              ITEM_ATTRIBUTE?.map((v) => v.value).forEach((v) => {
                negotiate_params[v] =
                  negotiate_audit_info?.check_list?.includes(v);
              });

              if (negotiate_audit_info) {
                const deliveryTypeKey =
                  deliveryTypeMap[
                    negotiate_audit_info?.delivery_type as keyof typeof deliveryTypeMap
                  ];

                if (deliveryTypeKey) {
                  negotiate_params = {
                    ...negotiate_params,
                    [deliveryTypeKey]: negotiate_audit_info?.delivery_type_item,
                  };
                }

                const wholesaleTypeKey =
                  wholesaleTypeMap[
                    negotiate_audit_info?.wholesale_type as keyof typeof wholesaleTypeMap
                  ];
                if (wholesaleTypeKey) {
                  negotiate_params = {
                    ...negotiate_params,
                    [wholesaleTypeKey]:
                      negotiate_audit_info.wholesale_type_item,
                  };
                }
                //中心临期提醒
                const centerExpireTypeKey =
                  centerExpireTypeMap[
                    negotiate_audit_info?.center_expire_type as keyof typeof centerExpireTypeMap
                  ];
                if (centerExpireTypeKey) {
                  negotiate_params = {
                    ...negotiate_params,
                    [centerExpireTypeKey]:
                      negotiate_audit_info.center_expire_type_item,
                  };
                }

                //门店临期提醒
                const storeExpireTypeKey =
                  storeExpireTypeMap[
                    negotiate_audit_info?.store_expire_type as keyof typeof storeExpireTypeMap
                  ];
                if (storeExpireTypeKey) {
                  negotiate_params = {
                    ...negotiate_params,
                    [storeExpireTypeKey]:
                      negotiate_audit_info.store_expire_type_item,
                  };
                }
              }
            }
            const {
              item_name,
              supplier_id,
              supplier_name,
              org_id,
              org_name,
              shorthand_code,
              item_public_category_id,
              supplier_quality_record_data_list,
            } = item_info;

            const params = {
              item_info: {
                ...oldFormValues.item_info,
                ...item_info,
                shorthand_code:
                  shorthand_code || (item_name && getFirstWord(item_name)),
                supplier_quality_record_data_list:
                  supplier_quality_record_data_list?.map((item: any) => {
                    return {
                      ...item,
                      ...item?.supplier_item,
                      end_date:
                        item?.valid_date && item?.end_date
                          ? dayjs(item?.valid_date)
                              .add(item?.end_date, 'month')
                              .format('YYYY-MM-DD')
                          : null,
                    };
                  }),
              },
              review_audit_info,
              appraise_audit_info,
              apply_item_name: item_name,
              supplier_id:
                supplier_id || LStorage.get('userInfo')?.supplier?.id,
              supplier_name: supplier_name,
              org_id: org_id,
              org_name: org_name,
              item_public_category_id: item_public_category_id,
              item_category_id: review_audit_info?.item_category_id,
              item_dept_id: negotiate_audit_info?.item_dept_id,
              meetings_date: review_audit_info?.meetings_date,
              negotiate_audit_info: {
                ...negotiate_audit_info,
                ...negotiate_params,
                package_bar_code: negotiate_audit_info?.package_bar_code,
                delivery_price: negotiate_audit_info?.deliveryPrice,
                wholesale_price: negotiate_audit_info?.wholesalePrice,
              },
            };
            let res;
            if (ref.current == 'save' || ref.current == 'refuse') {
              setSubmitLoading(true);
              res = oldFormValues?.fid
                ? await Api.updateItem({
                    ...params,
                    state:
                      oldFormValues?.state === CERTIFICATE_MAINTAINED.value
                        ? CERTIFICATE_MAINTAINED.value
                        : void 0,
                    fid: oldFormValues?.fid,
                  })
                : await Api.saveItem(params);
            }
            //商品信息提交
            if (ref.current == 'audit') {
              if (oldFormValues?.state == 'INIT' || !oldFormValues?.fid) {
                setSubmitLoading(true);
                res = await Api.itemCommit({
                  ...params,
                  fid: oldFormValues?.fid,
                });
              }
              if (oldFormValues?.state == 'REVIEW_AUDIT_ING') {
                res = await Api.reviewAudit({
                  ...params,
                  if_pass: true,
                  fid: oldFormValues?.fid,
                });
              }
              //二次谈判
              if (oldFormValues?.state == 'NEGOTIATE_AUDIT_ING') {
                res = await Api.negotiateaudit({
                  ...params,
                  if_pass: true,
                  fid: oldFormValues?.fid,
                });
              }

              //公司食安审核
              if (
                oldFormValues?.food_safe_audit_state ===
                  FoodSafeType.waitAudit &&
                isFoodSafeAudit.current
              ) {
                res = await Api.companyfoodsafeaudit({
                  ...params,
                  if_pass: true,
                  fid: oldFormValues?.fid,
                });
              }
              //公司商品主档审核
              if (oldFormValues?.state == 'COMPANY_ITEM_AUDIT_ING') {
                res = await Api.companyitemaudit({
                  ...params,
                  if_pass: true,
                  fid: oldFormValues?.fid,
                });
              }
              //采购初审驳回
              if (oldFormValues?.state == 'REVIEW_AUDIT_OPPOSE') {
                res = await Api.reviewopposeSubmit({
                  ...params,
                  // if_pass: true,
                  fid: oldFormValues?.fid,
                });
              }
              //食安审核驳回
              if (
                oldFormValues?.food_safe_audit_state === FoodSafeType.oppose
              ) {
                res = await Api.companyfoodsafeopposeSubmit({
                  ...params,
                  // if_pass: true,
                  fid: oldFormValues?.fid,
                });
              }
              //证件信息维护-固定传参
              if (oldFormValues?.state === CERTIFICATE_MAINTAINED.value) {
                res = await Api.updateItem({
                  ...params,
                  fid: oldFormValues?.fid,
                  submit_type: 1,
                  state: CERTIFICATE_MAINTAINED.value,
                });
              }
            }
            //品评审核
            if (ref.current == 'evaluate') {
              res = await Api.appraiseaudit({
                ...params,
                if_pass: true,
                fid: oldFormValues?.fid,
              });
            }
            if (res?.code == 0) {
              fetchData?.();
              if (ref.current != 'refuse') {
                onClose?.();
                message.success('操作成功');
              }
            }
            setSubmitLoading(false);
          },
          readOnly: (formValues: any) => {
            return formValues.state == 'COMPANY_CPO_AUDIT_ING';
          },
          queryFieldProps: {
            url: `/scm-mdm/hxl.scm.newitemapplyorder.read`,
            params: (row: any) => row,
            afterPost: async (data) => {
              let options = [];
              let supplierInfo = [];
              let producerSupplierOptions = [];
              let producerSupplierResult;
              if (!options?.length) {
                const res = await Api.getUnit();
                if (res?.code == 0) {
                  options = res?.data?.map((item: any) => ({
                    label: item.item_unit_name,
                    value: item.item_unit_name,
                  }));
                }
              }
              const supplierRes = await Api.supplierRead({
                id: userInfo?.supplier?.id,
              });

              if (supplierRes?.code == 0) {
                supplierInfo = supplierRes?.data;
              }

              // 非供应商的外检报告中生产商数据查询依赖当前单据的供应商id，配置做不到，需要在此处获取生产商下拉数据
              if (userInfo.user_type == 'SUPPLIER') {
                producerSupplierResult = await Api.supplierReadSupplier({
                  id: userInfo?.supplier?.id,
                });
              } else {
                if (data?.supplier_id) {
                  producerSupplierResult = await Api.readSupplier({
                    id: data?.supplier_id,
                  });
                }
              }

              if (producerSupplierResult?.code == 0) {
                if (producerSupplierResult.data?.supplier_type == 'PRODUCER') {
                  let list = [];
                  if (
                    Array.isArray(
                      producerSupplierResult.data
                        ?.supplier_association_producer_suppliers,
                    )
                  ) {
                    list =
                      producerSupplierResult.data?.supplier_association_producer_suppliers?.map(
                        (item: any) => {
                          return {
                            value: item?.detail_supplier_id,
                            label: item?.name,
                            origin_place: item?.origin_place,
                          };
                        },
                      );
                  }
                  producerSupplierOptions = [
                    ...list,
                    {
                      value: producerSupplierResult.data?.id,
                      label: producerSupplierResult.data?.name,
                      origin_place: producerSupplierResult.data?.origin_place,
                    },
                  ];
                } else if (
                  producerSupplierResult.data?.supplier_type == 'TRADER'
                ) {
                  if (
                    Array.isArray(
                      producerSupplierResult.data
                        ?.supplier_association_suppliers,
                    )
                  ) {
                    producerSupplierOptions =
                      producerSupplierResult.data?.supplier_association_suppliers?.map(
                        (item: any) => {
                          return {
                            value: item?.detail_supplier_id,
                            label: item?.name,
                            origin_place: item?.origin_place,
                          };
                        },
                      );
                  }
                }
              }

              if (Object.keys(data)?.length > 0) {
                const { negotiate_audit_info } = data;
                const wholesaleTypeItem =
                  negotiate_audit_info?.wholesale_type !== undefined &&
                  negotiate_audit_info[
                    wholesaleTypeMap[
                      negotiate_audit_info?.wholesale_type as keyof typeof wholesaleTypeMap
                    ]
                  ];

                const deliveryTypeItem =
                  negotiate_audit_info?.delivery_type !== undefined &&
                  negotiate_audit_info[
                    deliveryTypeMap[
                      negotiate_audit_info?.delivery_type as keyof typeof deliveryTypeMap
                    ]
                  ];

                const centerExpireTypeItem =
                  negotiate_audit_info?.center_expire_type !== undefined &&
                  negotiate_audit_info[
                    centerExpireTypeMap[
                      negotiate_audit_info?.center_expire_type as keyof typeof centerExpireTypeMap
                    ]
                  ];
                const storeExpireTypeItem =
                  negotiate_audit_info?.store_expire_type !== undefined &&
                  negotiate_audit_info[
                    storeExpireTypeMap[
                      negotiate_audit_info?.store_expire_type as keyof typeof storeExpireTypeMap
                    ]
                  ];
                let checkList: string[] = [];
                ITEM_ATTRIBUTE?.forEach((item) => {
                  if (negotiate_audit_info[item?.value] == true) {
                    checkList.push(item?.value);
                  }
                });

                //psd需要实时查询
                const { review_audit_info } = data;
                let replace_items_psd = review_audit_info?.replace_items;
                if (review_audit_info?.replace_items?.length > 0) {
                  const ids = review_audit_info?.replace_items?.map(
                    (item: any) => item.id,
                  );
                  const res = await Api.itemPsdFind({ item_ids: ids });
                  if (res?.code == 0) {
                    replace_items_psd = review_audit_info?.replace_items?.map(
                      (item: any) => {
                        return {
                          ...item,
                          sale_PSD:
                            res?.data?.find((i: any) => i?.item_id === item?.id)
                              ?.psd_money || 0,
                        };
                      },
                    );
                  }
                }

                return {
                  ...data,
                  item_info: {
                    ...data?.item_info,
                    executive_standard_type:
                      data?.item_info?.executive_standard_type ||
                      executiveStandardType.NATIONAL_STANDARDS, //执行标准默认国标
                    imports: data?.item_info?.imports ?? false,
                    is_nutritional_label:
                      !!data?.item_info?.is_nutritional_label,
                    supplier_quality_record_data_list:
                      data?.item_info?.supplier_quality_record_data_list?.map(
                        (item: any) => {
                          return {
                            ...item,
                            supplier_id: data.supplier_id,
                            state: data.state,
                            food_safe_audit_state: data.food_safe_audit_state,
                            supplier_item: {
                              producer_supplier_id: item.producer_supplier_id,
                              producer_supplier_name:
                                item.producer_supplier_name,
                              origin_place: item.origin_place,
                            },
                            end_date:
                              item?.valid_date && item?.end_date
                                ? dayjs(item.end_date).diff(
                                    dayjs(item.valid_date),
                                    'month',
                                  )
                                : null,
                          };
                        },
                      ),
                    options: options,
                    producerSupplierOptions: producerSupplierOptions,
                    fid: data?.fid,
                    state: data.state,
                    food_safe_audit_state: data.food_safe_audit_state,
                    org_id: data?.org_id,
                    supplier_id: data?.supplier_id,
                    supplier_name: data?.supplier_name,
                    item_public_category_id: data?.item_public_category_id,
                  },
                  review_audit_info: {
                    ...data?.review_audit_info,
                    meetings_date: data?.meetings_date,
                    item_category_id: data?.item_category_id,
                    state: data?.state,
                    food_safe_audit_state: data.food_safe_audit_state,
                    group_purchase_recommend:
                      data?.review_audit_info.group_purchase_recommend ?? false, //集采推荐
                    second_meeting:
                      data?.review_audit_info?.second_meeting ?? false, //二次过会
                    eliminate: data?.review_audit_info?.eliminate ?? false, //淘汰品
                    replace_items: replace_items_psd,
                    //采购初审自动取商品信息价格
                    first_round_negotiate_price:
                      data?.state == 'REVIEW_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.wholesale_price
                          : data?.item_info.purchase_price
                        : data?.review_audit_info.first_round_negotiate_price,
                  },

                  appraise_audit_info: {
                    ...data?.appraise_audit_info,
                    state: data.state,
                    food_safe_audit_state: data.food_safe_audit_state,
                  },

                  negotiate_audit_info: {
                    ...data?.negotiate_audit_info,
                    shorthand_code:
                      data?.negotiate_audit_info?.shorthand_code ||
                      (data.negotiate_audit_info.name &&
                        getFirstWord(data.negotiate_audit_info.name)),
                    wholesale_type_item: wholesaleTypeItem,
                    delivery_type_item: deliveryTypeItem,
                    center_expire_type_item: centerExpireTypeItem,
                    store_expire_type_item: storeExpireTypeItem,
                    check_list: checkList,
                    auto_generate_purchase:
                      data?.negotiate_audit_info?.auto_generate_purchase ??
                      false, //是否自动生成采购单
                    eliminate_stop_purchase:
                      data?.negotiate_audit_info?.eliminate_stop_purchase ??
                      false, //淘汰品自动停购
                    delivery_type:
                      data?.negotiate_audit_info?.delivery_type ?? 2, //配送价类型
                    wholesale_type:
                      data?.negotiate_audit_info?.wholesale_type ?? 2, //批发价类型
                    center_expire_type:
                      data?.negotiate_audit_info?.center_expire_type ?? 1, //中心临期提醒
                    store_expire_type:
                      data?.negotiate_audit_info?.store_expire_type ?? 1, //门店临期提醒
                    // input_tax_rate:
                    //   data?.negotiate_audit_info?.input_tax_rate ?? 9, //进项税率
                    output_tax_rate:
                      data?.negotiate_audit_info?.output_tax_rate ?? 9, //销项税率
                    expire_type: data?.negotiate_audit_info.expire_type ?? 1, //保质期
                    receive_rule_type:
                      data?.negotiate_audit_info?.receive_rule_type ?? 1, //收货规则
                    options: options,
                    state: data.state,
                    food_safe_audit_state: data.food_safe_audit_state,

                    //自动带出采购初审价格
                    final_round_negotiate_price:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.review_audit_info.first_round_negotiate_price
                        : data?.negotiate_audit_info
                            .final_round_negotiate_price,
                    //自动带出采购初审商品分类
                    item_category_id:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.review_audit_info.item_category_id
                        : data?.negotiate_audit_info.item_category_id,

                    //自动带出商品信息 采购长宽高
                    purchase_length:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.box_length
                          : data?.item_info.purchase_length
                        : data?.negotiate_audit_info.purchase_length,

                    purchase_width:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.box_width
                          : data?.item_info.purchase_width
                        : data?.negotiate_audit_info.purchase_width,

                    purchase_height:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.box_height
                          : data?.item_info.purchase_height
                        : data?.negotiate_audit_info.purchase_height,

                    //自动带出商品信息 基本长宽高
                    basic_length:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.package_length
                          : data?.item_info.retail_length
                        : data?.negotiate_audit_info.basic_length,

                    basic_width:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.package_width
                          : data?.item_info.retail_width
                        : data?.negotiate_audit_info.basic_width,

                    basic_height:
                      data?.state == 'NEGOTIATE_AUDIT_ING'
                        ? data?.apply_mode == 'HIRE_SPHERE'
                          ? data?.item_info.package_height
                          : data?.item_info.retail_height
                        : data?.negotiate_audit_info.basic_height,
                    tax_no: negotiate_audit_info?.tax_no
                      ? negotiate_audit_info?.tax_no
                      : data?.apply_mode !== 'HIRE_SPHERE'
                        ? data?.item_info?.item_tax_no
                        : data?.item_info?.tax_no,
                    input_tax_rate:
                      negotiate_audit_info?.tax_rate ||
                      negotiate_audit_info?.tax_rate == 0
                        ? negotiate_audit_info?.tax_rate
                        : data?.item_info?.tax_rate,
                  },
                };
              }
              return {
                ...data,
                item_info: {
                  enable_package_bar_code: true, //是否有外箱码
                  executive_standard_type:
                    executiveStandardType.NATIONAL_STANDARDS, //执行标准默认国标
                  expire_type: 1, //保质期
                  imports: data?.item_info?.imports ?? false,
                  is_nutritional_label: !!data?.item_info?.is_nutritional_label,
                  options: options,
                  producerSupplierOptions: producerSupplierOptions,
                  supplier_quality_record_data_list: [
                    {
                      supplier_item: {
                        producer_supplier_id:
                          supplierInfo?.supplier_type == 'PRODUCER'
                            ? userInfo?.supplier?.id
                            : undefined,
                        producer_supplier_name:
                          supplierInfo?.supplier_type == 'PRODUCER'
                            ? userInfo?.supplier?.name
                            : undefined,
                        origin_place:
                          supplierInfo?.supplier_type == 'PRODUCER' &&
                          userInfo?.supplier?.id
                            ? producerSupplierOptions?.find(
                                (item: any) =>
                                  item.value == userInfo?.supplier?.id,
                              )?.origin_place
                            : undefined,
                      },
                    },
                  ],
                },
              };
            },
          },
          extra: (context: any) => {
            const { fetchData, onClose, values, form, init } = context || {};
            return (
              <XlbButton.Group>
                {(!values?.fid ||
                  values?.state == 'INIT' ||
                  values?.state == CERTIFICATE_MAINTAINED.value ||
                  values?.state == 'REVIEW_AUDIT_ING' ||
                  values?.state == 'APPRAISE_AUDIT_ING' ||
                  (values?.state == 'REVIEW_AUDIT_OPPOSE' &&
                    hasAuth(['新品申请/采购初审驳回', '编辑'])) ||
                  values?.state == 'NEGOTIATE_AUDIT_ING' ||
                  (values.food_safe_audit_state === FoodSafeType.oppose &&
                    hasAuth(['新品申请/公司食安审核驳回', '编辑']))) &&
                  hasAuth(['新品申请', '编辑']) && (
                    <XlbButton
                      label="保存"
                      type="primary"
                      loading={submitLoading}
                      icon={<XlbIcon name="tijiao" />}
                      onClick={() => handleSubmit(form, 'save')}
                    />
                  )}
                {(values?.state == 'INIT' ||
                  !values?.fid ||
                  values?.state == CERTIFICATE_MAINTAINED.value ||
                  (values?.state == 'REVIEW_AUDIT_OPPOSE' &&
                    hasAuth(['新品申请/采购初审驳回', '编辑'])) ||
                  (values.food_safe_audit_state === FoodSafeType.oppose &&
                    hasAuth(['新品申请/公司食安审核驳回', '编辑']))) &&
                  hasAuth(['新品申请', '编辑']) && (
                    <XlbButton
                      label="提交"
                      type="primary"
                      loading={submitLoading}
                      icon={<XlbIcon name="tijiao" />}
                      onClick={() => handleSubmit(form, 'audit')}
                    />
                  )}
                {values?.state == 'REVIEW_AUDIT_ING' &&
                  hasAuth(['新品申请/采购初审', '审核']) && (
                    <XlbDropdownButton
                      icon={<XlbIcon name="shenhe" size={16} />}
                      dropList={
                        hasAuth(['新品申请/采购初审驳回', '审核'])
                          ? [
                              { label: '通过' },
                              { label: '拒绝' },
                              { label: '审核驳回' },
                            ]
                          : [{ label: '通过' }, { label: '拒绝' }]
                      }
                      dropdownItemClick={(index) => {
                        if (index == 0) {
                          handleSubmit(form, 'audit');
                        } else if (index == 1) {
                          handleAudit(
                            form,
                            'refuse',
                            values,
                            fetchData,
                            onClose,
                          );
                        } else {
                          // 驳回
                          handleAudit(
                            form,
                            'refuse',
                            values,
                            fetchData,
                            onClose,
                            'turnDown',
                          );
                        }
                      }}
                      label={'审核'}
                    ></XlbDropdownButton>
                  )}
                {values?.state === 'COMPANY_CPO_AUDIT_ING' &&
                  hasAuth(['新品申请/采购总监审核', '审核']) && (
                    <XlbDropdownButton
                      icon={<XlbIcon name="shenhe" size={16} />}
                      dropList={[{ label: '通过' }, { label: '拒绝' }]}
                      disabled={
                        values?.food_safe_audit_state !== FoodSafeType.audit
                      }
                      dropdownItemClick={(index) => {
                        if (index == 0) {
                          handleAudit(form, 'pass', values, fetchData, onClose);
                        } else {
                          handleAudit(
                            form,
                            'refuse',
                            values,
                            fetchData,
                            onClose,
                          );
                        }
                      }}
                      label={'审核'}
                    ></XlbDropdownButton>
                  )}
                {values?.state == 'COMPANY_CPO_AUDIT_ING' &&
                  hasAuth(['新品申请/采购总监审核撤回', '审核']) && (
                    <XlbButton
                      label="撤回"
                      type="primary"
                      icon={<XlbIcon name="tijiao" />}
                      onClick={() => handleCpoaudit(values, fetchData, onClose)}
                    />
                  )}
                {values?.state == 'APPRAISE_AUDIT_ING' &&
                  hasAuth(['新品申请/品评审核', '审核']) && (
                    <XlbDropdownButton
                      icon={<XlbIcon name="pinglun" size={16} />}
                      dropList={[{ label: '通过' }, { label: '拒绝' }]}
                      dropdownItemClick={(index) => {
                        if (index == 0) {
                          handleSubmit(form, 'evaluate');
                        } else {
                          handleAudit(
                            form,
                            'refuse',
                            values,
                            fetchData,
                            onClose,
                          );
                        }
                      }}
                      label={'品评'}
                    ></XlbDropdownButton>
                  )}
                {values?.state == 'APPRAISE_AUDIT_ING' &&
                  hasAuth(['新品申请/品评审核', '反审核']) && (
                    <XlbButton
                      label="反审核"
                      type="primary"
                      icon={<XlbIcon name="tijiao" />}
                      onClick={() => handleReAudit(values, fetchData, onClose)}
                    />
                  )}
                {values?.state == 'NEGOTIATE_AUDIT_ING' &&
                  hasAuth(['新品申请/采购谈判', '审核']) && (
                    <XlbDropdownButton
                      icon={<XlbIcon name="pinglun" size={16} />}
                      dropList={[{ label: '通过' }, { label: '拒绝' }]}
                      dropdownItemClick={(index) => {
                        if (index == 0) {
                          handleSubmit(form, 'audit');
                        } else {
                          handleAudit(
                            form,
                            'refuse',
                            values,
                            fetchData,
                            onClose,
                          );
                        }
                      }}
                      label={'审核'}
                    ></XlbDropdownButton>
                  )}
                {values?.state == 'COMPANY_ITEM_AUDIT_ING' &&
                  hasAuth(['新品申请/公司商品主档审核', '审核']) && (
                    <XlbDropdownButton
                      icon={<XlbIcon name="pinglun" size={16} />}
                      dropList={[{ label: '通过' }, { label: '拒绝' }]}
                      dropdownItemClick={(index) => {
                        if (index == 0) {
                          handleSubmit(form, 'audit');
                        } else {
                          handleAudit(
                            form,
                            'refuse',
                            values,
                            fetchData,
                            onClose,
                          );
                        }
                      }}
                      label={'审核'}
                    ></XlbDropdownButton>
                  )}
                {values?.food_safe_audit_state === FoodSafeType.waitAudit &&
                  hasAuth(['新品申请/公司食安审核', '审核']) && (
                    <XlbDropdownButton
                      icon={<XlbIcon name="shenhe" size={16} />}
                      dropList={
                        hasAuth(['新品申请/公司食安审核驳回', '审核'])
                          ? [
                              { label: '通过' },
                              { label: '拒绝' },
                              { label: '驳回' },
                            ]
                          : [{ label: '通过' }, { label: '拒绝' }]
                      }
                      dropdownItemClick={(index) => {
                        if (index === 0) {
                          handleSubmit(form, 'audit', true);
                        } else if (index === 1) {
                          handleAudit(
                            form,
                            'refuse',
                            { ...values, isFoodSafe: true },
                            fetchData,
                            onClose,
                          );
                        } else {
                          handleAudit(
                            form,
                            'oppose',
                            { ...values, isFoodSafe: true },
                            fetchData,
                            onClose,
                          );
                        }
                      }}
                      label={'食安审核'}
                    ></XlbDropdownButton>
                  )}
                {['REVIEW_AUDIT_ING', 'NEGOTIATE_AUDIT_ING'].includes(
                  values?.state,
                ) && (
                  <XlbDropdownButton
                    icon={<XlbIcon name="pinglun" size={16} />}
                    dropList={[{ label: '导入' }, { label: '导出' }]}
                    dropdownItemClick={(_index, item) => {
                      if (item?.label === '导入') {
                        handleImport(values, init);
                        return;
                      }
                      if (item?.label === '导出') {
                        handleExport(values);
                      }
                    }}
                    label={'业务操作'}
                  ></XlbDropdownButton>
                )}
                {values?.state == 'NEGOTIATE_AUDIT_ING' &&
                  hasAuth(['新品申请/采购谈判', '审核']) && (
                    <XlbButton
                      label="终止"
                      type="primary"
                      icon={<XlbIcon name="zhongzhi" />}
                      onClick={() => negotiateStop(values, fetchData, onClose)}
                    />
                  )}
              </XlbButton.Group>
            );
          },
          formList: [
            // @ts-ignore
            {
              componentType: 'tabs',
              fieldProps: {
                name: 'tabKey',
                items: [
                  {
                    label: '商品信息',
                    key: 'goodsInfo',
                    forceRender: true,
                    children: goodsInfoList,
                  },
                  {
                    label: '采购初审',
                    key: 'procurementAudit',
                    forceRender: true,
                    dependencies: ['fid', 'state'],
                    disabled: (formValues: any) => {
                      return (
                        !formValues?.fid ||
                        formValues?.state == 'INIT' ||
                        formValues?.state === CERTIFICATE_MAINTAINED.value ||
                        !hasAuth(['新品申请/采购初审', '查询'])
                      );
                    },
                    children: (formValues: any) =>
                      procurementAuditList(formValues),
                  },
                  {
                    label: '品评审核',
                    key: 'qualityAudit',
                    forceRender: true,
                    children: qualityAuditList,
                    dependencies: ['fid', 'state'],
                    disabled: (formValues: any) => {
                      return (
                        !formValues?.fid ||
                        formValues?.state == 'REVIEW_AUDIT_ING' ||
                        formValues?.state == 'INIT' ||
                        formValues?.state === CERTIFICATE_MAINTAINED.value ||
                        !hasAuth(['新品申请/品评审核', '查询'])
                      );
                    },
                  },
                  {
                    label: '采购谈判',
                    forceRender: true,
                    key: 'purchaseNegotiation',
                    dependencies: ['fid', 'state'],
                    disabled: (formValues: any) => {
                      return (
                        !formValues?.fid ||
                        formValues?.state == 'REVIEW_AUDIT_ING' ||
                        formValues?.state == 'APPRAISE_AUDIT_ING' ||
                        formValues?.state == 'INIT' ||
                        formValues?.state === CERTIFICATE_MAINTAINED.value ||
                        !hasAuth(['新品申请/采购谈判', '查询'])
                      );
                    },
                    children: purchaseNegotiationList,
                  },
                ],
              },
            },
          ],
        }}
      />
    </div>
  );
};

export default ProForm;
