.operateBtn {
  display: flex;
}

.newProductSign {
  height: calc(100vh - 80px);
  :global .xlb-field-textAfter {
    margin-bottom: 12px;
  }
  :global .xlb-field-textAfter {
    margin-left: 94px;
  }
  // :global .ant-flex {
  //   display: inline-flex;
  // }
  :global .ant-tabs-nav {
    position: sticky;
    top: 56px;
    background-color: #ffffff;
    z-index: 1;
  }
  :global .ant-btn.ant-btn-sm {
    padding: 0 !important;
  }
  :global
    .xlb-detail-form-layout-horizontal
    .ant-form-item
    .ant-form-item-label {
    width: 130px;
  }

  :global .xlb-detail-form .xlb-proItem-span-1 {
    min-width: 50px;
  }
  .detailContent {
    width: 100%;
    display: block;
    flex-grow: 1;
    :global .xlb-detail-page {
      padding: 0 0 8px 0;
    }
  }

  :global
    .ant-form.xlb-pro-form.xlb-pro-pageContainer-form.xlb-pro-pageContainer-form_layout.ant-form-inline
    > .ant-form-item
    .ant-form-item-label {
    width: 120px !important;
  }

  :global .xlb-date-range-picker {
    width: 381px !important;
  }

  :global
    .xlb-detail-form-layout-horizontal
    .ant-form-item
    .ant-form-item-control-input-content {
    .custom-form-item + .xlb-field-item-after {
      position: relative;
      inset: 0;
      transform: translate(0);
    }
    .custom-form-item-icon {
      cursor: pointer;
      margin-left: 4px;
      &:hover {
        color: #3d66fe;
      }
    }
  }
}
