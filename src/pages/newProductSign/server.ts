import { default as XlbFetch } from '@/utils/XlbFetch';
import { XlbFetch as fetch } from '@xlb/utils/';

//保存
const saveItem = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.save`, data);
};
//商品信息提交
const itemCommit = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.itemcommit`, data);
};
//更新
const updateItem = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.update`, data);
};
//关联供应商
const relatedSupplier = async (data: any) => {
  return await XlbFetch(
    '/scm-mdm/hxl.scm.newitemapplyorder.contactNewSupplier',
    data,
  );
};
// 商品单位
const getUnit = async () => {
  return await XlbFetch(`/erp/hxl.erp.itemunit.find`);
};
// 采购初审
const reviewAudit = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.reviewaudit`, data);
};
// 品控审核
const appraiseaudit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.appraiseaudit`,
    data,
  );
};
// 采购总监审核
const cpoaudit = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.cpoaudit`, data);
};
//食安公司审核
const companyfoodsafeaudit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.companyfoodsafeaudit`,
    data,
  );
};
//公司商品主档审核
const companyitemaudit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.companyitemaudit`,
    data,
  );
};
//采购谈判
const negotiateaudit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.negotiateaudit`,
    data,
  );
};
//操作记录
const recordfind = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.recordfind`, data);
};

//预览
const previewItem = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.newitemapplyorder.preview`, data);
};

// 供应商详情
export async function supplierRead(data: { id: number }) {
  return await XlbFetch('/erp/hxl.erp.supplier.read.supplier', data);
}

// 查询商品psd
export async function itemPsdFind(data: { item_ids: string[] }) {
  return await XlbFetch('/bi/hxl.bi.item.psd.find', data);
}

//供应商账户查生产商
export async function supplierReadSupplier(data: { id: number }) {
  return await XlbFetch('/erp/hxl.erp.supplier.read.supplier', data);
}

//其他用供应商id查生产商
export async function readSupplier(data: { id: number }) {
  return await XlbFetch('/erp/hxl.erp.supplier.short.read', data);
}

//查询投票结果
const voteresultfind = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.voteresultfind`,
    data,
  );
};

//品评审核反审核
const reappraiseaudit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.reappraiseaudit`,
    data,
  );
};

//采购总监审核撤回
const withdrawcpoaudit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.withdrawcpoaudit`,
    data,
  );
};
//商品分类查询psd
const itemMedianPsdFind = async (data: any) => {
  return await XlbFetch(`/scm-mdm/hxl.scm.item.medianpsd.find`, data);
};
// 初审驳回
const reviewoppose = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.reviewoppose`,
    data,
  );
};
//驳回供应商
const companyfoodsafeoppose = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.companyfoodsafeoppose`,
    data,
  );
};
// 采购初审提交
const reviewopposeSubmit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.reviewoppose.submit`,
    data,
  );
};
// 采购初审提交
const companyfoodsafeopposeSubmit = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.companyfoodsafeoppose.submit`,
    data,
  );
};

const negotiateterminate = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.negotiateterminate`,
    data,
  );
};

const getOrgList = async () => {
  return await XlbFetch('/erp/hxl.erp.org.noLimit.find', {
    levels: [1, 2],
  });
};
// 新品统配辅助参数读取
const getExtend = async (data: any) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.forcetransfer.extend.read`,
    data,
  );
};
interface ExportData {
  fid: string;
}
// 采购初审导出
const reviewauditExport = async (data: ExportData) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.reviewaudit.export`,
    data,
  );
};

// 采购谈判导出
const negotiateauditExport = async (data: ExportData) => {
  return await XlbFetch(
    `/scm-mdm/hxl.scm.newitemapplyorder.negotiateaudit.export`,
    data,
  );
};

// 证件信息压缩包导出
const infosDownloadZip = async (fid: string) => {
  return await fetch.post(
    `${process.env.BASE_URL}/scm-mdm/hxl.scm.newitemapplyorder.downloadzip.${fid}`,
    {},
    {
      responseType: 'blob',
    },
  );
};

export default {
  getOrgList,
  saveItem,
  itemCommit,
  updateItem,
  relatedSupplier,
  getUnit,
  reviewAudit,
  appraiseaudit,
  cpoaudit,
  companyfoodsafeaudit,
  negotiateaudit,
  recordfind,
  previewItem,
  supplierRead,
  itemPsdFind,
  companyitemaudit,
  supplierReadSupplier,
  readSupplier,
  voteresultfind,
  reappraiseaudit,
  withdrawcpoaudit,
  itemMedianPsdFind,
  reviewoppose,
  companyfoodsafeoppose,
  reviewopposeSubmit,
  companyfoodsafeopposeSubmit,
  negotiateterminate,
  reviewauditExport,
  negotiateauditExport,
  infosDownloadZip,
  getExtend,
};
