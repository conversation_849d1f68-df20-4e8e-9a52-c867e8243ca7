import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import dayjs from 'dayjs';
import Api from '../server';
const Item = (props: any): any => {
  return [
    {
      componentType: 'blueBar',
      fieldProps: {
        title: '采购初审',
      },
      name: 'review_audit_info',
      children: [
        {
          componentType: 'form',
          fieldProps: {
            dependencies: ['state'],
            readOnly: (formValues: any) => {
              return (
                !hasAuth(['新品申请/采购初审', '编辑']) ||
                formValues.state !== 'REVIEW_AUDIT_ING'
              );
            },
            width: '100%',
            formList: [
              {
                label: '一轮谈判价格',
                id: 'commonInput',
                name: 'first_round_negotiate_price',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入一轮谈判价格' }],
              },
              {
                label: '一轮谈判政策',
                id: 'commonInput',
                name: 'first_round_negotiate_policy',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入一轮谈判政策' }],
              },
              {
                label: '过会日期',
                id: 'createTime',
                name: 'meetings_date',
                fieldProps: {
                  placeholder: '请选择',
                  disabledDate: (current: any) => {
                    // 禁用之前的日期
                    return current < dayjs().startOf('day');
                  },
                },
                rules: [
                  {
                    required: true,
                    message: '请选择过会日期',
                  },
                ],
              },
              {
                label: '预估销售PSD',
                id: 'commonInputNumber',
                name: 'estimated_sale_psd',
                fieldProps: { placeholder: '请输入', precision: 4, min: 0 },
                rules: [{ required: true, message: '请输入预估销售PSD' }],
              },
              {
                label: '预估金额PSD',
                id: 'commonInputNumber',
                name: 'estimated_amount_psd',
                fieldProps: { placeholder: '请输入', precision: 4, min: 0 },
                rules: [{ required: true, message: '请输入预估金额PSD' }],
              },
              {
                label: '商品分类',
                name: 'item_category_id',
                id: ScmFieldKeyMap.scmCategoryId,
                rules: [{ required: true, message: '请选择商品分类' }],
                onChange: async (values: any, form: any, options: any) => {
                  if (options?.length > 0) {
                    form?.setFieldsValue({
                      item_category_name: options[0]?.name,
                      item_category_code: options[0]?.category_code,
                      center_item_category_id: options[0]?.center_id,
                    });
                    const res = await Api.itemMedianPsdFind({
                      item_category_ids: [options[0]?.id],
                      org_ids: props?.org_id ? [props.org_id] : [],
                    });
                    if (res?.code == 0) {
                      form?.setFieldsValue({
                        median_sales_psd: res.data?.psd_median_num,
                        median_amount_psd: res.data?.psd_median_money,
                      });
                    }
                  }
                },
                dependencies: ['item_category_name'],
                render: (obj: any) => {
                  return obj?.item_category_name;
                },
              },
              {
                label: '中位销量PSD',
                id: 'commonInputNumber',
                disabled: true,
                name: 'median_sales_psd',
                fieldProps: { placeholder: '请输入', precision: 4, min: 0 },
                rules: [{ required: true, message: '请输入中位销量PSD' }],
              },
              {
                label: '中位金额PSD',
                id: 'commonInputNumber',
                disabled: true,
                name: 'median_amount_psd',
                fieldProps: { placeholder: '请输入', precision: 4, min: 0 },
                rules: [{ required: true, message: '请输入中位金额PSD' }],
              },
              {
                label: '二次过会',
                name: 'second_meeting',
                id: ScmFieldKeyMap.scmItemByBoolean,
                fieldProps: { defaultValue: false },
                rules: [{ required: true, message: '请选择是否二次过会' }],
              },
              {
                label: '集采推荐',
                name: 'group_purchase_recommend',
                id: ScmFieldKeyMap.scmItemByBoolean,
                fieldProps: { defaultValue: false },
                rules: [{ required: true, message: '请选择是否集采推荐' }],
              },
              {
                label: '淘汰品',
                name: 'eliminate',
                id: ScmFieldKeyMap.scmItemByBoolean,
                fieldProps: { defaultValue: false },
                onChange: (formValues: any, form: any) => {
                  if (!formValues.eliminate) {
                    form?.setFieldsValue({
                      off_shelf_reason: null,
                      replace_items: null,
                    });
                  }
                },
                rules: [{ required: true, message: '请选择是否淘汰品' }],
              },
              {
                label: '销售渠道最低价',
                id: 'commonInputNumber',
                name: 'sale_bottom_price',
                fieldProps: { placeholder: '请输入', precision: 4, min: 0 },
              },
              {
                label: '新品视频',
                id: 'commonUpload',
                name: 'new_item_videos',
                fieldProps: {
                  action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                  mode: 'textButton',
                  listType: 'text',
                  customAccept: '.mp4,.avi,.mov,.wmv,.flv,.mkv',
                  hiddenControlerIcon: false,
                  deleteByServer: false,
                  showUpload: true,
                  data: {
                    refType: 'SUPPLIER_AUDIT',
                    refId: 'sfsffefefe',
                  },
                },
              },
              {
                label: '上新原因',
                name: 'up_new_reasons',
                id: 'commoneTextArea',
                fieldProps: {
                  placeholder: '请输入',
                  autoSize: { minRows: 4, maxRows: 6 },
                },
                rules: [{ required: true, message: '请输入上新原因' }],
              },
              {
                label: '汰换原因',
                name: 'off_shelf_reason',
                id: 'commoneTextArea',
                dependencies: ['eliminate'],
                hidden: (formValues: any) => {
                  return formValues.eliminate;
                },
                disabled: true,
                fieldProps: {
                  placeholder: '请输入',
                  autoSize: { minRows: 4, maxRows: 6 },
                },
              },
              {
                label: '汰换原因',
                name: 'off_shelf_reason',
                id: 'commoneTextArea',
                dependencies: ['eliminate'],
                hidden: (formValues: any) => {
                  return !formValues.eliminate;
                },
                fieldProps: {
                  placeholder: '请输入',
                  autoSize: { minRows: 4, maxRows: 6 },
                },
                rules: [{ required: true, message: '请输入汰换原因' }],
              },
              {
                label: '其他备注',
                name: 'memo',
                fieldProps: {
                  placeholder: '请输入',
                  autoSize: { minRows: 4, maxRows: 6 },
                },
                id: 'commoneTextArea',
              },
            ],
          },
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '替换商品',
          },
          children: [
            {
              componentType: 'editTable',
              name: 'replace_items',
              dependencies: ['eliminate'],
              height: 200,
              fieldProps: {
                colon: false,
                columns: [
                  {
                    code: '_index',
                    name: '序号',
                    features: {
                      sortable: true,
                    },
                    align: 'center',
                  },
                  {
                    code: 'code',
                    name: '商品代码',
                    width: 120,
                    features: {
                      sortable: true,
                    },
                  },
                  {
                    code: 'name',
                    name: '商品名称',
                    width: 200,
                    features: {
                      sortable: true,
                    },
                  },
                  {
                    code: 'sale_PSD',
                    name: '销售PSD',
                    features: {
                      sortable: true,
                    },
                  },
                ],
                dialogParams: {
                  type: 'item',
                  //url: '/center/hxl.center.item.short.page',
                  dataType: 'lists',
                  immediatePost: false,
                  isLeftColumn: true,
                  isMultiple: true,
                  primaryKey: 'id',
                  data: {
                    enabled: true,
                  },
                },
                afterPost: async (data: any) => {
                  const ids = data?.map((item: any) => item.id);
                  let transData = data;
                  const res = await Api.itemPsdFind({ item_ids: ids });
                  if (res?.code == 0) {
                    transData = data?.map((item: any) => {
                      return {
                        ...item,
                        sale_PSD:
                          res?.data?.find((i: any) => i?.item_id === item?.id)
                            ?.psd_money || 0,
                      };
                    });
                  }
                  return transData;
                },
                addConfig: {
                  dependencies: ['eliminate', 'state'],
                  hidden: (formValues: any) => {
                    return !(
                      hasAuth(['新品申请/采购初审', '编辑']) &&
                      formValues?.eliminate &&
                      formValues?.state === 'REVIEW_AUDIT_ING'
                    );
                  },
                },

                delConfig: {
                  dependencies: ['eliminate', 'state'],
                  hidden: (formValues: any) => {
                    return !(
                      hasAuth(['新品申请/采购初审', '编辑']) &&
                      formValues?.eliminate &&
                      formValues?.state === 'REVIEW_AUDIT_ING'
                    );
                  },
                },
              },
            },
          ],
        },
      ],
    },
  ];
};

export default Item;
