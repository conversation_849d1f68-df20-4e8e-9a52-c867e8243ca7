import Avatar from '@/assets/images/basic/defaultAvatar.png';
import { XlbDrawer, XlbTimeLine } from '@xlb/components';
import { STATE_TYPE } from '../data';

const item = (props: any) => {
  const { visible, handleVisible, data } = props;
  return (
    <XlbDrawer
      placement={'right'}
      title="操作记录"
      onClose={() => handleVisible(false)}
      open={visible}
      footer={false}
    >
      <XlbTimeLine
        mode="left"
        items={data.map((item: any, index: number) => ({
          activeKey: index,
          color: 'gray',
          label: (
            <div style={{ fontWeight: 500, fontSize: 16 }}>
              {item.operation_type}
            </div>
          ),
          children: (
            <div style={{ display: 'flex', alignItems: 'center', gap: 10 }}>
              <img width={36} height={36} src={Avatar} />
              <div style={{ flex: 1 }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <span style={{ fontSize: 14, fontWeight: 400 }}>
                    {item.user_name}
                  </span>
                  <span style={{ fontSize: 12, color: '#86909C' }}>
                    {item.create_time}
                  </span>
                </div>
                <span
                  className={
                    STATE_TYPE.find((i) => i.label == item.operation_type)
                      ?.color
                  }
                  style={{ fontSize: 12 }}
                >
                  {item.operation_type}
                </span>
                {item?.remark && <div>{`备注:${item.remark}`}</div>}
              </div>
            </div>
          ),
        }))}
        defaultActiveKey={['1', '2']}
      />
    </XlbDrawer>
  );
};

export default item;
