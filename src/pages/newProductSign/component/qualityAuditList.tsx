import { hasAuth } from '@/utils/kit';
import {
  XlbButton,
  XlbDatePicker,
  XlbIcon,
  XlbImportModal,
  XlbInputNumber,
} from '@xlb/components';
import IconFont from '@xlb/components/dist/components/icon';
import { Space } from 'antd';
import dayjs from 'dayjs';
import { unionBy } from 'lodash';
import * as XLSX from 'xlsx';
import { colomnsHeaderMap } from '../data';
import Api from '../server';

const Item = (formValue: any) => {
  return [
    {
      componentType: 'blueBar',
      name: 'appraise_audit_info',
      fieldProps: {
        title: '品评审核',
      },
      children: [
        {
          componentType: 'form',
          fieldProps: {
            width: '100%',
            dependencies: ['state'],
            readOnly: (formValues: any) => {
              return (
                !hasAuth(['新品申请/品评审核', '编辑']) ||
                formValues.state !== 'APPRAISE_AUDIT_ING'
              );
            },
            formList: [
              {
                id: 'commonInputNumber',
                label: '投票人数',
                name: 'vote_num',
                itemSpan: 3,
                readOnly: true,
              },
              {
                id: 'commonInput',
                readOnly: true,
                itemSpan: 3,
                label: '投票均分',
                name: 'vote_result',
              },
              {
                id: 'commoneTextArea',
                label: '说明',
                name: 'memo',
                itemSpan: 6,
                fieldProps: {
                  placeholder: '请输入',
                  autoSize: { minRows: 1, maxRows: 1 },
                },
              },
            ],
          },
        },
        {
          componentType: 'editTable',
          primaryKey: 'id',
          name: 'org_and_store',
          fieldProps: {
            colon: false,
            columns: [
              {
                code: '_index',
                name: '序号',
                features: {
                  sortable: true,
                },
                align: 'center',
              },
              {
                code: 'org_name',
                name: '公司',
                width: 120,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'store_name',
                name: '配送中心',
                width: 200,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'province',
                name: '省',
                width: 120,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'avg_psd',
                name: '仓平均销量PSD',
                width: 150,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'mid_psd',
                name: '仓中位销量PSD',
                width: 150,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'psd',
                name: '店均psd',
                width: 120,
                features: {
                  sortable: true,
                },
                render: (text: any, record: any) => {
                  console.log(text, 'render', record);
                  if (
                    record._click &&
                    formValue?.state === 'APPRAISE_AUDIT_ING'
                  ) {
                    return (
                      <XlbInputNumber
                        style={{ display: 'block' }}
                        onClick={(e) => e.stopPropagation()}
                        defaultValue={text}
                        precision={2}
                        onBlur={(e) => {
                          const value = Number(e.target.value || 0).toFixed(2);
                          record.psd = e.target.value ? value : '';
                          record.allocated_quantity = e.target.value
                            ? (+value * 3).toFixed(2)
                            : '';
                        }}
                      />
                    );
                  }
                  return <span>{text}</span>;
                },
              },
              {
                code: 'allocated_quantity',
                name: '店均统配量',
                width: 120,
                features: {
                  sortable: true,
                },
                render: (text: any, record: any) => {
                  if (
                    record._click &&
                    formValue?.state === 'APPRAISE_AUDIT_ING'
                  ) {
                    return (
                      <XlbInputNumber
                        style={{ display: 'block' }}
                        value={record.allocated_quantity}
                        precision={2}
                        onChange={(e) => {
                          record.allocated_quantity =
                            e || e === 0 ? Number(e).toFixed(2) : '';
                        }}
                      />
                    );
                  }
                  return <span>{text}</span>;
                },
              },
              {
                code: 'store_count',
                name: '仓统配门店数',
                width: 120,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'open_store_count',
                name: '仓营业门店数',
                width: 120,
                features: {
                  sortable: true,
                },
              },
              {
                code: 'store_entry_rate',
                name: '进店率',
                width: 120,
                features: {
                  sortable: true,
                },
                render: (text: any, record: any) => {
                  if (
                    record._click &&
                    formValue?.state === 'APPRAISE_AUDIT_ING'
                  ) {
                    return (
                      <XlbInputNumber
                        onClick={(e) => e.stopPropagation()}
                        min={0}
                        max={100}
                        precision={2}
                        suffix="%"
                        defaultValue={text}
                        onChange={(e) => {
                          record.store_entry_rate =
                            e || e === 0 ? Number(e).toFixed(2) : '';
                        }}
                      />
                    );
                  }
                  return (
                    <span>
                      {text || text === 0 ? `${Number(text).toFixed(2)}%` : ''}
                    </span>
                  );
                },
              },
              {
                code: 'earliest_arrival_date',
                name: '最早到货日期',
                width: 200,
                features: {
                  sortable: true,
                },
                render: (text: any, record: any) => {
                  if (
                    record._click &&
                    formValue?.state === 'APPRAISE_AUDIT_ING'
                  ) {
                    return (
                      <XlbDatePicker
                        format={'YYYY-MM-DD'}
                        width={180}
                        onClick={(e) => e.stopPropagation()}
                        defaultValue={text}
                        onChange={(e) => {
                          const value = e || '';
                          record.earliest_arrival_date = value;
                        }}
                        // 禁用今天之前的日期
                        disabledDate={(current: any) =>
                          current < dayjs().subtract(1, 'day').endOf('day')
                        }
                      />
                    );
                  }
                  return <span>{text}</span>;
                },
              },
            ],
            onChangeData: (data: any) => {
              console.log('store_details', data);
              // form.setFielvalue()
            },
            afterPost: async (data: any) => {
              const resT = await Api.getExtend({
                erp_store_ids: data?.map((i: any) => i.id || i.erp_store_id),
                fid: formValue?.fid,
                // erp_store_ids: data?.map((i: any) => i.erp_store_id),
                item_category_ids: [formValue?.item_category_id],
                company_id: formValue?.company_id,
              });
              if (resT.code === 0) {
                const dataTransform = data?.map((item: any) => {
                  const extraInfo = resT?.data?.find(
                    (t: any) => t.erp_store_id === item.id,
                  );
                  return {
                    ...item,
                    ...extraInfo,
                    erp_store_id: item.id,
                  };
                });
                return dataTransform;
              } else {
                return [];
              }
            },
            dialogParams: {
              type: 'store',
              dataType: 'tree',
              isMultiple: true,
              data: {
                center_flag: true,
                store_house_filter: true,
              },
              primaryKey: 'id',
              //url: '/center/hxl.center.store.delivery.findall',
            },
            addConfig: {
              dependencies: ['state'],
              hidden: (formValues: any) => {
                return (
                  !hasAuth(['新品申请/品评审核', '编辑']) ||
                  formValues.state !== 'APPRAISE_AUDIT_ING'
                );
              },
            },
            delConfig: {
              dependencies: ['state'],
              hidden: (formValues: any) => {
                return (
                  !hasAuth(['新品申请/品评审核', '编辑']) ||
                  formValues.state !== 'APPRAISE_AUDIT_ING'
                );
              },
            },
            extra: (formValues: any) => {
              const { org_and_store } =
                formValues?.form?.getFieldsValue(true) || {};
              // const newArr = org_and_store?.map((t: any) => {
              //   const obj = renameKeys(t, colomnsHeaderMap);
              //   extraText?.map((nameKey: string) => {
              //     if (!obj.hasOwnProperty(nameKey)) {
              //       obj[`${nameKey}`] = null;
              //     }
              //   });
              //   return obj;
              // });
              const newArr = (org_and_store || [])?.map((t) => {
                const transformedItem = {};
                colomnsHeaderMap.forEach((key, value) => {
                  console.log(colomnsHeaderMap, key, value, 'colomnsHeaderMap');
                  transformedItem[key] = t[value];
                });

                console.log(transformedItem, 'hh');
                return transformedItem;
              });
              console.log(newArr, 'newArr');

              return (
                <Space>
                  <XlbButton
                    type="primary"
                    label="导入"
                    // disabled={loading}
                    onClick={async () => {
                      await XlbImportModal({
                        templateUrl: `/scm-mdm/hxl.scm.newitemapplyorder.forcetransfer.download`,
                        importUrl: `/scm-mdm/hxl.scm.newitemapplyorder.forcetransfer.import`,
                        templateName: '导入模板',
                        params: { company_id: formValue?.company_id },
                        callback: async (res) => {
                          const { org_and_store } =
                            formValues?.form?.getFieldsValue(true) || {};
                          console.log(
                            org_and_store,
                            res?.data?.content,
                            '导入',
                          );
                          // if (res.code === 0 && !res.data?.state) {
                          //   XlbTipsModal({
                          //     tipsList: res.data?.error_messages,
                          //   });
                          // }
                          const resStores = res?.data?.content || [];
                          // const orgAndStores=org_and_store?.map
                          const newStores = unionBy(
                            [...resStores, ...org_and_store],
                            'erp_store_id',
                          );
                          if (newStores?.length) {
                            const resT = await Api.getExtend({
                              fid: formValue?.fid,
                              erp_store_ids: newStores?.map(
                                (i: any) => i.erp_store_id,
                              ),
                              item_category_ids: [formValue?.item_category_id],
                              company_id: formValue?.company_id,
                            });
                            if (resT.code === 0) {
                              console.log('扩展', resT?.data, newStores);
                              const dataTransform = newStores?.map(
                                (item: any) => {
                                  const extraInfo = resT?.data?.find(
                                    (t: any) =>
                                      t.erp_store_id === item.erp_store_id,
                                  );
                                  return {
                                    ...item,
                                    ...extraInfo,
                                  };
                                },
                              );
                              console.log(dataTransform, 'dataTransform');
                              formValues?.form?.setFieldsValue({
                                org_and_store: dataTransform,
                              });
                            } else {
                              formValues?.form?.setFieldsValue({
                                org_and_store: newStores,
                              });
                            }
                          }
                        },
                      });
                    }}
                    icon={
                      <IconFont name="daoru" color="currentColor" size={16} />
                    }
                  />
                  <XlbButton
                    label="导出"
                    type="primary"
                    icon={<XlbIcon name="daochu" />}
                    // disabled={!(formValues?.dataSource?.length > 0)}
                    onClick={async () => {
                      // 创建工作簿
                      const wb = XLSX.utils.book_new();

                      // 创建工作表
                      const ws = XLSX.utils.json_to_sheet(newArr);
                      // const dateColumnIndex = 5; // 假设 earliest_arrival_date 在第3列（索引从0开始）
                      // const range = XLSX.utils.decode_range(ws['!ref']);
                      // for (let R = range.s.r + 1; R <= range.e.r; ++R) {
                      //   const cellRef = XLSX.utils.encode_cell({
                      //     r: R,
                      //     c: dateColumnIndex,
                      //   }); // 单元格引用，例如 C2, C3, ...
                      //   if (ws[cellRef]) {
                      //     ws[cellRef].t = 's'; // 设置为字符串类型
                      //   }
                      // }
                      // 将工作表添加到工作簿
                      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

                      // 导出文件
                      XLSX.writeFile(wb, '新品申请统配参数导出.xlsx');
                    }}
                  />
                </Space>
              );
            },
          },
        },
      ],
    },
  ];
};

export default Item;
