import { XlbModal } from '@xlb/components';
import PreviewTable from './previewTable';

const Item = (props: any) => {
  const { visible, handleVisible, data } = props;

  return (
    <XlbModal
      isCancel={false}
      open={visible}
      title={null}
      width="100%"
      styles={{
        body: {
          height: 'calc(100vh - 70px)',
          maxHeight: 'calc(100vh - 70px)',
        },
      }}
      // height="100%"
      centered
      closeIcon={false}
      onOk={() => handleVisible(false)}
      onCancel={() => handleVisible(false)}
    >
      {data.map((item: any) => (
        <PreviewTable data={item} />
      ))}
    </XlbModal>
  );
};

export default Item;
