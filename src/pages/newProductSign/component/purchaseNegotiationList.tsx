import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { ProForm } from '@ant-design/pro-form';
import { XlbSelect } from '@xlb/components';
import { getFirstWord } from '@xlb/utils';
import {
  buyLimits,
  countType,
  dayOrMohth,
  EXPIRE_TYPE,
  goodsType,
  inCheckList,
  ITEM_ATTRIBUTE,
  ITEM_TYPE,
  makeDate,
  purchaseType,
  RECEIVE_RULE_TYPE,
} from '../data';
const Item = (props: any): any => {
  return [
    {
      componentType: 'blueBar',
      name: 'negotiate_audit_info',
      fieldProps: {
        title: '采购谈判',
      },
      children: [
        {
          componentType: 'form',
          fieldProps: {
            width: '100%',
            dependencies: ['state'],
            readOnly: (formValues: any) => {
              return (
                !hasAuth(['新品申请/采购谈判', '编辑']) ||
                !(
                  formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                  formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                )
              );
            },
            formList: [
              {
                label: '最终谈判价格',
                id: 'commonInputNumber',
                name: 'final_round_negotiate_price',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                    return true;
                  } else {
                    if (
                      !hasAuth(['新品申请/采购谈判', '编辑']) ||
                      !(
                        formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                        formValues.state == 'GROUP_ITEM_AUDIT_ING'
                      )
                    ) {
                      return true;
                    } else {
                      return false;
                    }
                  }
                },
                disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                fieldProps: { placeholder: '请输入', min: 0 },
                rules: [{ required: true, message: '请输入最终谈判价格' }],
                render: (text: any) => {
                  return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                    ? text?.final_round_negotiate_price
                    : '****';
                },
              },
              {
                label: '最终谈判政策',
                id: 'commonInput',
                name: 'final_round_negotiate_policy',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                    return true;
                  } else {
                    if (
                      !hasAuth(['新品申请/采购谈判', '编辑']) ||
                      !(
                        formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                        formValues.state == 'GROUP_ITEM_AUDIT_ING'
                      )
                    ) {
                      return true;
                    } else {
                      return false;
                    }
                  }
                },
                disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入最终谈判政策' }],
                render: (text: any) => {
                  return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                    ? text?.final_round_negotiate_policy
                    : '****';
                },
              },
              // {
              //   label: '单店首单量',
              //   id: 'commonInputNumber',
              //   name: 'initial_order_size_per_shop',
              //   fieldProps: { placeholder: '请输入' },
              //   rules: [{ required: true, message: '请输入单店首单量' }],
              // },
              // {
              //   label: '自动生成采购订单',
              //   name: 'auto_generate_purchase',
              //   id: ScmFieldKeyMap.scmItemByBoolean,
              //   fieldProps: { defaultValue: true },
              //   rules: [
              //     { required: true, message: '请选择是否自动生成采购订单' },
              //   ],
              // },
              {
                label: '淘汰品自动停购',
                name: 'eliminate_stop_purchase',
                id: ScmFieldKeyMap.scmItemByBoolean,
                fieldProps: { defaultValue: true },
                rules: [{ required: true, message: '请选择淘汰品自动停购' }],
              },
              {
                label: '其他备注',
                id: 'commoneTextArea',
                name: 'memo',
                fieldProps: {
                  placeholder: '请输入',
                  autoSize: { minRows: 4, maxRows: 6 },
                },
              },
            ],
          },
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '商品信息',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  return (
                    !hasAuth(['新品申请/采购谈判', '编辑']) ||
                    !(
                      formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                      formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                    )
                  );
                },
                formList: [
                  {
                    label: '商品条码',
                    id: 'commonInput',
                    name: 'bar_code',
                    fieldProps: {
                      placeholder: '定量装输入商品销售条码，散称条码',
                    },
                    rules: [{ required: true, message: '请输入商品条码' }],
                  },
                  {
                    label: '商品名称',
                    id: 'commonInput',
                    name: 'name',
                    fieldProps: {
                      placeholder: '品牌 + 品名 + 口味 + 克重 如:奥利奥',
                    },
                    rules: [{ required: true, message: '请输入供应商名称' }],
                    onChange: (values: any, form: any) => {
                      form?.setFieldsValue({
                        shorthand_code: getFirstWord(values.target.value),
                      });
                    },
                  },
                  {
                    label: '商品等级',
                    id: 'commonInput',
                    name: 'item_level',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入商品等级' }],
                  },
                  // {
                  //   label: '外箱码',
                  //   id: 'commonInput',
                  //   name: 'package_bar_code',
                  //   fieldProps: { placeholder: '请输入',defaultValue:props?.item_info?.package_bar_code },
                  //   rules: [{ required: true, message: '请输入外箱码' }],
                  // },
                  {
                    label: '速记码',
                    id: 'commonInput',
                    name: 'shorthand_code',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入速记码' }],
                  },
                  {
                    label: '零售规格',
                    id: 'commonInput',
                    name: 'retail_spec',
                    fieldProps: { placeholder: '请输入' },
                  },
                  {
                    label: '外箱条形码',
                    id: 'commonInput',
                    name: 'package_bar_code',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入外箱条形码' }],
                  },
                  {
                    label: '采购规格',
                    id: 'commonInput',
                    name: 'purchase_spec',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入采购规格' }],
                  },
                  {
                    label: '采购类型',
                    id: 'commonSelect',
                    name: 'purchase_type',
                    fieldProps: {
                      placeholder: '请输入',
                      options: purchaseType,
                    },
                    rules: [{ required: true, message: '请选择采购规格' }],
                  },
                  {
                    name: 'item_category_id',
                    id: ScmFieldKeyMap.scmCategoryId,
                    label: '商品分类',
                    rules: [{ required: true, message: '请选择商品分类' }],
                    onChange: (values: any, form: any, options: any) => {
                      if (options?.length > 0) {
                        form?.setFieldsValue({
                          item_category_name: options[0]?.name,
                          item_category_code: options[0]?.category_code,
                          center_item_category_id: options[0]?.center_id,
                        });
                      }
                    },
                    dependencies: ['item_category_name'],
                    render: (obj: any) => {
                      return obj?.item_category_name;
                    },
                    fieldProps: {
                      defaultValue: props?.review_audit_info?.item_category_id,
                    },
                  },
                  {
                    label: '核算方式',
                    id: 'commonSelect',
                    name: 'account_method',
                    fieldProps: {
                      placeholder: '请选择',
                      options: countType,
                    },
                    rules: [{ required: true, message: '请选择核算方式' }],
                  },
                  {
                    label: '商品部门',
                    id: ScmFieldKeyMap.productDepartment,
                    name: 'item_dept_id',
                    fieldProps: {
                      placeholder: '请选择',
                    },
                    onChange: (value: any, form: any, options: any) => {
                      form.setFieldsValue({
                        item_dept_name: options?.label,
                      });
                    },
                  },
                  {
                    label: '采购范围',
                    id: 'commonSelect',
                    fieldProps: {
                      placeholder: '请选择',
                      options: buyLimits,
                    },
                    name: 'purchase_scope',
                    rules: [{ required: true, message: '请选择采购范围' }],
                  },
                  {
                    label: '商品类型',
                    id: 'commonSelect',
                    fieldProps: {
                      placeholder: '请选择',
                      options: goodsType,
                    },
                    name: 'item_type',
                    rules: [{ required: true, message: '请选择商品类型' }],
                  },
                  {
                    label: '商品品牌',
                    id: ScmFieldKeyMap.productBrand,
                    name: 'item_brand_id',
                    fieldProps: {
                      placeholder: '请选择',
                    },
                    rules: [{ required: true, message: '请选择商品品牌' }],
                    onChange: (values: any, form: any, options: any) => {
                      form?.setFieldsValue({
                        center_item_brand_id: options?.center_id,
                        item_brand_name: options?.label,
                      });
                    },
                  },
                  {
                    label: '商品产地',
                    id: 'commonInput',
                    name: 'origin_place',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入商品产地' }],
                  },
                  {
                    label: '玩具序列号',
                    id: 'commonInput',
                    name: 'toy_code',
                    fieldProps: { placeholder: '请输入' },
                  },
                  {
                    label: '是否执行企业标准',
                    id: ScmFieldKeyMap.executiveStandardType,
                    name: 'executive_standard_type',
                    fieldProps: { placeholder: '请选择' },
                    rules: [
                      { required: true, message: '请选择是否执行企业标准' },
                    ],
                  },
                  {
                    label: '产品执行标准',
                    id: ScmFieldKeyMap.executiveStandard,
                    name: 'executive_standard',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入产品执行标准' }],
                  },
                  {
                    label: '斤规格',
                    id: ScmFieldKeyMap.specUnit,
                    rules: [{ required: true, message: '请选择斤规格' }],
                    render: (record: any) => {
                      return (
                        <div>
                          {record.weight_spec}
                          <span style={{ marginLeft: '10px' }}>
                            {record.weight_spec_unit}
                          </span>
                        </div>
                      );
                    },
                  },
                  {
                    label: '税收分类编码',
                    id: 'commonInput',
                    name: 'tax_no',
                    fieldProps: {
                      // defaultValue: formValue?.item_info?.tax_no,
                      placeholder: '请输入',
                    },
                    // rules: [{ required: true, message: '请输入税收分类编码' }],
                    rules: [
                      { required: true, message: '请输入税收分类编码' },
                      {
                        pattern: /^\d{1,19}$/,
                        message: '仅支持输入数字,且不能超过19位',
                      },
                    ],
                  },
                  // {
                  //   label: '采购经理',
                  //   id: 'purchaseManager',
                  //   name: 'purchase_manager_id',
                  //   fieldProps: { placeholder: '请选择', disabled: true },
                  //   rules: [{ required: true, message: '请选择采购经理' }],
                  //   onChange: (values: any, form: any, options: any) => {
                  //     form?.setFieldsValue({
                  //       purchase_manager_name:
                  //         options?.length && options[0]?.name,
                  //     });
                  //   },
                  // },
                ],
              },
            },
          ],
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '计量单位',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  return (
                    !hasAuth(['新品申请/采购谈判', '编辑']) ||
                    !(
                      formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                      formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                    )
                  );
                },
                formList: [
                  {
                    id: ScmFieldKeyMap.scmUnit,
                    name: 'unit',
                    rules: [{ required: true, message: '请选择基本单位' }],
                    label: '基本单位',
                  },
                ],
              },
            },
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  return (
                    !hasAuth(['新品申请/采购谈判', '编辑']) ||
                    !(
                      formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                      formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                    )
                  );
                },
                formList: [
                  {
                    id: ScmFieldKeyMap.scmUnit,
                    name: 'stock_unit',
                    rules: [{ required: true, message: '请选择库存单位' }],
                    label: '库存单位',
                  },
                  {
                    label: '库存换算率',
                    id: 'commonInput',
                    name: 'stock_ratio',
                    fieldProps: { placeholder: '请输入' },
                    rules: [
                      { required: true, message: '请输入库存换算率' },
                      {
                        pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/,
                        message: '请输入正确格式的数据',
                      },
                    ],
                  },
                  {
                    id: ScmFieldKeyMap.scmUnit,
                    name: 'purchase_unit',
                    rules: [{ required: true, message: '请选择采购单位' }],
                    label: '采购单位',
                  },
                  {
                    label: '采购换算率',
                    id: 'commonInput',
                    name: 'purchase_ratio',
                    fieldProps: { placeholder: '请输入' },
                    rules: [
                      { required: true, message: '请输入采购换算率' },
                      {
                        pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/,
                        message: '请输入正确格式的数据',
                      },
                    ],
                  },
                  {
                    id: ScmFieldKeyMap.scmUnit,
                    name: 'wholesale_unit',
                    rules: [{ required: true, message: '请选择批发单位' }],
                    label: '批发单位',
                  },
                  {
                    label: '批发换算率',
                    id: 'commonInput',
                    name: 'wholesale_ratio',
                    fieldProps: { placeholder: '请输入' },
                    rules: [
                      { required: true, message: '请输入批发换算率' },
                      {
                        pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/,
                        message: '请输入正确格式的数据',
                      },
                    ],
                  },
                  {
                    id: ScmFieldKeyMap.scmUnit,
                    name: 'delivery_unit',
                    rules: [{ required: true, message: '请选择配送单位' }],
                    label: '配送单位',
                  },
                  {
                    label: '配送换算率',
                    id: 'commonInput',
                    name: 'delivery_ratio',
                    fieldProps: { placeholder: '请输入', disabled: true },
                    rules: [
                      { required: true, message: '请输入配送换算率' },
                      {
                        pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/,
                        message: '请输入正确格式的数据',
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '价格设置',
          },
          children: [
            {
              componentType: 'subTitle',
              fieldProps: {
                title: '采购价',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    dependencies: ['state'],
                    readOnly: (formValues: any) => {
                      return (
                        !hasAuth(['新品申请/采购谈判', '编辑']) ||
                        !(
                          formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                          formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                        )
                      );
                    },
                    width: '100%',
                    formList: [
                      {
                        label: '采购价',
                        disabled: !hasAuth(['新品申请/采购价', '编辑']),
                        id: 'commonInputNumber',
                        name: 'purchase_price',
                        dependencies: ['state', 'purchase_price'],
                        fieldProps: {
                          placeholder: '请输入',
                          min: 0,
                        },
                        readOnly: (formValues: any) => {
                          if (!hasAuth(['新品申请/采购价', '查询'])) {
                            return true;
                          } else {
                            if (
                              !hasAuth(['新品申请/采购谈判', '编辑']) ||
                              !(
                                formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                                formValues.state == 'GROUP_ITEM_AUDIT_ING'
                              )
                            ) {
                              return true;
                            } else {
                              return false;
                            }
                          }
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/采购价', '查询'])
                            ? text?.purchase_price
                            : '****';
                        },
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'subTitle',
              fieldProps: {
                title: '配送价',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    dependencies: ['state'],
                    readOnly: (formValues: any) => {
                      return (
                        !hasAuth(['新品申请/采购谈判', '编辑']) ||
                        !(
                          formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                          formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                        )
                      );
                    },
                    formList: [
                      {
                        label: '配送价类型',
                        id: ScmFieldKeyMap.deliveryPriceWithRadio,
                        render: (text: any) => {
                          return (
                            <div>
                              {
                                ITEM_TYPE.find(
                                  (item) => item.value == text.delivery_type,
                                )?.label
                              }
                              <span style={{ marginLeft: '10px' }}>
                                {text.delivery_type_item}
                              </span>
                            </div>
                          );
                        },
                      },
                      {
                        id: ScmFieldKeyMap.deliveryPriceCompute,
                        readOnly: (formValues: any) => {
                          return !hasAuth(['新品申请/配送价', '查询']);
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/配送价', '查询'])
                            ? text.delivery_num
                            : '****';
                        },
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'subTitle',
              fieldProps: {
                title: '批发价',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    dependencies: ['state'],
                    readOnly: (formValues: any) => {
                      return (
                        !hasAuth(['新品申请/采购谈判', '编辑']) ||
                        !(
                          formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                          formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                        )
                      );
                    },
                    formList: [
                      {
                        label: '批发价类型',
                        id: 'commonInputNumber',
                        name: 'wholesale_type_item',
                        dependencies: ['wholesale_type'],
                        fieldProps: {
                          style: { width: '100%' },
                          placeholder: '请输入',
                          defaultValue: 0,
                          min: 0,
                          addonBefore: (
                            <ProForm.Item noStyle name={'wholesale_type'}>
                              <XlbSelect
                                width={80}
                                options={ITEM_TYPE}
                                defaultValue={2}
                              />
                            </ProForm.Item>
                          ),
                        },
                        render: (text: any) => {
                          return (
                            <div>
                              {
                                ITEM_TYPE.find(
                                  (item) => item.value == text.wholesale_type,
                                )?.label
                              }
                              <span style={{ marginLeft: '10px' }}>
                                {text.wholesale_type_item}
                              </span>
                            </div>
                          );
                        },
                      },
                      {
                        id: ScmFieldKeyMap.wholesaleCompute,
                        readOnly: (formValues: any) => {
                          return !hasAuth(['新品申请/批发价', '查询']);
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/批发价', '查询'])
                            ? text.wholesale_num
                            : '****';
                        },
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'subTitle',
              fieldProps: {
                title: '默认零售价',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    dependencies: ['state'],
                    readOnly: (formValues: any) => {
                      return (
                        !hasAuth(['新品申请/采购谈判', '编辑']) ||
                        !hasAuth(['新品申请/默认零售价', '查询']) ||
                        !(
                          formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                          formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                        )
                      );
                    },
                    disabled: !hasAuth(['新品申请/默认零售价', '编辑']),
                    formList: [
                      {
                        label: '标准售价',
                        id: ScmFieldKeyMap.standardPriceWithRadio,
                        name: 'sale_price',
                        // disabled: !hasAuth(['新品申请/默认零售价', '编辑']),
                        // readOnly: !hasAuth(['新品申请/默认零售价', '查询']),
                        render: (text: any) => {
                          return hasAuth(['新品申请/默认零售价', '查询'])
                            ? text?.sale_price
                            : '****';
                        },
                      },
                      {
                        label: '售价2',
                        id: 'commonInputNumber',
                        name: 'sale_price_s',
                        fieldProps: {
                          precision: 2,
                          min: 0,
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/默认零售价', '查询'])
                            ? text.sale_price_s
                            : '****';
                        },
                      },
                      {
                        label: '售价3',
                        id: 'commonInputNumber',
                        name: 'sale_price_t',
                        fieldProps: {
                          precision: 2,
                          min: 0,
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/默认零售价', '查询'])
                            ? text.sale_price_t
                            : '****';
                        },
                      },
                      {
                        label: '售价4',
                        id: 'commonInputNumber',
                        name: 'sale_price_f',
                        fieldProps: {
                          precision: 2,
                          min: 0,
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/默认零售价', '查询'])
                            ? text.sale_price_f
                            : '****';
                        },
                      },
                      {
                        label: '最低售价',
                        id: 'commonInputNumber',
                        name: 'sale_min_price',
                        fieldProps: {
                          precision: 2,
                          min: 0,
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/默认零售价', '查询'])
                            ? text.sale_min_price
                            : '****';
                        },
                      },
                      {
                        label: '最高售价',
                        id: 'commonInputNumber',
                        name: 'sale_max_price',
                        fieldProps: {
                          precision: 2,
                          min: 0,
                        },
                        render: (text: any) => {
                          return hasAuth(['新品申请/默认零售价', '查询'])
                            ? text.sale_max_price
                            : '****';
                        },
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '体积重量',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  return (
                    !hasAuth(['新品申请/采购谈判', '编辑']) ||
                    !(
                      formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                      formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                    )
                  );
                },
                formList: [
                  {
                    label: '采购单位:长(cm)',
                    id: 'commonInputNumber',
                    name: 'purchase_length',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 2,
                    },
                    rules: [
                      {
                        pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                        message: '请输入正确格式的数据',
                      },
                      {
                        pattern:
                          /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
                        message: '不能大于1000cm且保留两位小数',
                      },
                    ],
                  },
                  {
                    label: '宽(cm)',
                    id: 'commonInputNumber',
                    name: 'purchase_width',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 2,
                    },
                    rules: [
                      {
                        pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                        message: '请输入正确格式的数据',
                      },
                      {
                        pattern:
                          /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
                        message: '不能大于1000cm且保留两位小数',
                      },
                    ],
                  },
                  {
                    label: '高(cm)',
                    id: 'commonInputNumber',
                    name: 'purchase_height',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 2,
                    },
                    rules: [
                      {
                        pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                        message: '请输入正确格式的数据',
                      },
                      {
                        pattern:
                          /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
                        message: '不能大于1000cm且保留两位小数',
                      },
                    ],
                  },
                  {
                    label: '基本单位:长(cm)',
                    id: 'commonInputNumber',
                    name: 'basic_length',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 2,
                    },
                    rules: [
                      {
                        pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                        message: '请输入正确格式的数据',
                      },
                      {
                        pattern:
                          /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
                        message: '不能大于1000cm且保留两位小数',
                      },
                    ],
                  },
                  {
                    label: '宽(cm)',
                    id: 'commonInputNumber',
                    name: 'basic_width',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 2,
                    },
                    rules: [
                      {
                        pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                        message: '请输入正确格式的数据',
                      },
                      {
                        pattern:
                          /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
                        message: '不能大于1000cm且保留两位小数',
                      },
                    ],
                  },
                  {
                    label: '高(cm)',
                    id: 'commonInputNumber',
                    name: 'basic_height',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 2,
                    },
                    rules: [
                      {
                        pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                        message: '请输入正确格式的数据',
                      },
                      {
                        pattern:
                          /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
                        message: '不能大于1000cm且保留两位小数',
                      },
                    ],
                  },
                  {
                    label: '托盘可放数量',
                    id: 'commonInputNumber',
                    name: 'pallet_capacity',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 1,
                      defaultValue: null,
                    },
                  },
                  {
                    label: '货物重量',
                    id: 'commonSelect',
                    name: 'cargo_weight',
                    fieldProps: {
                      placeholder: '请选择',
                      options: [
                        { label: '小', value: 0 },
                        { label: '中', value: 1 },
                        { label: '大', value: 2 },
                      ],
                    },
                  },
                  {
                    label: '采购体积(m³)',
                    id: ScmFieldKeyMap.purchaseVolume,
                    name: 'purchase_volume',
                    disabled: true,
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 4,
                    },
                  },
                  {
                    label: '配送体积(m³)',
                    id: ScmFieldKeyMap.deliveryVolume,
                    name: 'delivery_volume',
                    disabled: true,
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 4,
                    },
                  },
                  {
                    label: '毛重(kg)',
                    id: 'commonInputNumber',
                    name: 'gross_weight',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 3,
                    },
                  },
                  {
                    label: '净重(kg)',
                    id: 'commonInputNumber',
                    name: 'net_weight',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 3,
                    },
                  },
                ],
              },
            },
          ],
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '其他设置',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  return (
                    !hasAuth(['新品申请/采购谈判', '编辑']) ||
                    !(
                      formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                      formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                    )
                  );
                },
                formList: [
                  {
                    label: '保质期（月/天）',
                    id: 'commonInputNumber',
                    dependencies: ['expire_type'],
                    name: 'expire_type_num',
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 0,
                      addonBefore: (
                        <ProForm.Item noStyle name={'expire_type'}>
                          <XlbSelect
                            width={80}
                            options={dayOrMohth}
                            defaultValue={1}
                          />
                        </ProForm.Item>
                      ),
                      style: { width: '100%' },
                    },
                    render: (text: any) => {
                      return (
                        <div>
                          {text.expire_type_num}
                          <span style={{ marginLeft: '10px' }}>
                            {
                              dayOrMohth.find(
                                (item) => item.value == text.expire_type,
                              )?.label
                            }
                          </span>
                        </div>
                      );
                    },
                  },
                  {
                    label: '收货规则',
                    id: 'commonInputNumber',
                    name: 'receive_rule_type_num',
                    dependencies: ['receive_rule_type'],
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 0,
                      addonBefore: (
                        <ProForm.Item noStyle name={'receive_rule_type'}>
                          <XlbSelect
                            width={80}
                            options={RECEIVE_RULE_TYPE}
                            defaultValue={0}
                          />
                        </ProForm.Item>
                      ),
                      style: { width: '100%' },
                    },
                    render: (text: any) => {
                      return (
                        <div>
                          {text.receive_rule_type_num}
                          <span style={{ marginLeft: '10px' }}>
                            {
                              RECEIVE_RULE_TYPE.find(
                                (item) => item.value == text.receive_rule_type,
                              )?.label
                            }
                          </span>
                        </div>
                      );
                    },
                  },
                  {
                    label: '中心临期提醒',
                    id: 'commonInputNumber',
                    name: 'center_expire_type_item',
                    dependencies: ['center_expire_type'],
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 0,
                      addonBefore: (
                        <ProForm.Item noStyle name={'center_expire_type'}>
                          <XlbSelect
                            width={80}
                            options={EXPIRE_TYPE}
                            defaultValue={0}
                          />
                        </ProForm.Item>
                      ),
                      style: { width: '100%' },
                    },
                    render: (text: any) => {
                      return (
                        <div>
                          {text.center_expire_type_item}
                          <span style={{ marginLeft: '10px' }}>
                            {
                              EXPIRE_TYPE.find(
                                (item) => item.value == text.center_expire_type,
                              )?.label
                            }
                          </span>
                        </div>
                      );
                    },
                  },
                  {
                    label: '门店临期提醒',
                    id: 'commonInputNumber',
                    name: 'store_expire_type_item',
                    dependencies: ['store_expire_type'],
                    fieldProps: {
                      placeholder: '请输入',
                      precision: 0,
                      addonBefore: (
                        <ProForm.Item noStyle name={'store_expire_type'}>
                          <XlbSelect
                            width={80}
                            options={EXPIRE_TYPE}
                            defaultValue={0}
                          />
                        </ProForm.Item>
                      ),
                      style: { width: '100%' },
                    },
                    render: (text: any) => {
                      return (
                        <div>
                          {text.store_expire_type_item}
                          <span style={{ marginLeft: '10px' }}>
                            {
                              EXPIRE_TYPE.find(
                                (item) => item.value == text.store_expire_type,
                              )?.label
                            }
                          </span>
                        </div>
                      );
                    },
                  },
                  {
                    label: 'wms日期录入规则',
                    id: 'commonSelect',
                    name: 'date_in_type',
                    fieldProps: {
                      options: makeDate,
                    },
                  },
                  {
                    label: '进项税率',
                    id: ScmFieldKeyMap.taxRate,
                    name: 'input_tax_rate',
                    // fieldProps: {
                    //   // defaultValue: formValue?.item_info?.tax_rate,
                    //   options: inputRateList,
                    // },
                    rules: [{ required: true, message: '请输入进项税率' }],
                  },
                  {
                    label: '销项税率',
                    id: ScmFieldKeyMap.taxRate,
                    name: 'output_tax_rate',
                    fieldProps: {
                      defaultValue: 9,
                      // options: rateList,
                    },
                  },
                  {
                    label: '入库抽检',
                    id: 'commonSelect',
                    name: 'in_check',
                    fieldProps: {
                      defaultValue: 0,
                      options: inCheckList,
                    },
                  },
                  {
                    label: '商品属性',
                    itemSpan: 24,
                    id: ScmFieldKeyMap.productAttributes,
                    fieldProps: {
                      options: ITEM_ATTRIBUTE,
                    },
                    readOnly: false,
                    dependencies: ['state'],
                    disabled: (formValues: any) => {
                      return (
                        !hasAuth(['新品申请/采购谈判', '编辑']) ||
                        !(
                          formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                          formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                        )
                      );
                    },
                    name: 'check_list',
                  },
                  {
                    label: '商品说明',
                    id: 'commoneTextArea',
                    name: 'item_desc',
                    itemSpan: 24,
                    fieldProps: {
                      placeholder: '请输入',
                      autoSize: { minRows: 4, maxRows: 6 },
                    },
                  },
                  {
                    label: 'POS销售提醒',
                    id: 'commoneTextArea',
                    name: 'pos_info',
                    itemSpan: 24,
                    fieldProps: {
                      placeholder: '请输入',
                      autoSize: { minRows: 4, maxRows: 6 },
                    },
                  },
                ],
              },
            },
          ],
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '其他信息',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['state'],
                readOnly: (formValues: any) => {
                  return (
                    !hasAuth(['新品申请/采购谈判', '编辑']) ||
                    !(
                      formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                      formValues.state == 'COMPANY_ITEM_AUDIT_ING'
                    )
                  );
                },
                formList: [
                  {
                    label: '商品特色',
                    id: 'commoneTextArea',
                    name: 'item_feature',
                    fieldProps: {
                      placeholder: '请输入',
                      autoSize: { minRows: 4, maxRows: 6 },
                    },
                  },
                  {
                    label: '优惠政策',
                    id: 'commoneTextArea',
                    name: 'preferential_policy',
                    fieldProps: {
                      placeholder: '若无优惠政策，填写"无"',
                      autoSize: { minRows: 4, maxRows: 6 },
                    },
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.preferential_policy
                        : '****';
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    dependencies: ['state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          !hasAuth(['新品申请/采购谈判', '编辑']) ||
                          !(
                            formValues.state == 'NEGOTIATE_AUDIT_ING' ||
                            formValues.state == 'GROUP_ITEM_AUDIT_ING'
                          )
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ];
};

export default Item;
