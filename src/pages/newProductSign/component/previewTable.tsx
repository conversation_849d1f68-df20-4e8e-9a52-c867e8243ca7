import { XlbBaseUpload, XlbImage } from '@xlb/components';
import { Table } from 'antd';
import QRCode from 'qrcode.react';
import { useEffect, useState } from 'react';
import {
  ITEM_SOURCE,
  PAY_TYPE,
  SALES_CHANNELS,
  SUPPLIER_ATTRIBUTES,
} from '../data';
import Api from '../server';
import styles from './index.module.less';

const item = (props: any) => {
  const { data } = props;
  const [voteData, setVoteData] = useState<any>({});
  let intervalId: any;

  // 获取投票数据
  const fetchData = async () => {
    const res = await Api.voteresultfind({ fid: data.fid });
    if (res?.code == 0) {
      setVoteData(res.data);
    }
  };

  useEffect(() => {
    fetchData();
    // 设置定时器
    intervalId = setInterval(fetchData, 10000);

    // 清理函数：组件卸载时清除定时器
    return () => {
      clearInterval(intervalId);
    };
  }, []);
  const sharedOnCell = (_: number, index?: number) => {
    if (data?.item_category_name?.includes('散称')) {
      if (index === 10 || index === 11 || index === 12) {
        return { colSpan: 0 };
      }
    } else {
      if (index === 9 || index === 10 || index === 11 || index === 12) {
        return { colSpan: 0 };
      }
    }
    return {};
  };
  const column = [
    {
      title: '商品名称：',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: data?.item_name || '-',
      colSpan: 3,
      dataIndex: 'tel',
      align: 'left',
      width: 200,
      onCell: (_: number, index: any) => {
        if (data?.item_category_name?.includes('散称')) {
          return {
            colSpan: index === 10 || index === 11 || index === 12 ? 3 : 1,
          };
        } else {
          return {
            colSpan:
              index === 9 || index === 10 || index === 11 || index === 12
                ? 3
                : 1,
          };
        }
      },
    },
    {
      title: 'Phone',
      colSpan: 0,
      width: 200,
      dataIndex: 'phone',
      onCell: sharedOnCell,
    },
    {
      title: 'Phone1',
      colSpan: 0,
      width: 200,
      dataIndex: 'phone1',
      onCell: sharedOnCell,
    },

    {
      title: '商品图片',
      dataIndex: 'goodImage',
      width: 300,
      onCell: (_: number, index: any) => {
        if (index === 0) {
          return { rowSpan: 13 };
        }
        // These two are merged into above cell
        if (index !== 0) {
          return { rowSpan: 0 };
        }
        // if (index === 10 || index === 11 || index === 12) {
        //   return { colSpan: 0 };
        // }
        return {};
      },
    },
    {
      title: '二维码',
      dataIndex: 'goodDetail',
      width: 300,
      onCell: (_: number, index: any) => {
        if (index === 0) {
          return { rowSpan: 13 };
        }
        // These two are merged into above cell
        if (index !== 0) {
          return { rowSpan: 0 };
        }
        return {};
      },
    },
  ];

  const dataList = [
    {
      key: '1',
      name: '采购规格：',
      tel: data?.package_rule || '-',
      phone: '保质期（月/天）',
      phone1: data?.expire_type_string || '-',
      goodImage: (
        <div
          style={{
            maxWidth: '400px',
            height: 'calc(100vh - 360px)',
            overflowX: 'auto',
            margin: 'auto',
          }}
        >
          <XlbImage
            src={data?.images[0]?.url}
            preview
            width="100%"
            height="auto"
          />
        </div>
      ),
      goodDetail: data?.state == 'APPRAISE_AUDIT_ING' && (
        <div style={{ marginRight: '20px' }}>
          <QRCode
            style={{ width: '100%', height: '100%' }}
            id={'key'}
            value={`https://pga-test.xlbsoft.com?fid=${data?.fid}&vote_type=${'NEW_ITEM'}&source=${'SCM'}`}
          />
          <p style={{ textAlign: 'center' }}>打开微信扫一扫</p>
          <div style={{ textAlign: 'center' }}>
            <div>投票人数：{voteData?.vote_num}</div>
            <div>投票均分：{voteData?.vote_result}</div>
          </div>
        </div>
      ),
    },
    {
      key: '2',
      name: '产地：',
      tel: data?.origin_place || '-',
      phone: '供应商类型：',
      phone1: data?.supplier_type
        ? SUPPLIER_ATTRIBUTES.find((item) => item.value == data?.supplier_type)
            ?.label
        : '-',
    },
    {
      key: '3',
      name: '新品来源：',
      tel: data?.new_item_source
        ? ITEM_SOURCE.find((item) => item.value == data?.new_item_source)?.label
        : '-',
      phone: '斤规格：',
      phone1: data?.weight_spec || '-',
    },
    {
      key: '4',
      name: '采购价-斤：',
      tel: data?.wholesale_price_catty || '-',
      phone: '售价-斤：',
      phone1: data?.price || '-',
    },
    {
      key: '5',
      name: '采购价：',
      tel: data?.wholesale_price || '-',
      phone: '零售价：',
      phone1: data?.suggested_price || '-',
    },
    {
      key: '6',
      name: '箱价：',
      tel:
        data?.box_price !== undefined ? (data?.box_price || 0).toString() : '-',
      phone: '总毛利率：',
      phone1:
        data?.gross_profit_margin !== undefined
          ? ((data?.gross_profit_margin || 0) * 10000) / 100 + '%'
          : '-',
    },
    {
      key: '7',
      name: '预估PSD：',
      tel:
        data?.estimated_sale_psd !== undefined
          ? (data?.estimated_sale_psd || 0).toString()
          : '-',
      phone: '预估PSD金额：',
      phone1:
        data?.estimated_amount_psd !== undefined
          ? (data?.estimated_amount_psd || 0).toString()
          : '-',
    },
    {
      key: '8',
      name: '中位PSD数量：',
      tel:
        data?.psd_median_num !== undefined
          ? (data?.psd_median_num || 0).toString()
          : '-',
      phone: '中位PSD金额：',
      phone1:
        data?.psd_median_money !== undefined
          ? (data.psd_median_money || 0).toString()
          : '-',
    },
    {
      key: '9',
      name: '付款方式：',
      tel: data?.payment_method
        ? PAY_TYPE.find((item) => item.value == data?.payment_method)?.label
        : '-',
      phone: '新品视频：',
      phone1: (
        <XlbBaseUpload
          mode="look"
          hiddenControlerIcon={true}
          showUpload={false}
          listType={'text'}
          fileList={data?.new_item_videos}
        />
      ),
    },
    {
      key: '9',
      name: '销售渠道：',
      tel: data?.sales_channels
        ? SALES_CHANNELS.map((item: any) =>
            data?.sales_channels.includes(item?.value) ? item.label : null,
          )
            .filter(Boolean)
            .join(',')
        : '-',
      phone: '销售渠道最低价：',
      phone1: data?.sale_bottom_price || 0,
    },
    {
      key: '10',
      name: '政策：',
      tel: data?.policy || '无',
    },
    {
      key: '11',
      name: '上新理由：',
      tel: data?.up_new_reasons || '无',
    },
    {
      key: '12',
      name: '商品描述：',
      tel: data?.recommendation_reason || '无',
    },
  ];

  const noDataList = [
    {
      key: '1',
      name: '采购规格：',
      tel: data?.package_rule || '-',
      phone: '保质期（月/天）',
      phone1: data?.expire_type_string || '-',
      goodImage: (
        <div
          style={{
            maxWidth: '400px',
            height: 'calc(100vh - 360px)',
            overflowX: 'auto',
            margin: 'auto',
          }}
        >
          <XlbImage
            src={data?.images[0]?.url}
            preview
            width="100%"
            height="auto"
          />
        </div>
      ),
      goodDetail: data?.state == 'APPRAISE_AUDIT_ING' && (
        <div style={{ marginRight: '20px' }}>
          <QRCode
            style={{ width: '100%', height: '100%' }}
            id={'key'}
            value={`https://pga-test.xlbsoft.com?fid=${data?.fid}&vote_type=${'NEW_ITEM'}&source=${'SCM'}`}
          />
          <p style={{ textAlign: 'center' }}>打开微信扫一扫</p>
          <div style={{ textAlign: 'center' }}>
            <div>投票人数：{voteData?.vote_num}</div>
            <div>投票均分：{voteData?.vote_result}</div>
          </div>
        </div>
      ),
    },
    {
      key: '2',
      name: '产地：',
      tel: data?.origin_place || '-',
      phone: '供应商类型：',
      phone1: data?.supplier_type
        ? SUPPLIER_ATTRIBUTES.find((item) => item.value == data?.supplier_type)
            ?.label
        : '-',
    },
    {
      key: '3',
      name: '新品来源：',
      tel: data?.new_item_source
        ? ITEM_SOURCE.find((item) => item.value == data?.new_item_source)?.label
        : '-',
      phone: '采购价：',
      phone1: data?.wholesale_price || '-',
    },
    {
      key: '5',
      name: '零售价：',
      tel: data?.suggested_price || '-',
      phone: '箱价：',
      phone1:
        data?.box_price !== undefined ? (data?.box_price || 0).toString() : '-',
    },
    {
      key: '6',
      name: '总毛利率：',
      tel:
        data?.gross_profit_margin !== undefined
          ? ((data?.gross_profit_margin || 0) * 10000) / 100 + '%'
          : '-',
      phone: '预估PSD：',
      phone1:
        data?.estimated_sale_psd !== undefined
          ? (data?.estimated_sale_psd || 0).toString()
          : '-',
    },
    {
      key: '7',
      name: '预估PSD金额：',
      tel:
        data?.estimated_amount_psd !== undefined
          ? (data?.estimated_amount_psd || 0).toString()
          : '-',
      phone: '中位PSD数量：',
      phone1:
        data?.psd_median_num !== undefined
          ? (data?.psd_median_num || 0).toString()
          : '-',
    },
    {
      key: '8',
      name: '中位PSD金额：',
      tel:
        data?.psd_median_money !== undefined
          ? (data.psd_median_money || 0).toString()
          : '-',
      phone: '付款方式：',
      phone1: data?.payment_method
        ? PAY_TYPE.find((item) => item.value == data?.payment_method)?.label
        : '-',
    },
    {
      key: '9',
      name: '新品视频：',
      tel: (
        <XlbBaseUpload
          mode="look"
          hiddenControlerIcon={true}
          showUpload={false}
          listType={'text'}
          fileList={data?.new_item_videos}
        />
      ),
      phone: '销售渠道：',
      phone1: data?.sales_channels
        ? SALES_CHANNELS.map((item: any) =>
            data?.sales_channels.includes(item?.value) ? item.label : null,
          )
            .filter(Boolean)
            .join(',')
        : '-',
    },
    {
      key: '9',
      name: '销售渠道最低价：',
      tel: data?.sale_bottom_price || 0,
    },
    {
      key: '10',
      name: '政策：',
      tel: data?.policy || '无',
    },
    {
      key: '11',
      name: '上新理由：',
      tel: data?.up_new_reasons || '无',
    },
    {
      key: '12',
      name: '商品描述：',
      tel: data?.recommendation_reason || '无',
    },
  ];
  return (
    <div className={styles.voteContent}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex' }}>
          <div>
            <span>过会日期：</span>
            <span>{data?.meetings_date}</span>
          </div>
          <div style={{ marginLeft: '20px' }}>
            <span>提报部门：</span>
            <span>{data?.dept_name}</span>
          </div>
        </div>
        <div style={{ display: 'flex' }}>
          <div>
            <span>大类名称：</span>
            <span>{data?.item_public_category_name}</span>
          </div>
          <div style={{ marginLeft: '20px' }}>
            <span>品类经理：</span>
            <span>{data?.category_manager_name}</span>
          </div>
        </div>
      </div>
      <div className={styles.formTitle}>{data?.dept_name}-新品评审表</div>
      <div>
        <Table
          dataSource={
            data?.item_category_name?.includes('散称') ? dataList : noDataList
          }
          columns={column}
          bordered
          pagination={false}
          disableHover={true}
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '10px',
          borderBottom: '2px solid black',
        }}
      >
        <div>
          <div style={{ marginBottom: '8px' }}>
            在售渠道及价格等信息：
            {data?.sales_channel &&
              SALES_CHANNELS.find((item) => item.value == data?.sales_channel)
                ?.label}
          </div>
          <div style={{ marginBottom: '8px' }}>
            <span>汰换品：{data?.replace_item}|</span>
            <span style={{ marginLeft: '12px' }}>
              汰换品下架原因：{data?.replace_item_down_reason}|
            </span>
            <span style={{ marginLeft: '12px' }}>
              汰换品PSD：{data?.replace_item_psd}
            </span>
          </div>
          <div style={{ marginBottom: '8px' }}>备注：{data?.memo}</div>
          <div></div>
        </div>
      </div>
    </div>
  );
};
export default item;
