import { REGEX } from '@/constants/common';
import { executiveStandardType } from '@/constants/config/data';
import { ScmFieldKeyMap } from '@/constants/config/scm';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbIcon, XlbImage, XlbProDetail, XlbTooltip } from '@xlb/components';
import { message } from 'antd';
import copy from 'copy-to-clipboard';
import {
  CERTIFICATE_MAINTAINED,
  FoodSafeType,
  MONTHS_OPTION,
  NO_FOOD_CATEGORY_ID,
  PAY_TYPE,
  payment_method,
  Retail_method,
  SALES_CHANNELS,
  SUPPLIER_ATTRIBUTES,
} from '../data';
import styles from '../index.module.less';
import Api from '../server';

const ContactCompany: any[] = [
  {
    label: '万好',
    value: '万好',
  },
  {
    label: '万兴',
    value: '万兴',
  },
  {
    label: '万昌',
    value: '万昌',
  },
  {
    label: '万优',
    value: '万优',
  },
  {
    label: '万权',
    value: '万权',
  },
  {
    label: '万灿',
    value: '万灿',
  },
  {
    label: '万拓',
    value: '万拓',
  },
];

const Item = () => {
  const isCertificated = (formValue: any) =>
    LStorage.get('userInfo')?.supplier &&
    formValue.state === CERTIFICATE_MAINTAINED.value;
  const itemPicture = {
    url: 'https://hxl-erp-image-dev.oss-cn-hangzhou.aliyuncs.com/exampleImg2.png',
    name: '商品标签设计稿图片',
    uid: '888',
    suffix_type: 'png',
  };

  const executivePicture = {
    url: 'https://hxl-erp-image-dev.oss-cn-hangzhou.aliyuncs.com/exampleImg1.png',
    name: '商品执行的经备案的企业标准图片',
    uid: '888',
    suffix_type: 'png',
  };

  const compliancePicture = {
    url: 'https://hxl-erp-dev.oss-cn-hangzhou.aliyuncs.com/itemcompliancecertificate.jpeg',
    name: '商品合规证明资料',
    uid: '888',
    suffix_type: 'jpeg',
  };

  const reportPicture = {
    url: 'https://hxl-erp-dev.oss-cn-hangzhou.aliyuncs.com/%E5%9E%8B%E6%A3%80%E5%A4%96%E6%A3%80%E6%8A%A5%E5%91%8A%E7%A4%BA%E4%BE%8B%E8%AF%B4%E6%98%8E.jpeg',
    name: '型检外检报告示例图片',
    uid: '888',
    suffix_type: 'jpeg',
  };
  const TEMPLATE_URL =
    'https://hxl-erp-dev.oss-cn-hangzhou.aliyuncs.com/%E4%BE%9B%E8%B4%A7%E5%A3%B0%E6%98%8E%E6%A8%A1%E6%9D%BF.docx';
  return [
    {
      componentType: 'blueBar',
      fieldProps: {
        title: '基本信息',
      },
      dependencies: ['apply_mode'],
      hidden: (formValues: any) => {
        return formValues?.apply_mode !== 'HIRE_SPHERE';
      },
      name: 'item_info',
      children: [
        {
          componentType: 'form',
          fieldProps: {
            width: '100%',
            dependencies: ['fid', 'state'],
            readOnly: (formValues: any) => {
              return (
                (formValues.fid &&
                  formValues.state !== 'INIT' &&
                  (formValues.state !== 'REVIEW_AUDIT_ING' ||
                    !hasAuth(['新品申请/采购初审', '编辑'])) &&
                  (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                    formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                !hasAuth(['新品申请', '编辑']) ||
                isCertificated(formValues)
              );
            },
            formList: [
              {
                label: '联系公司',
                id: ScmFieldKeyMap?.contactCompanys,
                name: 'org_id',
                fieldProps: { placeholder: '请选择' },
                rules: [{ required: true, message: '请选择联系公司' }],
                render: (obj: any) => {
                  return obj?.org_name;
                },
              },
              {
                label: '供应商名称',
                id: 'commonInput',
                name: 'supplier_name',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入供应商名称' }],
              },
              {
                label: '供应商类型',
                id: 'commonSelect',
                name: 'supplier_type',
                fieldProps: {
                  placeholder: '请选择',
                  options: SUPPLIER_ATTRIBUTES,
                },
                rules: [{ required: true, message: '请选择供应商类型' }],
              },
              {
                label: '联系人',
                id: 'commonInput',
                name: 'contact',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入联系人' }],
              },
              {
                label: '联系电话',
                id: 'commonInput',
                name: 'phone',
                fieldProps: { placeholder: '请输入' },
                rules: [
                  { required: true, message: '请输入联系电话' },
                  { pattern: REGEX?.TEL, message: '手机号格式不正确' },
                ],
              },
            ],
          },
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '商品信息',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['fid', 'state'],
                readOnly: (formValues: any) => {
                  return (
                    (formValues.fid &&
                      formValues.state !== 'INIT' &&
                      (formValues.state !== 'REVIEW_AUDIT_ING' ||
                        !hasAuth(['新品申请/采购初审', '编辑'])) &&
                      (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                        formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                    !hasAuth(['新品申请', '编辑']) ||
                    isCertificated(formValues)
                  );
                },
                formList: [
                  {
                    label: '商品品牌',
                    id: ScmFieldKeyMap.productBrand,
                    name: 'item_brand_id',
                    fieldProps: {
                      placeholder: '请选择',
                    },
                    dependencies: [
                      'item_brand',
                      'item_brand_name',
                      'fid',
                      'state',
                    ],
                    rules: [
                      ({ getFieldsValue }: any) => {
                        const formValues = getFieldsValue(true);
                        return {
                          required: !(
                            (formValues.fid &&
                              formValues.state !== 'INIT' &&
                              (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                !hasAuth(['新品申请/采购初审', '编辑'])) &&
                              (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                            !hasAuth(['新品申请', '编辑']) ||
                            isCertificated(formValues)
                          ),
                          message: '请选择商品品牌',
                        };
                      },
                    ],
                    onChange: (_values: any, form: any, options: any) => {
                      form?.setFieldsValue({
                        center_item_brand_id: options?.center_id,
                        item_brand_name: options?.label,
                      });
                    },
                    render: (data: any) => (
                      <>{data.item_brand_name || data.item_brand}</>
                    ),
                  },
                  {
                    label: '商品名称',
                    id: 'commonInput',
                    name: 'item_name',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入商品名称' }],
                  },
                  {
                    label: '商品品类',
                    id: ScmFieldKeyMap.goodsCategoryId,
                    name: 'item_public_category_id',
                    fieldProps: { placeholder: '请选择' },
                    rules: [{ required: true, message: '请选择商品品类' }],
                    onChange: (value: any, form: any, options: any) => {
                      form.setFieldsValue({
                        item_public_category_name: options?.label,
                      });
                    },
                  },
                  {
                    label: '商品等级',
                    id: 'commonInput',
                    name: 'item_level',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入商品等级' }],
                  },
                  {
                    label: '产地',
                    id: 'commonInput',
                    name: 'origin_place',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入产地' }],
                  },
                  {
                    label: '是否执行企业标准',
                    id: ScmFieldKeyMap.executiveStandardType,
                    name: 'executive_standard_type',
                    fieldProps: { placeholder: '请选择' },
                    rules: [
                      { required: true, message: '请选择是否执行企业标准' },
                    ],
                  },
                  {
                    label: '产品执行标准',
                    id: ScmFieldKeyMap.executiveStandard,
                    name: 'executive_standard',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入产品执行标准' }],
                  },
                  {
                    label: '进口商品',
                    id: ScmFieldKeyMap.scmItemByBoolean,
                    name: 'imports',
                    fieldProps: { defaultValue: false },
                    rules: [{ required: true, message: '请输入进口商品' }],
                  },
                  {
                    label: '标签标识营养成分',
                    id: ScmFieldKeyMap.scmItemByBoolean,
                    name: 'is_nutritional_label',
                    fieldProps: { defaultValue: false },
                    rules: [
                      { required: true, message: '请选择标签标识营养成分' },
                    ],
                  },
                  {
                    label: '商品条形码',
                    id: 'commonInput',
                    name: 'bar_code',
                    fieldProps: { placeholder: '请输入' },
                  },
                  {
                    label: '外箱条形码',
                    id: 'commonInput',
                    name: 'box_bar_code',
                    fieldProps: { placeholder: '请输入' },
                  },
                  {
                    label: '采购规格',
                    id: 'commonInput',
                    name: 'package_rule',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入采购规格' }],
                  },
                  {
                    label: '采购换算率',
                    id: 'commonInput',
                    name: 'ratio',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入采购换算率' }],
                  },
                  {
                    label: '保质期（月/天）',
                    id: ScmFieldKeyMap.expireType,
                    rules: [
                      { required: true, message: '请选择保质期（月/天）' },
                    ],
                    render: (record: any) => {
                      return (
                        <div>
                          {record.expire_type_num}
                          {record.expire_type == 1 ? '天' : '月'}
                        </div>
                      );
                    },
                  },
                  {
                    label: '采购价',
                    id: 'commonInputNumber',
                    name: 'wholesale_price',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑']) ||
                          isCertificated(formValues)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    fieldProps: { placeholder: '请输入', precision: 2 },
                    rules: [{ required: true, message: '请输入采购价' }],
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.wholesale_price
                        : '****';
                    },
                  },
                  {
                    label: '建议零售价',
                    id: 'commonInputNumber',
                    name: 'suggested_price',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑']) ||
                          isCertificated(formValues)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    fieldProps: { placeholder: '请输入', precision: 2 },
                    rules: [{ required: true, message: '请输入建议零售价' }],
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.suggested_price
                        : '****';
                    },
                  },
                  {
                    label: '销售渠道',
                    id: 'commonSelect',
                    name: 'sales_channels',
                    fieldProps: {
                      placeholder: '请选择',
                      mode: 'multiple',
                      options: SALES_CHANNELS,
                    },
                    rules: [{ required: true, message: '请选择销售渠道' }],
                  },
                  {
                    label: '斤规格',
                    id: ScmFieldKeyMap.specUnit,
                    rules: [{ required: true, message: '请选择斤规格' }],
                    render: (record: any) => {
                      return (
                        <div>
                          {record.weight_spec}
                          <span style={{ marginLeft: '10px' }}>
                            {record.weight_spec_unit}
                          </span>
                        </div>
                      );
                    },
                  },
                  {
                    label: '付款方式',
                    id: 'commonSelect',
                    name: 'payment_method',
                    fieldProps: { placeholder: '请选择', options: PAY_TYPE },
                    rules: [{ required: true, message: '请选择付款方式' }],
                  },
                  {
                    label: '进项税率',
                    id: ScmFieldKeyMap.taxRate,
                    name: 'tax_rate',
                    fieldProps: { placeholder: '请选择' },
                    rules: [{ required: true, message: '请选择进项税率' }],
                    render: (record: any) => {
                      return record?.tax_rate;
                    },
                  },
                  {
                    label: '税收分类编码',
                    id: 'commonInput',
                    name: 'tax_no',
                    fieldProps: { placeholder: '请输入' },
                    rules: [
                      { required: true, message: '请输入税收分类编码' },
                      {
                        pattern: /^\d{1,19}$/,
                        message: '仅支持输入数字,且不能超过19位',
                      },
                    ],
                  },

                  {
                    label: '商品尺寸:长(cm)',
                    id: 'commonInput',
                    name: 'package_length',
                    fieldProps: { placeholder: '商品尺寸长(cm)' },
                    rules: [
                      { required: true, message: '请选择商品尺寸cm(长*宽*高)' },
                    ],
                  },
                  {
                    label: '宽(cm)',
                    id: 'commonInput',
                    name: 'package_width',
                    fieldProps: { placeholder: '商品尺寸宽(cm)' },
                    rules: [
                      { required: true, message: '请选择商品尺寸宽(cm)' },
                    ],
                  },
                  {
                    label: '高(cm)',
                    id: 'commonInput',
                    name: 'package_height',
                    fieldProps: { placeholder: '商品尺寸高(cm)' },
                    rules: [{ required: true, message: '商品尺寸高(cm)' }],
                  },
                  {
                    label: '外箱尺寸:长(cm)',
                    id: 'commonInput',
                    name: 'box_length',
                    fieldProps: { placeholder: '外箱尺寸长(cm)' },
                    rules: [
                      { required: true, message: '请选择外箱尺寸长(cm)' },
                    ],
                  },
                  {
                    label: '宽(cm)',
                    id: 'commonInput',
                    name: 'box_width',
                    fieldProps: { placeholder: '外箱尺寸宽(cm)' },
                    rules: [
                      { required: true, message: '请选择外箱尺寸宽(cm)' },
                    ],
                  },
                  {
                    label: '高(cm)',
                    id: 'commonInput',
                    name: 'box_height',
                    fieldProps: { placeholder: '外箱尺寸高(cm)' },
                    rules: [
                      { required: true, message: '请选择外箱尺寸高(cm)' },
                    ],
                  },

                  {
                    label: '卸货费（单位:元）',
                    id: 'commonInput',
                    name: 'unloading_fee',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑']))) ||
                          !hasAuth(['新品申请', '编辑']) ||
                          isCertificated(formValues)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    fieldProps: { placeholder: '请输入' },
                    rules: [
                      { required: true, message: '请输入卸货费（单位:元）' },
                    ],
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.unloading_fee
                        : '****';
                    },
                  },
                  {
                    label: '合同政策',
                    id: 'commoneTextArea',
                    name: 'contract_policy',
                    fieldProps: { placeholder: '请输入' },
                    rules: [{ required: true, message: '请输入合同政策' }],
                  },
                  {
                    label: '推荐理由及优势',
                    id: 'commoneTextArea',
                    name: 'recommendation_reason',
                    fieldProps: { placeholder: '请输入' },
                    rules: [
                      { required: true, message: '请输入推荐理由及优势' },
                    ],
                  },
                  {
                    label: '商品图片',
                    id: 'commonUpload',
                    name: 'images',
                    fieldProps: {
                      action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                      mode: 'textButton',
                      listType: 'text',
                      hiddenControlerIcon: false,
                      deleteByServer: false,
                      showUpload: true,
                      data: {
                        refType: 'SUPPLIER_AUDIT',
                        refId: 'sfsffefefe',
                      },
                    },
                    rules: [{ required: true, message: '请上传商品图片' }],
                  },
                ],
              },
            },
          ],
        },
        {
          componentType: 'customer',
          children: (formValues: any) => {
            return [
              {
                componentType: 'blueBar',
                fieldProps: {
                  title: '证件信息',
                  subTitle: (
                    <span
                      className={'link'}
                      onClick={async (e) => {
                        if (!formValues?.fid) return;
                        const res = await Api.infosDownloadZip(formValues.fid);
                        if (res?.data?.type === 'application/json')
                          message.error('未找到此新品申请单商品信息附件');
                        else {
                          const download = new Download();
                          download.filename = '证件信息下载.zip';
                          download.zip(res?.data);
                        }
                      }}
                    >
                      打包下载
                    </span>
                  ),
                },
                dependencies: ['state', 'fid'],
                hidden: (formValues: any) => {
                  return formValues.state == 'REVIEW_AUDIT_ING';
                },
                children: [
                  {
                    componentType: 'form',
                    fieldProps: {
                      width: '100%',
                      dependencies: ['fid', 'state', 'food_safe_audit_state'],
                      readOnly: (formValues: any) => {
                        return (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            !isCertificated(formValues) &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                            (!hasAuth(['新品申请/公司食安审核驳回', '编辑']) ||
                              formValues.food_safe_audit_state !==
                                FoodSafeType.oppose)) ||
                          !hasAuth(['新品申请', '编辑'])
                        );
                      },
                      formList: [
                        {
                          label: '商品标签设计稿',
                          id: 'commonUpload',
                          name: 'item_label_design_draft',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                CERTIFICATE_MAINTAINED.value,
                              message: '请上传商品标签设计稿',
                            }),
                          ],
                        },
                        {
                          label: '营养成分外检报告',
                          id: 'commonUpload',
                          name: 'nutrients_external_report',
                          dependencies: [
                            'item_public_category_id',
                            'is_nutritional_label',
                            'state',
                          ],
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                  CERTIFICATE_MAINTAINED.value &&
                                getFieldsValue(true)?.is_nutritional_label,
                              message: '请上传营养成分外检报告',
                            }),
                          ],
                          hidden: (formValues: any) =>
                            formValues.item_public_category_id ===
                            NO_FOOD_CATEGORY_ID,
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            accept: ['pdf'],
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '商品合规证明资料',
                          id: 'commonUpload',
                          name: 'item_compliance_certificate',
                          dependencies: ['item_public_category_id'],
                          hidden: (formValues: any) =>
                            formValues.item_public_category_id !==
                            NO_FOOD_CATEGORY_ID,
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '产品规格书',
                          // id: 'commonUpload',
                          id: ScmFieldKeyMap?.formSpanItemLarge,
                          name: 'product_spec_url',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            accept: ['xls', 'pdf'],
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                CERTIFICATE_MAINTAINED.value,
                              message: '请上传产品规格书',
                            }),
                          ],
                        },
                        {
                          label: '标签外检报告',
                          id: 'commonUpload',
                          name: 'label_third_party_inspection',
                          dependencies: ['state'],
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                CERTIFICATE_MAINTAINED.value,
                              message: '请上传标签外检报告',
                            }),
                          ],
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            accept: ['pdf'],
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '商标注册证',
                          id: 'commonUpload',
                          name: 'trademark_register',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '商品执行的经备案的企业标准文件',
                          id: ScmFieldKeyMap?.formSpanItemLarge,
                          name: 'executive_standard_url',
                          dependencies: ['executive_standard_type'],
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                  CERTIFICATE_MAINTAINED.value &&
                                getFieldsValue(true)
                                  ?.executive_standard_type ===
                                  executiveStandardType.COMPANY_STANDARDS,
                              message: '请上传商品执行的经备案的企业标准文件',
                            }),
                          ],
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            accept: ['pdf'],
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '其他附件',
                          id: 'commonUpload',
                          name: 'other_sccessories_url',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                      ],
                    },
                  },
                  {
                    componentType: 'form',
                    fieldProps: {
                      width: '100%',
                      dependencies: ['fid', 'state', 'food_safe_audit_state'],
                      readOnly: (formValues: any) => {
                        return (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            !isCertificated(formValues) &&
                            formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                            formValues?.food_safe_audit_state !==
                              FoodSafeType.waitAudit &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                            (!hasAuth(['新品申请/公司食安审核驳回', '编辑']) ||
                              formValues.food_safe_audit_state !==
                                FoodSafeType.oppose)) ||
                          !hasAuth(['新品申请', '编辑'])
                        );
                      },
                      formList: [
                        {
                          id: ScmFieldKeyMap.externalReportList,
                          name: 'supplier_quality_record_data_list',
                          itemSpan: 24,
                          label: '型检外检报告',
                          dependencies: [
                            'supplier_id',
                            'state',
                            'food_safe_audit_state',
                            'imports',
                          ],
                          hidden: (formValues: any) => {
                            return (
                              formValues?.food_safe_audit_state ===
                                FoodSafeType.waitAudit &&
                              !isCertificated(formValues)
                            );
                          },
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                  CERTIFICATE_MAINTAINED.value &&
                                !getFieldsValue(true)?.imports,
                              message: '请上传型检外检报告',
                            }),
                          ],
                          render: (obj: any) => {
                            // 自定义只读模式
                            const dataList =
                              obj?.supplier_quality_record_data_list;
                            return (
                              <div>
                                {dataList?.map((item: any) => {
                                  const itemData = {
                                    ...item,
                                    ...item?.supplier_item,
                                  };
                                  console.log('此时的itemData是：', itemData);
                                  return (
                                    <div className={styles.detailContent}>
                                      <XlbProDetail
                                        initialValues={itemData}
                                        formList={[
                                          {
                                            componentType: 'form',
                                            fieldProps: {
                                              readOnly: true,
                                              width: '100%',
                                              formList: [
                                                {
                                                  label: '',
                                                  itemSpan: 1,
                                                  id: 'commonUpload',
                                                  name: 'supplier_quality_record_files',
                                                  fieldProps: {
                                                    action:
                                                      '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                                    mode: 'textButton',
                                                    // accept: ['pdf'],
                                                    listType: 'text',
                                                    hiddenControlerIcon: false,
                                                    deleteByServer: false,
                                                    showUpload: true,
                                                    data: {
                                                      refType: 'SUPPLIER_AUDIT',
                                                      refId: 'sfsffefefe',
                                                    },
                                                  },
                                                },
                                                {
                                                  label: '生产厂商',
                                                  id: 'commonInput',
                                                  itemSpan: 6,
                                                  name: 'producer_supplier_name',
                                                  render: (formValues) => {
                                                    return formValues?.producer_supplier_name ? (
                                                      <div className="v-flex">
                                                        {
                                                          formValues?.producer_supplier_name
                                                        }
                                                        <XlbIcon
                                                          name="fuzhi"
                                                          className="custom-form-item-icon"
                                                          onClick={() => {
                                                            copy(
                                                              formValues?.producer_supplier_name,
                                                            );
                                                            message.success(
                                                              '复制成功',
                                                            );
                                                          }}
                                                        />
                                                      </div>
                                                    ) : (
                                                      <div>-</div>
                                                    );
                                                  },
                                                },
                                                {
                                                  label: '外检报告签发日期',
                                                  id: 'commonInput',
                                                  itemSpan: 5,
                                                  name: 'valid_date',
                                                },
                                                {
                                                  label: '外检报告截止时间',
                                                  id: 'commonInput',
                                                  name: 'end_date',
                                                  itemSpan: 5,
                                                  dependencies: ['valid_date'],
                                                  render: (formValues) => {
                                                    console.log(
                                                      '外检报告截止时间',
                                                      formValues,
                                                    );
                                                    return (
                                                      <div>
                                                        {
                                                          MONTHS_OPTION.find(
                                                            (v) =>
                                                              v.value ==
                                                              formValues.end_date,
                                                          )?.label
                                                        }
                                                      </div>
                                                    );
                                                  },
                                                },
                                                {
                                                  label: '产地',
                                                  id: 'commonInput',
                                                  itemSpan: 6,
                                                  name: 'origin_place',
                                                },
                                              ],
                                            },
                                          },
                                        ]}
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            );
                          },
                        },
                        {
                          id: ScmFieldKeyMap.disabledExternalReportList,
                          name: 'supplier_quality_record_data_list',
                          itemSpan: 24,
                          label: '型检外检报告',
                          dependencies: [
                            'supplier_id',
                            'state',
                            'food_safe_audit_state',
                            'imports',
                          ],
                          hidden: (formValues: any) => {
                            return (
                              formValues?.food_safe_audit_state !==
                                FoodSafeType.waitAudit ||
                              isCertificated(formValues)
                            );
                          },
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                  CERTIFICATE_MAINTAINED.value &&
                                !getFieldsValue(true)?.imports,
                              message: '请上传型检外检报告',
                            }),
                          ],
                          render: (obj: any) => {
                            // 自定义只读模式
                            const dataList =
                              obj?.supplier_quality_record_data_list;
                            return (
                              <div>
                                {dataList?.map((item: any) => {
                                  const itemData = {
                                    ...item,
                                    ...item?.supplier_item,
                                  };
                                  console.log('此时的itemData是：', itemData);
                                  return (
                                    <div className={styles.detailContent}>
                                      <XlbProDetail
                                        initialValues={itemData}
                                        formList={[
                                          {
                                            componentType: 'form',
                                            fieldProps: {
                                              readOnly: true,
                                              width: '100%',
                                              formList: [
                                                {
                                                  label: '',
                                                  itemSpan: 1,
                                                  id: 'commonUpload',
                                                  name: 'supplier_quality_record_files',
                                                  fieldProps: {
                                                    action:
                                                      '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                                    mode: 'textButton',
                                                    // accept: ['pdf'],
                                                    listType: 'text',
                                                    hiddenControlerIcon: false,
                                                    deleteByServer: false,
                                                    showUpload: true,
                                                    data: {
                                                      refType: 'SUPPLIER_AUDIT',
                                                      refId: 'sfsffefefe',
                                                    },
                                                  },
                                                },
                                                {
                                                  label: '生产厂商',
                                                  id: 'commonInput',
                                                  itemSpan: 6,
                                                  name: 'producer_supplier_name',
                                                  render: (formValues) => {
                                                    return formValues?.producer_supplier_name ? (
                                                      <div className="v-flex">
                                                        {
                                                          formValues?.producer_supplier_name
                                                        }
                                                        <XlbIcon
                                                          name="fuzhi"
                                                          className="custom-form-item-icon"
                                                          onClick={() => {
                                                            copy(
                                                              formValues?.producer_supplier_name,
                                                            );
                                                            message.success(
                                                              '复制成功',
                                                            );
                                                          }}
                                                        />
                                                      </div>
                                                    ) : (
                                                      <div>-</div>
                                                    );
                                                  },
                                                },
                                                {
                                                  label: '外检报告签发日期',
                                                  id: 'commonInput',
                                                  itemSpan: 5,
                                                  name: 'valid_date',
                                                },
                                                {
                                                  label: '外检报告截止时间',
                                                  id: 'commonInput',
                                                  name: 'end_date',
                                                  itemSpan: 5,
                                                  dependencies: ['valid_date'],
                                                  render: (formValues) => {
                                                    console.log(
                                                      '外检报告截止时间',
                                                      formValues,
                                                    );
                                                    return (
                                                      <div>
                                                        {
                                                          MONTHS_OPTION.find(
                                                            (v) =>
                                                              v.value ==
                                                              formValues.end_date,
                                                          )?.label
                                                        }
                                                      </div>
                                                    );
                                                  },
                                                },
                                                {
                                                  label: '产地',
                                                  id: 'commonInput',
                                                  itemSpan: 6,
                                                  name: 'origin_place',
                                                },
                                              ],
                                            },
                                          },
                                        ]}
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            );
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            ];
          },
        },
        {
          componentType: 'customer',
          children: (formValues: any) => {
            return [
              {
                componentType: 'blueBar',
                fieldProps: {
                  title: '证件信息',
                  subTitle: (
                    <span
                      className={'link'}
                      onClick={async (e) => {
                        if (!formValues?.fid) return;
                        const res = await Api.infosDownloadZip(formValues.fid);
                        if (res?.data?.type === 'application/json')
                          message.error('未找到此新品申请单商品信息附件');
                        else {
                          const download = new Download();
                          download.filename = '证件信息下载.zip';
                          download.zip(res?.data);
                        }
                      }}
                    >
                      打包下载
                    </span>
                  ),
                },
                dependencies: ['state', 'fid'],
                hidden: (formValues: any) => {
                  return formValues.state !== 'REVIEW_AUDIT_ING';
                },
                children: [
                  {
                    componentType: 'form',
                    fieldProps: {
                      width: '100%',
                      dependencies: ['fid', 'state'],
                      readOnly: (formValues: any) => {
                        return (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            !isCertificated(formValues) &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑'])
                        );
                      },
                      formList: [
                        {
                          label: '商品标签设计稿',
                          id: 'commonUpload',
                          name: 'item_label_design_draft',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '营养成分外检报告',
                          id: 'commonUpload',
                          name: 'nutrients_external_report',
                          dependencies: [
                            'item_public_category_id',
                            'is_nutritional_label',
                            'state',
                          ],
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                  CERTIFICATE_MAINTAINED.value &&
                                getFieldsValue(true)?.is_nutritional_label,
                              message: '请上传营养成分外检报告',
                            }),
                          ],
                          hidden: (formValues: any) =>
                            formValues.item_public_category_id ===
                            NO_FOOD_CATEGORY_ID,
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            accept: ['pdf'],
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '商品合规证明资料',
                          id: 'commonUpload',
                          name: 'item_compliance_certificate',
                          dependencies: ['item_public_category_id'],
                          hidden: (formValues: any) =>
                            formValues.item_public_category_id !==
                            NO_FOOD_CATEGORY_ID,
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '产品规格书',
                          id: ScmFieldKeyMap?.formSpanItemLarge,
                          // id: 'commonUpload',
                          name: 'product_spec_url',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            // accept: ['xlsx', 'XLSX'],
                            accept: ['xls', 'pdf'],
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '标签外检报告',
                          id: 'commonUpload',
                          name: 'label_third_party_inspection',
                          dependencies: ['state'],
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                CERTIFICATE_MAINTAINED.value,
                              message: '请上传标签外检报告',
                            }),
                          ],
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            accept: ['pdf'],
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '商标注册证',
                          id: 'commonUpload',
                          name: 'trademark_register',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '商品执行的经备案的企业标准文件',
                          id: ScmFieldKeyMap?.formSpanItemLarge,
                          // id: 'commonUpload',
                          name: 'executive_standard_url',
                          dependencies: ['executive_standard_type'],
                          rules: [
                            ({ getFieldsValue }: any) => ({
                              required:
                                getFieldsValue(true)?.state ===
                                  CERTIFICATE_MAINTAINED.value &&
                                getFieldsValue(true)
                                  ?.executive_standard_type ===
                                  executiveStandardType.COMPANY_STANDARDS,
                              message: '请上传商品执行的经备案的企业标准文件',
                            }),
                          ],
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            accept: ['pdf'],
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                        {
                          label: '其他附件',
                          id: 'commonUpload',
                          name: 'other_sccessories_url',
                          fieldProps: {
                            action:
                              '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                            mode: 'textButton',
                            listType: 'text',
                            hiddenControlerIcon: false,
                            deleteByServer: false,
                            showUpload: true,
                            data: {
                              refType: 'SUPPLIER_AUDIT',
                              refId: 'sfsffefefe',
                            },
                          },
                        },
                      ],
                    },
                  },
                  {
                    componentType: 'form',
                    fieldProps: {
                      width: '100%',
                      dependencies: ['fid', 'state', 'food_safe_audit_state'],
                      readOnly: (formValues: any) => {
                        return (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            !isCertificated(formValues) &&
                            formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                            formValues?.food_safe_audit_state !==
                              FoodSafeType.waitAudit &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑'])
                        );
                      },
                      formList: [
                        {
                          id: ScmFieldKeyMap.externalReportListNoRules,
                          name: 'supplier_quality_record_data_list',
                          itemSpan: 24,
                          label: '型检外检报告',
                          dependencies: ['supplier_id', 'state'],
                          render: (obj: any) => {
                            // 自定义只读模式
                            const dataList =
                              obj?.supplier_quality_record_data_list;
                            return (
                              <div>
                                {dataList?.map((item: any) => {
                                  const itemData = {
                                    ...item,
                                    ...item?.supplier_item,
                                  };
                                  return (
                                    <div className={styles.detailContent}>
                                      <XlbProDetail
                                        initialValues={itemData}
                                        formList={[
                                          {
                                            componentType: 'form',
                                            fieldProps: {
                                              readOnly: true,
                                              width: '100%',
                                              formList: [
                                                {
                                                  label: '',
                                                  itemSpan: 1,
                                                  id: 'commonUpload',
                                                  name: 'supplier_quality_record_files',
                                                  fieldProps: {
                                                    action:
                                                      '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                                    mode: 'textButton',
                                                    listType: 'text',
                                                    hiddenControlerIcon: false,
                                                    deleteByServer: false,
                                                    showUpload: true,
                                                    data: {
                                                      refType: 'SUPPLIER_AUDIT',
                                                      refId: 'sfsffefefe',
                                                    },
                                                  },
                                                },
                                                {
                                                  label: '生产厂商',
                                                  id: 'commonInput',
                                                  itemSpan: 6,
                                                  name: 'producer_supplier_name',
                                                  render: (formValues) => {
                                                    return formValues?.producer_supplier_name ? (
                                                      <div className="v-flex">
                                                        {
                                                          formValues?.producer_supplier_name
                                                        }
                                                        <XlbIcon
                                                          name="fuzhi"
                                                          className="custom-form-item-icon"
                                                          onClick={() => {
                                                            copy(
                                                              formValues?.producer_supplier_name,
                                                            );
                                                            message.success(
                                                              '复制成功',
                                                            );
                                                          }}
                                                        />
                                                      </div>
                                                    ) : (
                                                      <div>-</div>
                                                    );
                                                  },
                                                },
                                                {
                                                  label: '外检报告签发日期',
                                                  id: 'commonInput',
                                                  itemSpan: 5,
                                                  name: 'valid_date',
                                                },
                                                {
                                                  label: '外检报告截止时间',
                                                  id: 'commonInput',
                                                  name: 'end_date',
                                                  itemSpan: 5,
                                                  dependencies: ['valid_date'],
                                                  render: (formValues) => {
                                                    console.log(
                                                      '外检报告截止时间',
                                                      formValues,
                                                    );
                                                    return (
                                                      <div>
                                                        {
                                                          MONTHS_OPTION.find(
                                                            (v) =>
                                                              v.value ==
                                                              formValues.end_date,
                                                          )?.label
                                                        }
                                                      </div>
                                                    );
                                                  },
                                                },
                                                {
                                                  label: '产地',
                                                  id: 'commonInput',
                                                  itemSpan: 6,
                                                  name: 'origin_place',
                                                },
                                              ],
                                            },
                                          },
                                        ]}
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            );
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            ];
          },
        },
      ],
    },
    {
      componentType: 'blueBar',
      fieldProps: {
        title: '商品信息', // 招募渠道
      },
      dependencies: ['apply_mode'],
      hidden: (formValues: any) => {
        return formValues?.apply_mode == 'HIRE_SPHERE';
      },
      name: 'item_info',
      children: [
        {
          componentType: 'form',
          fieldProps: {
            width: '100%',
            dependencies: ['fid', 'state'],
            readOnly: (formValues: any) => {
              return (
                (formValues.fid &&
                  formValues.state !== 'INIT' &&
                  (formValues.state !== 'REVIEW_AUDIT_ING' ||
                    !hasAuth(['新品申请/采购初审', '编辑'])) &&
                  (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                    formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                !hasAuth(['新品申请', '编辑']) ||
                isCertificated(formValues)
              );
            },
            formList: [
              {
                label: '联系公司',
                id: ScmFieldKeyMap.contactCompanys,
                name: 'org_id',
                fieldProps: { placeholder: '请选择' },
                rules: [{ required: true, message: '请选择联系公司' }],
              },

              {
                label: '商品名称',
                id: 'commonInput',
                name: 'item_name',
                fieldProps: {
                  placeholder:
                    '品牌 + 品名 + 口味 + 克重 如:奥利奥夹心饼干(草莓味)',
                },
                rules: [{ required: true, message: '请输入商品名称' }],
              },
              {
                label: '商品品牌',
                id: ScmFieldKeyMap.productBrand,
                name: 'item_brand_id',
                fieldProps: {
                  placeholder: '请选择',
                },
                dependencies: ['item_brand', 'item_brand_name', 'fid', 'state'],
                rules: [
                  ({ getFieldsValue }: any) => {
                    const formValues = getFieldsValue(true);
                    return {
                      required: !(
                        (formValues.fid &&
                          formValues.state !== 'INIT' &&
                          (formValues.state !== 'REVIEW_AUDIT_ING' ||
                            !hasAuth(['新品申请/采购初审', '编辑'])) &&
                          (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                            formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                        !hasAuth(['新品申请', '编辑']) ||
                        isCertificated(formValues)
                      ),
                      message: '请选择商品品牌',
                    };
                  },
                ],
                onChange: (_values: any, form: any, options: any) => {
                  form?.setFieldsValue({
                    center_item_brand_id: options?.center_id,
                    item_brand_name: options?.label,
                  });
                },
                render: (data: any) => (
                  <>{data.item_brand_name || data.item_brand}</>
                ),
              },
              {
                label: '商品条码',
                id: 'commonInput',
                name: 'bar_code',
                dependencies: ['retail_method'],
                hidden: (formValues: any) => {
                  return formValues.retail_method == 0;
                },
                fieldProps: {
                  placeholder: '定量装输入商品销售条码，散称条码商品无需输入',
                },
                rules: [{ required: true, message: '请输入商品条码' }],
              },
              {
                label: '商品条码',
                id: 'commonInput',
                name: 'bar_code',
                dependencies: ['retail_method'],
                hidden: (formValues: any) => {
                  return formValues.retail_method !== 0;
                },
                fieldProps: {
                  placeholder: '定量装输入商品销售条码，散称条码商品无需输入',
                },
              },
              {
                label: '采购规格',
                id: 'commonInput',
                name: 'package_spec',
                fieldProps: { placeholder: '录入采购规格 如：1*10盒*12瓶' },
                rules: [{ required: true, message: '请输入包装规格' }],
              },
              {
                label: '采购换算率',
                id: 'commonInput',
                name: 'ratio',
                fieldProps: {
                  placeholder:
                    '示例:1箱有15瓶水，按照1瓶售卖，采购换算率填15；1箱有5kg，按照公斤售卖，采购换算率填5',
                },
                rules: [
                  { required: true, message: '请填入换算率' },
                  {
                    pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                    message: '请输入正确格式的数据',
                  },
                ],
              },
              {
                label: '付款方式',
                id: 'commonSelect',
                name: 'payment_method',
                fieldProps: { placeholder: '请选择', options: payment_method },
                rules: [{ required: true, message: '请选择付款方式' }],
              },
              {
                label: '卸货费(单位:元)',
                id: 'commonInput',
                name: 'unloading_fee',
                fieldProps: { placeholder: '请输入' },
                rules: [
                  { required: true, message: '请填入卸货费(单位:元)' },
                  {
                    pattern: /^\d+(\.{0,1}\d+){0,1}$/,
                    message: '请输入正确格式的数据',
                  },
                ],
              },
              {
                label: '零售方式',
                id: 'commonSelect',
                name: 'retail_method',
                fieldProps: { placeholder: '请选择', options: Retail_method },
                rules: [{ required: true, message: '请选择零售方式' }],
                onChange: (value: any, form: any) => {
                  form.setFieldsValue({
                    enable_package_bar_code: value === 1,
                    package_bar_code: null,
                  });
                },
              },
              {
                label: '商品等级',
                id: 'commonInput',
                name: 'item_level',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入商品等级' }],
              },
              {
                label: '斤规格',
                id: ScmFieldKeyMap.specUnit,
                rules: [{ required: true, message: '请选择斤规格' }],
                render: (record: any) => {
                  return (
                    <div>
                      {record.weight_spec}
                      <span style={{ marginLeft: '10px' }}>
                        {record.weight_spec_unit}
                      </span>
                    </div>
                  );
                },
              },
              {
                label: '保质期（月/天）',
                id: ScmFieldKeyMap.expireType,
                rules: [{ required: true, message: '请选择保质期（月/天）' }],
                render: (record: any) => {
                  return (
                    <div>
                      {record.expire_type_num}
                      {record.expire_type == 1 ? '天' : '月'}
                    </div>
                  );
                },
              },
              {
                label: '外箱码',
                id: ScmFieldKeyMap.packageBar,
                name: 'package_bar_code',
                rules: [
                  {
                    pattern: /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/,
                    message: '请输入数字',
                  },
                ],
                render: (record: any) => {
                  return (
                    <div>
                      {record.enable_package_bar_code ? '有' : '无'}
                      <span style={{ marginLeft: '10px' }}>
                        {record.package_bar_code}
                      </span>
                    </div>
                  );
                },
              },
              {
                label: '进项税率',
                id: ScmFieldKeyMap.taxRate,
                name: 'tax_rate',
                fieldProps: { placeholder: '请选择' },
                rules: [{ required: true, message: '请选择进项税率' }],
              },
              {
                label: '税收分类编码',
                id: 'commonInput',
                name: 'item_tax_no',
                fieldProps: { placeholder: '请输入' },
                // rules: [
                //   { required: true, message: '请输入税收分类编码' },
                //   {
                //     pattern: /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/,
                //     message: '请输入数字',
                //   },
                // ],
                rules: [
                  { required: true, message: '请输入税收分类编码' },
                  {
                    pattern: /^\d{1,19}$/,
                    message: '仅支持输入数字,且不能超过19位',
                  },
                ],
              },
              {
                label: '产地',
                id: 'commonInput',
                name: 'origin_place',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入产地' }],
              },
              {
                id: ScmFieldKeyMap.goodsCategoryId,
                name: 'item_public_category_id',
                label: '商品品类',
                fieldProps: { placeholder: '请选择' },
                rules: [{ required: true, message: '请选择商品品类' }],
                onChange: (value: any, form: any, options: any) => {
                  form.setFieldsValue({
                    item_public_category_name: options?.label,
                  });
                },
              },
              {
                label: '是否执行企业标准',
                id: ScmFieldKeyMap.executiveStandardType,
                name: 'executive_standard_type',
                fieldProps: { placeholder: '请选择' },
                rules: [{ required: true, message: '请选择是否执行企业标准' }],
              },
              {
                label: '产品执行标准',
                id: ScmFieldKeyMap.executiveStandard,
                name: 'executive_standard',
                fieldProps: { placeholder: '请输入' },
                rules: [{ required: true, message: '请输入产品执行标准' }],
              },
              {
                label: '进口商品',
                id: ScmFieldKeyMap.scmItemByBoolean,
                name: 'imports',
                fieldProps: { defaultValue: false },
                rules: [{ required: true, message: '请输入进口商品' }],
              },
              {
                label: '标签标识营养成分',
                id: ScmFieldKeyMap.scmItemByBoolean,
                name: 'is_nutritional_label',
                fieldProps: { defaultValue: false },
                rules: [{ required: true, message: '请选择标签标识营养成分' }],
              },
            ],
          },
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '单位信息',
          },
          children: [
            {
              componentType: 'subTitle',
              fieldProps: {
                title: '零售单位',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      return (
                        (formValues.fid &&
                          formValues.state !== 'INIT' &&
                          (formValues.state !== 'REVIEW_AUDIT_ING' ||
                            !hasAuth(['新品申请/采购初审', '编辑'])) &&
                          (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                            formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                        !hasAuth(['新品申请', '编辑']) ||
                        isCertificated(formValues)
                      );
                    },
                    formList: [
                      {
                        label: '长',
                        id: ScmFieldKeyMap.centimeter,
                        name: 'retail_length',
                      },
                      {
                        label: '宽',
                        id: ScmFieldKeyMap.centimeter,
                        name: 'retail_width',
                      },
                      {
                        label: '高',
                        id: ScmFieldKeyMap.centimeter,
                        name: 'retail_height',
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'subTitle',
              fieldProps: {
                title: '采购单位',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      return (
                        (formValues.fid &&
                          formValues.state !== 'INIT' &&
                          (formValues.state !== 'REVIEW_AUDIT_ING' ||
                            !hasAuth(['新品申请/采购初审', '编辑'])) &&
                          (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                            formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                        !hasAuth(['新品申请', '编辑']) ||
                        isCertificated(formValues)
                      );
                    },
                    formList: [
                      {
                        label: '长',
                        id: ScmFieldKeyMap.centimeter,
                        name: 'purchase_length',
                      },
                      {
                        label: '宽',
                        id: ScmFieldKeyMap.centimeter,
                        name: 'purchase_width',
                      },
                      {
                        label: '高',
                        id: ScmFieldKeyMap.centimeter,
                        name: 'purchase_height',
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '价格信息',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                width: '100%',
                dependencies: ['fid', 'state'],
                readOnly: (formValues: any) => {
                  return (
                    (formValues.fid &&
                      formValues.state !== 'INIT' &&
                      (formValues.state !== 'REVIEW_AUDIT_ING' ||
                        !hasAuth(['新品申请/采购初审', '编辑'])) &&
                      (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                        formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                    !hasAuth(['新品申请', '编辑']) ||
                    isCertificated(formValues)
                  );
                },
                formList: [
                  {
                    label: '建议零售价(元)',
                    id: 'commonInputNumber',
                    name: 'suggested_price',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑']) ||
                          isCertificated(formValues)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    fieldProps: {
                      placeholder: '最小零售单位价格，称重按公斤',
                      min: 0,
                      precision: 2,
                    },
                    rules: [
                      { required: true, message: '请输入建议零售价(元)' },
                    ],
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.suggested_price
                        : '****';
                    },
                  },
                  {
                    label: '供应价(元)',
                    id: 'commonInputNumber',
                    name: 'purchase_price',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑']) ||
                          isCertificated(formValues)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    fieldProps: {
                      placeholder: '最小零售单位价格，称重按公斤',
                      min: 0,
                      precision: 2,
                    },
                    rules: [{ required: true, message: '请输入供应价(元)' }],
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.purchase_price
                        : '****';
                    },
                  },
                  {
                    label: '整件供应价(元)',
                    id: 'commonInputNumber',
                    name: 'whole_supply_price',
                    dependencies: ['fid', 'state'],
                    readOnly: (formValues: any) => {
                      if (!hasAuth(['新品申请/采购谈判价及政策', '查询'])) {
                        return true;
                      } else {
                        if (
                          (formValues.fid &&
                            formValues.state !== 'INIT' &&
                            (formValues.state !== 'REVIEW_AUDIT_ING' ||
                              !hasAuth(['新品申请/采购初审', '编辑'])) &&
                            (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                              formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                          !hasAuth(['新品申请', '编辑']) ||
                          isCertificated(formValues)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    },
                    disabled: !hasAuth(['新品申请/采购谈判价及政策', '编辑']),
                    fieldProps: {
                      placeholder: '整件供应价',
                      min: 0,
                      precision: 2,
                    },
                    rules: [
                      { required: true, message: '请输入整件供应价(元)' },
                    ],
                    render: (text: any) => {
                      return hasAuth(['新品申请/采购谈判价及政策', '查询'])
                        ? text?.whole_supply_price
                        : '****';
                    },
                  },
                ],
              },
            },
          ],
        },
        {
          componentType: 'customer',
          children: (formValues: any) => {
            return [
              {
                componentType: 'blueBar',
                fieldProps: {
                  title: '附件',
                  subTitle: (
                    <span
                      className={'link'}
                      onClick={async (e) => {
                        if (!formValues?.fid) return;
                        const res = await Api.infosDownloadZip(formValues.fid);
                        if (res?.data?.type === 'application/json')
                          message.error('未找到此新品申请单商品信息附件');
                        else {
                          const download = new Download();
                          download.filename = '证件信息下载.zip';
                          download.zip(res?.data);
                        }
                      }}
                    >
                      打包下载
                    </span>
                  ),
                },
                children: [
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span>型检外检报告</span>
                          <XlbTooltip title="每一个标签对应的型检外检报告单独上传">
                            <XlbIcon
                              color="#979faa"
                              hoverColor="#3D66FE"
                              name="bangzhu"
                              size={16}
                              style={{ margin: '0 8px' }}
                            />
                          </XlbTooltip>
                          <span
                            className="cursors"
                            onClick={() => {
                              XlbImage?.openImageItem(reportPicture);
                            }}
                          >
                            示例图
                          </span>
                        </div>
                      ),
                    },
                    children: [
                      {
                        componentType: 'form',
                        dependencies: ['fid'],
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                formValues?.food_safe_audit_state !==
                                  FoodSafeType.waitAudit &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              id: ScmFieldKeyMap.externalReportList,
                              name: 'supplier_quality_record_data_list',
                              itemSpan: 24,
                              dependencies: [
                                'supplier_id',
                                'state',
                                'food_safe_audit_state',
                                'imports',
                              ],
                              hidden: (formValues: any) => {
                                return (
                                  formValues?.food_safe_audit_state ===
                                    FoodSafeType.waitAudit &&
                                  !isCertificated(formValues)
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                      CERTIFICATE_MAINTAINED.value &&
                                    !getFieldsValue(true)?.imports,
                                  message: '请上传型检外检报告',
                                }),
                              ],
                              render: (obj: any) => {
                                // 自定义只读模式
                                const dataList =
                                  obj?.supplier_quality_record_data_list;
                                return (
                                  <div>
                                    {dataList?.map((item: any) => {
                                      const itemData = {
                                        ...item,
                                        ...item?.supplier_item,
                                      };
                                      return (
                                        <div className={styles.detailContent}>
                                          <XlbProDetail
                                            initialValues={itemData}
                                            formList={[
                                              {
                                                componentType: 'form',
                                                fieldProps: {
                                                  readOnly: true,
                                                  width: '100%',
                                                  formList: [
                                                    {
                                                      label: '',
                                                      itemSpan: 1,
                                                      id: 'commonUpload',
                                                      name: 'supplier_quality_record_files',
                                                      fieldProps: {
                                                        action:
                                                          '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                                        mode: 'textButton',
                                                        listType: 'text',
                                                        // accept: ['pdf'],
                                                        hiddenControlerIcon:
                                                          false,
                                                        deleteByServer: false,
                                                        showUpload: true,
                                                        data: {
                                                          refType:
                                                            'SUPPLIER_AUDIT',
                                                          refId: 'sfsffefefe',
                                                        },
                                                      },
                                                    },
                                                    {
                                                      label: '生产厂商',
                                                      id: 'commonInput',
                                                      itemSpan: 6,
                                                      name: 'producer_supplier_name',
                                                      render: (formValues) => {
                                                        return formValues?.producer_supplier_name ? (
                                                          <div className="v-flex">
                                                            {
                                                              formValues?.producer_supplier_name
                                                            }
                                                            <XlbIcon
                                                              name="fuzhi"
                                                              className="custom-form-item-icon"
                                                              onClick={() => {
                                                                copy(
                                                                  formValues?.producer_supplier_name,
                                                                );
                                                                message.success(
                                                                  '复制成功',
                                                                );
                                                              }}
                                                            />
                                                          </div>
                                                        ) : (
                                                          <div>-</div>
                                                        );
                                                      },
                                                    },
                                                    {
                                                      label: '外检报告签发日期',
                                                      id: 'commonInput',
                                                      itemSpan: 5,
                                                      name: 'valid_date',
                                                    },
                                                    {
                                                      label: '外检报告截止时间',
                                                      id: 'commonInput',
                                                      name: 'end_date',
                                                      itemSpan: 5,
                                                      dependencies: [
                                                        'valid_date',
                                                      ],
                                                      render: (formValues) => {
                                                        return (
                                                          <div>
                                                            {
                                                              MONTHS_OPTION.find(
                                                                (v) =>
                                                                  v.value ==
                                                                  formValues.end_date,
                                                              )?.label
                                                            }
                                                          </div>
                                                        );
                                                      },
                                                    },
                                                    {
                                                      label: '产地',
                                                      id: 'commonInput',
                                                      itemSpan: 6,
                                                      name: 'origin_place',
                                                    },
                                                  ],
                                                },
                                              },
                                            ]}
                                          />
                                        </div>
                                      );
                                    })}
                                  </div>
                                );
                              },
                            },
                            {
                              id: ScmFieldKeyMap.disabledExternalReportList,
                              name: 'supplier_quality_record_data_list',
                              itemSpan: 24,
                              label: '型检外检报告',
                              dependencies: [
                                'supplier_id',
                                'state',
                                'food_safe_audit_state',
                                'imports',
                              ],
                              hidden: (formValues: any) => {
                                return (
                                  formValues?.food_safe_audit_state !==
                                    FoodSafeType.waitAudit ||
                                  isCertificated(formValues)
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                      CERTIFICATE_MAINTAINED.value &&
                                    !getFieldsValue(true)?.imports,
                                  message: '请上传型检外检报告',
                                }),
                              ],
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: '产品规格书',
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'product_spec_url',
                              itemSpan: 24,
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传产品规格书',
                                }),
                              ],
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                accept: ['xls', 'pdf'],
                                // accept: ['xlsx', 'XLSX'],
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: (
                        <div>
                          商品标签设计稿
                          <span
                            style={{
                              marginLeft: 10,
                              cursor: 'pointer',
                            }}
                            onClick={() => {
                              XlbImage?.openImageItem(itemPicture);
                            }}
                          >
                            示例图
                          </span>
                        </div>
                      ),
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'item_label_design_draft',
                              itemSpan: 24,
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传商品标签设计稿',
                                }),
                              ],
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: '营养成分外检报告',
                    },
                    dependencies: ['item_public_category_id'],
                    hidden: (formValues: any) =>
                      formValues.item_public_category_id ===
                      NO_FOOD_CATEGORY_ID,
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'nutrients_external_report',
                              itemSpan: 24,
                              dependencies: ['state', 'is_nutritional_label'],
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                      CERTIFICATE_MAINTAINED.value &&
                                    getFieldsValue(true)?.is_nutritional_label,
                                  message: '请上传营养成分外检报告',
                                }),
                              ],
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                accept: ['pdf'],
                                listType: 'text',
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span>商品合规证明资料</span>
                          <XlbTooltip title="按照附件中商品类别对应的资料要求提交，附件中无要求的商品类别无需提交">
                            <XlbIcon
                              color="#979faa"
                              hoverColor="#3D66FE"
                              name="bangzhu"
                              size={16}
                              style={{ margin: '0 8px' }}
                            />
                          </XlbTooltip>
                          <span
                            style={{ cursor: 'pointer' }}
                            onClick={() =>
                              XlbImage?.openImageItem(compliancePicture)
                            }
                          >
                            查看附件
                          </span>
                        </div>
                      ),
                    },
                    dependencies: ['item_public_category_id'],
                    hidden: (formValues: any) =>
                      formValues.item_public_category_id !==
                      NO_FOOD_CATEGORY_ID,
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'item_compliance_certificate',
                              itemSpan: 24,
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: '标签外检报告',
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'label_third_party_inspection',
                              itemSpan: 24,
                              dependencies: ['state'],
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传标签外检报告',
                                }),
                              ],
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                accept: ['pdf'],
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: '商品注册证证件',
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'trademark_register',
                              itemSpan: 24,
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: (
                        <div>
                          商品执行的经备案的企业标准文件
                          <span
                            style={{
                              marginLeft: 10,
                              cursor: 'pointer',
                            }}
                            onClick={() => {
                              XlbImage?.openImageItem(executivePicture);
                            }}
                          >
                            示例图
                          </span>
                        </div>
                      ),
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'executive_standard_url',
                              itemSpan: 24,
                              dependencies: ['executive_standard_type'],
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                      CERTIFICATE_MAINTAINED.value &&
                                    getFieldsValue(true)
                                      ?.executive_standard_type ===
                                      executiveStandardType.COMPANY_STANDARDS,
                                  message:
                                    '请上传商品执行的经备案的企业标准文件',
                                }),
                              ],
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                accept: ['pdf'],
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },

                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span>其他附件</span>
                          <XlbTooltip
                            placement="topLeft"
                            arrow={{ pointAtCenter: true }}
                            title="进口商品的进口货物报关单、入库货物检验检疫证明可在此处上传。"
                          >
                            <XlbIcon
                              color="#979faa"
                              hoverColor="#3D66FE"
                              name="bangzhu"
                              size={16}
                              style={{ margin: '0 8px' }}
                            />
                          </XlbTooltip>
                          <span
                            className="cursors"
                            onClick={() => window.open(TEMPLATE_URL, '_blank')}
                          >
                            下载模板
                          </span>
                        </div>
                      ),
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                formValues.state !== 'NEGOTIATE_AUDIT_ING' &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.state !==
                                    'COMPANY_FOOD_SAFE_AUDIT_OPPOSE')) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '上传',
                              id: 'commonUpload',
                              name: 'other_sccessories_url',
                              itemSpan: 24,
                              fieldProps: {
                                action:
                                  '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
                                mode: 'textButton',
                                listType: 'text',
                                hiddenControlerIcon: false,
                                deleteByServer: false,
                                showUpload: true,
                                data: {
                                  refType: 'SUPPLIER_AUDIT',
                                  refId: 'sfsffefefe',
                                },
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },

                  {
                    componentType: 'subTitle',
                    fieldProps: {
                      title: '上传图片',
                    },
                    children: [
                      {
                        componentType: 'form',
                        fieldProps: {
                          width: '100%',
                          dependencies: [
                            'fid',
                            'state',
                            'food_safe_audit_state',
                          ],
                          readOnly: (formValues: any) => {
                            return (
                              (formValues.fid &&
                                formValues.state !== 'INIT' &&
                                !isCertificated(formValues) &&
                                (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                  !hasAuth(['新品申请/采购初审', '编辑'])) &&
                                (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                                  formValues.state !== 'REVIEW_AUDIT_OPPOSE') &&
                                (!hasAuth([
                                  '新品申请/公司食安审核驳回',
                                  '编辑',
                                ]) ||
                                  formValues.food_safe_audit_state !==
                                    FoodSafeType.oppose)) ||
                              !hasAuth(['新品申请', '编辑'])
                            );
                          },
                          formList: [
                            {
                              label: '产品效果图',
                              id: ScmFieldKeyMap.scmUploadPicture,
                              name: 'images',
                              dependencies: [
                                'fid',
                                'state',
                                'food_safe_audit_state',
                              ],
                              disabled: (formValues: any) => {
                                return (
                                  (formValues.fid &&
                                    !isCertificated(formValues) &&
                                    formValues.state !== 'INIT' &&
                                    (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                      !hasAuth([
                                        '新品申请/采购初审',
                                        '编辑',
                                      ])) &&
                                    (!hasAuth([
                                      '新品申请/采购初审驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.state !==
                                        'REVIEW_AUDIT_OPPOSE') &&
                                    (!hasAuth([
                                      '新品申请/公司食安审核驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.food_safe_audit_state !==
                                        FoodSafeType.oppose)) ||
                                  !hasAuth(['新品申请', '编辑'])
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传产品效果图',
                                }),
                              ],
                            },
                            {
                              label: '商品背面照',
                              id: ScmFieldKeyMap.scmUploadPicture,
                              name: 'back_images',
                              dependencies: [
                                'fid',
                                'state',
                                'food_safe_audit_state',
                              ],
                              disabled: (formValues: any) => {
                                return (
                                  (formValues.fid &&
                                    !isCertificated(formValues) &&
                                    formValues.state !== 'INIT' &&
                                    (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                      !hasAuth([
                                        '新品申请/采购初审',
                                        '编辑',
                                      ])) &&
                                    (!hasAuth([
                                      '新品申请/采购初审驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.state !==
                                        'REVIEW_AUDIT_OPPOSE') &&
                                    (!hasAuth([
                                      '新品申请/公司食安审核驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.food_safe_audit_state !==
                                        FoodSafeType.oppose)) ||
                                  !hasAuth(['新品申请', '编辑'])
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传商品背面照',
                                }),
                              ],
                            },
                            {
                              label: '商品条码照',
                              id: ScmFieldKeyMap.scmUploadPicture,
                              name: 'bar_code_images',
                              dependencies: [
                                'fid',
                                'state',
                                'food_safe_audit_state',
                              ],
                              disabled: (formValues: any) => {
                                return (
                                  (formValues.fid &&
                                    !isCertificated(formValues) &&
                                    formValues.state !== 'INIT' &&
                                    (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                      !hasAuth([
                                        '新品申请/采购初审',
                                        '编辑',
                                      ])) &&
                                    (!hasAuth([
                                      '新品申请/采购初审驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.state !==
                                        'REVIEW_AUDIT_OPPOSE') &&
                                    (!hasAuth([
                                      '新品申请/公司食安审核驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.food_safe_audit_state !==
                                        FoodSafeType.oppose)) ||
                                  !hasAuth(['新品申请', '编辑'])
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传商品条码照',
                                }),
                              ],
                            },
                            {
                              label: '外箱正面照',
                              id: ScmFieldKeyMap.scmUploadPicture,
                              name: 'pack_age_face_images',
                              dependencies: [
                                'fid',
                                'state',
                                'food_safe_audit_state',
                              ],
                              disabled: (formValues: any) => {
                                return (
                                  (formValues.fid &&
                                    !isCertificated(formValues) &&
                                    formValues.state !== 'INIT' &&
                                    (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                      !hasAuth([
                                        '新品申请/采购初审',
                                        '编辑',
                                      ])) &&
                                    (!hasAuth([
                                      '新品申请/采购初审驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.state !==
                                        'REVIEW_AUDIT_OPPOSE') &&
                                    (!hasAuth([
                                      '新品申请/公司食安审核驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.food_safe_audit_state !==
                                        FoodSafeType.oppose)) ||
                                  !hasAuth(['新品申请', '编辑'])
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传外箱正面照',
                                }),
                              ],
                            },
                            {
                              label: '外箱条码照',
                              id: ScmFieldKeyMap.scmUploadPicture,
                              name: 'pack_age_bar_code_images',
                              dependencies: [
                                'fid',
                                'state',
                                'food_safe_audit_state',
                              ],
                              disabled: (formValues: any) => {
                                return (
                                  (formValues.fid &&
                                    !isCertificated(formValues) &&
                                    formValues.state !== 'INIT' &&
                                    (formValues.state !== 'REVIEW_AUDIT_ING' ||
                                      !hasAuth([
                                        '新品申请/采购初审',
                                        '编辑',
                                      ])) &&
                                    (!hasAuth([
                                      '新品申请/采购初审驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.state !==
                                        'REVIEW_AUDIT_OPPOSE') &&
                                    (!hasAuth([
                                      '新品申请/公司食安审核驳回',
                                      '编辑',
                                    ]) ||
                                      formValues.food_safe_audit_state !==
                                        FoodSafeType.oppose)) ||
                                  !hasAuth(['新品申请', '编辑'])
                                );
                              },
                              rules: [
                                ({ getFieldsValue }: any) => ({
                                  required:
                                    getFieldsValue(true)?.state ===
                                    CERTIFICATE_MAINTAINED.value,
                                  message: '请上传外箱条码照',
                                }),
                              ],
                            },
                          ],
                        },
                      },
                    ],
                  },
                ],
              },
            ];
          },
        },

        {
          componentType: 'blueBar',
          fieldProps: {
            title: '其他信息',
          },
          children: [
            {
              componentType: 'form',
              fieldProps: {
                itemSpan: 24,
                dependencies: ['fid', 'state'],
                readOnly: (formValues: any) => {
                  return (
                    (formValues.fid &&
                      formValues.state !== 'INIT' &&
                      (formValues.state !== 'REVIEW_AUDIT_ING' ||
                        !hasAuth(['新品申请/采购初审', '编辑'])) &&
                      (!hasAuth(['新品申请/采购初审驳回', '编辑']) ||
                        formValues.state !== 'REVIEW_AUDIT_OPPOSE')) ||
                    !hasAuth(['新品申请', '编辑']) ||
                    isCertificated(formValues)
                  );
                },
                formList: [
                  {
                    label: '商品特色',
                    id: 'commoneTextArea',
                    name: 'item_feature',
                    fieldProps: {
                      placeholder: '请输入',
                      autoSize: { minRows: 4, maxRows: 6 },
                    },
                  },
                  {
                    label: '优惠政策',
                    id: 'commoneTextArea',
                    name: 'preferential_policy',
                    fieldProps: {
                      placeholder: '若无优惠政策，填写"无"',
                      autoSize: { minRows: 4, maxRows: 6 },
                    },
                    rules: [{ required: true, message: '请输入优惠政策' }],
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ];
};

export default Item;
