import { ScmFieldKeyMap } from '@/constants/config/scm';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { XlbBaseUpload, XlbTableColumnProps } from '@xlb/components';
import dayjs from 'dayjs';
import styles from './index.less';
const userInfo = LStorage.get('userInfo');
export const UPLOAD_STATE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '已上传', value: 'UPLOADED' },
];
export const UPLOAD_TYPE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '已上传', value: 'UPLOADED' },
  { label: '临期', value: 'TEMPORARY' },
  { label: '过期', value: 'EXPIRE' },
];
export const supplierOptions = [
  { value: 'TRADER', label: '贸易商' },
  { value: 'PRODUCER', label: '生产商' },
];
export const MISS_ITEM_STATE = [
  { label: '未上传', value: 'UNUPLOAD' },
  { label: '部分上传', value: 'PARTUPLOAD' },
  { label: '全部上传', value: 'ALLUPLOAD' },
];
export const FEE_LICENSE_TYPE = [
  { label: '审核中', value: 'AUDIT' },
  { label: '企业', value: 'COMPANY' },
];

export const ocrType = {
  license: 'BUSINESS_LICENSE',
  food_production: 'FOOD_PRODUCTION_LICENSE',
  food_business: 'FOOD_BUSINESS_LICENSE',
};
export const ocrname = {
  license: 'business_license',
  food_production: 'food_production_license',
  food_business: 'food_business_license',
};

export const baseList = [
  {
    label: '证件附件',
    name: 'files',
    id: 'commonUpload',
    rules: [{ required: true, message: '请上传附件' }],
    fieldProps: {
      action: '/scm/hxl.scm.storefilemanage.file.upload',
      buttonModal: true,
      accept: ['image'],
      mode: 'textButton',
      maxCount: 1,
      listType: 'picture',
      data: { id: '1' },
    },
  },
  {
    label: '名称',
    name: 'name',
    id: 'commonInput',
    rules: [{ required: true, message: '请输入名称' }],
  },
  {
    label: '社会信用代码',
    name: 'credit_code',
    id: 'commonInput',
    rules: [{ required: true, message: '请输入社会信用代码' }],
  },
  {
    label: '法人',
    name: 'legal_person',
    id: 'commonInput',
    rules: [{ required: true, message: '请输入法人' }],
  },
  {
    label: '有效期始',
    name: 'start_date',
    id: ScmFieldKeyMap.scmDate,
    rules: [{ required: true, message: '请输入有效期始' }],
  },
  {
    label: '有效期止',
    name: 'end_date',
    id: ScmFieldKeyMap.scmDate,
    dependencies: ['start_date'],
    rules: [
      ({ getFieldValue }: any) => ({
        validator: (_: any, value: any) => {
          const start = getFieldValue('start_date');
          if (!!value && start && dayjs(start).isAfter(value)) {
            return Promise.reject('有效期始不得大于有效期止');
          }
          return Promise.resolve();
        },
      }),
    ],
  },
];

export const itemEndDate = {
  label: '有效期止',
  name: 'end_date',
  id: ScmFieldKeyMap.scmDate,
  dependencies: ['start_date'],
  rules: [
    {
      required: true,
      message: '请输入有效期止',
    },
    ({ getFieldValue }: any) => ({
      validator: (_: any, value: any) => {
        const start = getFieldValue('start_date');
        if (!!value && start && dayjs(start).isAfter(value)) {
          return Promise.reject('有效期始不得大于有效期止');
        }
        return Promise.resolve();
      },
    }),
  ],
};

export const itemfiles = [
  {
    label: '品种明细',
    name: 'category_files',
    id: 'commonUpload',
    rules: [{ required: true, message: '请上传品种附件' }],
    fieldProps: {
      action: '/scm/hxl.scm.storefilemanage.file.upload',
      buttonModal: true,
      accept: ['image'],
      mode: 'textButton',
      data: { id: 1 },
      // maxCount: 1,
    },
  },
  {
    label: '食品生产许可证编号',
    name: 'licence_number',
    id: 'commonInput',
    rules: [{ required: true, message: '请填写食品生产许可证编号' }],
  },
  {
    label: '生产地址',
    name: 'origin_place',
    id: 'commonInput',
    rules: [{ required: true, message: '请填写生产地址' }],
  },
  {
    label: '食品类别',
    name: 'food_category',
    id: 'commonInput',
    rules: [{ required: true, message: '请填写食品类别' }],
  },
];
export const itemlook = {
  name: '品种明细',
  code: 'category_files',
  render: (text: any) => {
    return (
      <div
        onClick={(e) => {
          e?.stopPropagation();
        }}
      >
        <XlbBaseUpload
          className="danger"
          mode="look"
          hiddenControlerIcon={true}
          showUpload={false}
          listType={'text'}
          fileList={text}
        />
      </div>
    );
  },
};

export const filesList = [
  {
    label: '证件附件',
    name: 'files',
    id: 'commonUpload',
    rules: [{ required: true, message: '请上传附件' }],
    fieldProps: {
      action: '/scm/hxl.scm.storefilemanage.file.upload',
      buttonModal: true,
      accept: ['image'],
      mode: 'textButton',
      maxCount: 5,
      listType: 'picture',
      data: { id: '1' },
    },
  },
];
// 查看归档
export const tableBaseList = [
  { name: '序号', code: '_index' },
  { name: '名称', code: 'name', width: 100 },
  { name: '社会信用代码', code: 'credit_code', width: 120 },
  { name: '法人', code: 'legal_person' },
  { name: '有效期始', code: 'start_date', width: 150 },
  { name: '有效期止', code: 'end_date', width: 150 },
  {
    name: '证件',
    code: 'files',
    render: (text: any) => {
      return (
        <div
          onClick={(e) => {
            e?.stopPropagation();
          }}
        >
          <XlbBaseUpload
            className="danger"
            mode="look"
            hiddenControlerIcon={true}
            showUpload={false}
            listType={'text'}
            fileList={text}
          />
        </div>
      );
    },
  },
  { name: '归档时间', code: 'archive_date', width: 160 },
];
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    align: 'center',
    lock: true,
  },
  {
    name: '供应商',
    // code: 'supplier_name',
    code: 'supplier_name',
    width: 160,
    features: { sortable: true, details: true },
    lock: true,
    render(text, record, index) {
      return (
        <div
          onClick={() => {
            record.data_state = 'PASS';
            record.look_state = 'PASS';
          }}
        >
          {text}
        </div>
      );
    },
  },
  {
    name: '供应商类型',
    code: 'supplier_type',
    width: 190,
    features: { sortable: true },
    lock: true,
    render(text, record, index) {
      return supplierOptions?.find((item) => item.value === text)?.label;
    },
  },
  {
    name: '营业执照',
    code: 'license_state',
    width: 130,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '食品经营许可证',
    code: 'food_business_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '食品生产许可证',
    code: 'food_production_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '生产品种明细',
    code: 'category_detail_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  // {
  //   name: '经营生产环境',
  //   code: 'business_production_state',
  //   width: 144,
  //   features: { sortable: true },
  //   render: (text) => {
  //     return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
  //   },
  // },
  {
    name: '质量体系认证证书',
    code: 'quality_certificate_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '工厂门头照片',
    code: 'factory_gate_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '加工场所照片',
    code: 'factory_place_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '实验室照片',
    code: 'laboratory_state',
    width: 144,
    features: { sortable: true },
    render: (text) => {
      return UPLOAD_TYPE.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '变更申请',
    code: '123',
    width: 100,
    features: { details: true },
    render(text, record, index) {
      return (
        <>
          {(userInfo.user_type === 'SUPPLIER' ||
            (userInfo.user_type === 'USER' && record.state !== 'AUDIT')) &&
          hasAuth(['供应商证件', '编辑']) ? (
            <div
              onClick={() => {
                record.data_state = 'TEMP';
                record.look_state = 'TEMP';
              }}
            >
              更新
            </div>
          ) : (
            <div className={styles.flex}>
              <div
                onClick={() => {
                  record.data_state = 'PASS';
                  record.look_state = 'PASS';
                }}
                style={{ flex: '1' }}
              >
                查看
              </div>
              {record?.state === 'AUDIT' && hasAuth(['供应商证件', '审核']) && (
                <div
                  style={{ flex: '1' }}
                  onClick={() => {
                    record.data_state = 'TEMP';
                    record.look_state = 'PASS';
                  }}
                >
                  审核
                </div>
              )}
            </div>
          )}
        </>
      );
    },
  },
  {
    name: '审核状态',
    code: 'state',
    width: 100,
    features: {},
    render: (text) => {
      return states.filter((item) => item.value === text)[0]?.label;
    },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核批注',
    code: 'memo',
    width: 100,
  },
  {
    name: '最近更新人',
    code: 'update_by',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '最近更新时间',
    code: 'update_time',
    width: 120,
    features: { sortable: true },
  },
];
export const aduitlist: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    align: 'center',
    lock: true,
  },
  {
    name: '申请人',
    code: 'apply_by',
    width: 100,
    features: {},
  },
  {
    name: '申请时间',
    code: 'apply_time',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
    features: {},
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核状态',
    code: 'state',
    width: 120,
    features: {},
    render(text, record, index) {
      return states?.find((item) => item.value === text)?.label;
    },
  },
  {
    name: '批注',
    code: 'memo',
    width: 120,
    features: {},
  },
];
export let states = [
  { label: '待审核', value: 'AUDIT' },
  { label: '审核通过', value: 'PASS' },
  { label: '审核拒绝', value: 'DENY' },
];

export const OPERATION_TYPE = {
  license: 'LICENSE',
  food_production: 'FOODPRODUCTION',
  food_business: 'FOODBUSINESS',
  business_production: 'BUSINESSPRODUCTION',
  quality_certificate: 'QUALITYCERTIFICATE',
  factory_gate: 'FACTORYGATE',
  factory_place: 'FACTORYPLACE',
  laboratory: 'LABORATORY',
};

export const formList = [
  {
    id: 'dateCommon',
    format: 'YYYY-MM-DD',
    name: 'check_date',
    label: '日期选择',
  },
  {
    id: 'commonSelect',
    label: '时间类型',
    name: 'query_date_type',
    fieldProps: {
      allowClear: false,
      options: [
        {
          label: '审核时间',
          value: 'AUDIT',
        },
        {
          label: '最近更新时间',
          value: 'UPDATE',
        },
      ],
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    id: ScmFieldKeyMap?.supplierIds,
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
          query_producer: userInfo.user_type === 'SUPPLIER' ? true : undefined,
        },
      },
    },
  },
  {
    label: '供应商类型',
    id: 'commonSelect',
    name: 'supplier_types',
    fieldProps: {
      options: supplierOptions,
      mode: 'multiple',
    },
  },
  {
    label: '营业执照',
    id: 'commonSelect',
    name: 'license_states',
    fieldProps: {
      options: UPLOAD_TYPE,
      mode: 'multiple',
    },
  },
  {
    label: '经营许可证',
    id: 'commonSelect',
    name: 'food_business_states',
    fieldProps: {
      options: UPLOAD_TYPE,
      mode: 'multiple',
    },
  },
  {
    label: '生产许可证',
    id: 'commonSelect',
    name: 'food_production_states',
    fieldProps: {
      options: UPLOAD_TYPE,
      mode: 'multiple',
    },
  },
  {
    label: '生产品种明细',
    id: 'commonSelect',
    name: 'category_detail_states',
    fieldProps: {
      options: UPLOAD_STATE,
      mode: 'multiple',
    },
  },
  // {
  //   label: '经营生产环境',
  //   id: 'commonSelect',
  //   name: 'business_production_states',
  //   fieldProps: {
  //     multiple: true,
  //     options: UPLOAD_STATE,
  //     mode: 'multiple',
  //   },
  // },
  {
    label: '审核状态',
    id: 'commonSelect',
    name: 'states',
    fieldProps: {
      multiple: true,
      options: states,
      mode: 'multiple',
    },
  },
  {
    label: '审核人',
    id: 'commonInput',
    name: 'audit_by',
  },
];

export const QUALITY_TYPE = [
  {
    label: 'HACCP',
    value: 'HACCP',
  },
  {
    label: 'ISO22000',
    value: 'ISO22000',
  },
  {
    label: 'ISO9001',
    value: 'ISO9001',
  },
  {
    label: 'FSSC22000',
    value: 'FSSC22000',
  },
  {
    label: 'BRC',
    value: 'BRC',
  },
  {
    label: 'AIB',
    value: 'AIB',
  },
  {
    label: '其他体系',
    value: '其他体系',
  },
];

export const qualityList = [
  {
    label: '证件附件',
    name: 'files',
    id: 'commonUpload',
    rules: [{ required: true, message: '请上传附件' }],
    fieldProps: {
      action: '/scm/hxl.scm.storefilemanage.file.upload',
      buttonModal: true,
      accept: ['image'],
      mode: 'textButton',
      maxCount: 1,
      listType: 'picture',
      data: { id: '1' },
    },
  },
  {
    label: '体系证书类型',
    name: 'type1',
    id: 'commonSelect',
    rules: [{ required: true, message: '请选择体系证书类型' }],
    fieldProps: {
      options: QUALITY_TYPE,
    },
  },
  {
    label: '名称',
    name: 'name',
    id: 'commonInput',
    rules: [{ required: true, message: '请输入名称' }],
  },
  {
    label: '有效期始',
    name: 'start_date',
    id: ScmFieldKeyMap.scmDate,
    rules: [{ required: true, message: '请输入有效期始' }],
  },
  {
    label: '有效期止',
    name: 'end_date',
    id: ScmFieldKeyMap.scmDate,
    dependencies: ['start_date'],
    rules: [
      { required: true, message: '请输入有效期止' },
      ({ getFieldValue }: any) => ({
        validator: (_: any, value: any) => {
          const start = getFieldValue('start_date');
          if (!!value && start && dayjs(start).isAfter(value)) {
            return Promise.reject('有效期始不得大于有效期止');
          }
          return Promise.resolve();
        },
      }),
    ],
  },
];
