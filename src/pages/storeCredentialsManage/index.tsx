import { dateStrSlice } from '@/utils/format';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { history } from '@@/core/history';
import { ProFormDependency } from '@ant-design/pro-components';
import {
  XlbBaseUpload,
  XlbBasicForm,
  XlbButton,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbProForm,
  XlbProPageContainer,
  XlbSelect,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { DetailFormList } from '@xlb/components/dist/lowcodes/XlbProDetail/type';
import { Col, message, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import {
  aduitlist,
  baseList,
  filesList,
  formList,
  itemEndDate,
  itemfiles,
  itemlook,
  ocrname,
  ocrType,
  OPERATION_TYPE,
  QUALITY_TYPE,
  tableBaseList,
  tableList,
} from './data';
import styles from './index.less';
import {
  ocr,
  qualitycertificateSave,
  save,
  savebusinessproduction,
  submitItem,
  supplierfilemanage,
} from './server';

const Index: React.FC = () => {
  const formRef = useRef<any>(null);
  const record = history.location.state as any;
  const ref = useRef<any>();
  const userInfo = LStorage.get('userInfo');
  // const [form] = XlbBasicForm.useForm();
  let onClose = () => {};
  const transformData = (key: string, formValues?: any) => {
    const list: any = [...baseList];
    if (key === 'food_production') {
      list.splice(1, 0, ...itemfiles);
      // 生产商的生产许可证截至日期必填
      if (formValues?.supplier_type === 'PRODUCER') {
        list.splice(list.length - 1, 1, itemEndDate);
      }
    }
    const transformList = list.map((item: any) => {
      return {
        ...item,
        onChange:
          item?.name === 'files'
            ? async (e: any, form: any) => {
                if (e[0]?.url) {
                  const res = await ocr({
                    type: ocrType[key as keyof typeof ocrType],
                    url: e[0]?.url,
                  });
                  if (res?.code === 0) {
                    const datarang = dateStrSlice(
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.valid_period,
                    );
                    // 食品经营许可法人legal_representative
                    // 食品经营许可名字operator_name
                    // 食品生产许可法人legal_representative
                    // 食品生产许可名字producer_name
                    const producer_name =
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.company_name ||
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.operator_name;
                    res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                      ?.producer_name;
                    const legal =
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.legal_representative ||
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.legal_person;
                    console.log(
                      '88888888',
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.production_address,
                    );
                    const production_address =
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.production_address;
                    const food_type =
                      res?.data?.[ocrname?.[key as keyof typeof ocrname]]
                        ?.food_type;
                    form?.setFieldsValue({
                      ...res?.data?.[ocrname?.[key as keyof typeof ocrname]],
                      name: producer_name,
                      legal_person: legal,
                      start_date: datarang?.start_date,
                      end_date: datarang?.end_date,
                      origin_place: production_address,
                      food_category: food_type,
                    });
                  }
                }
              }
            : undefined,
      };
    });
    return transformList;
  };

  const handleCheck = (value: any, key: string) => {
    let columns = [...tableBaseList];
    if (key == 'food_production') {
      columns.splice(7, 0, itemlook);
    }
    if (key == 'aduit') {
      columns = [...aduitlist];
    }
    if (key == 'business_production') {
      columns = tableBaseList.filter((item) => item.code === 'files');
    }
    XlbTipsModal({
      width: key == 'weighing_licence' ? 1200 : 800,
      bordered: true,
      isConfirm: false,
      title: key === 'aduit' ? '审核记录' : '查看归档',
      tips: (
        <XlbTable
          columns={columns || []}
          dataSource={
            (key !== 'aduit'
              ? value?.[key]?.archive_report
              : value?.record_report) || []
          }
          hideOnSinglePage={true}
        />
      ),
      isCancel: false,
    });
  };

  const handleSubmit = (form: any, type: string) => {
    form.submit();
    ref.current = type;
  };
  // 查看归档
  const readcheckout = (type: string, formValues: any) => {
    return {
      componentType: 'customer',
      render: (...args) => {
        const validateArry = transformData(type).map((ele) => ele?.name);
        return (
          <ProFormDependency name={[type]}>
            {(obj, form) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    gap: 20,
                  }}
                >
                  {
                    <XlbButton
                      type="primary"
                      onClick={() => handleCheck(formValues, type)}
                      icon={
                        <XlbIcon
                          name="xianshimima"
                          color="currentColor"
                          size={16}
                        />
                      }
                    >
                      查看归档
                    </XlbButton>
                  }
                  {formValues?.look_state === 'TEMP' &&
                    formValues?.state !== 'AUDIT' &&
                    userInfo?.user_type !== 'USER' && (
                      <XlbButton
                        type="primary"
                        onClick={() => handleSubmit(form, type)}
                      >
                        保存
                      </XlbButton>
                    )}
                </div>
              );
            }}
          </ProFormDependency>
        );
      },
      // hidden:
      //   formValues?.look_state === 'PASS' || formValues?.state === 'AUDIT',
    };
  };
  // 审核
  const processingResults = async (
    data: any,
    index: any,
    onClose: any,
    fetchData: any,
  ) => {
    if (index === 0) {
      const res = await supplierfilemanage({ id: data?.id, state: 'PASS' });
      res?.code === 0 && message.success('操作成功');
      onClose();
      fetchData();
      return;
    }
    XlbTipsModal({
      tips: (
        <XlbProForm
          formRef={formRef}
          formList={[
            {
              label: '审核拒绝说明',
              id: 'commoneTextArea',
              name: 'memo',
              fieldProps: { autoSize: { minRows: 2, maxRows: 4 }, width: 126 },
              rules: [{ required: true, message: '请填写拒绝原因' }],
              render(formValues: any) {
                return formValues?.memo || '';
              },
            },
          ]}
        />
      ),
      title: '审核',
      width: 350,
      isFullScreen: true,
      isCancel: true,
      onOkBeforeFunction: async () => {
        try {
          await formRef.current.validateFields();
        } catch (error) {
          return false;
        }
        const res = await supplierfilemanage({
          ...formRef.current.getFieldsValue(true),
          id: data?.id,
          state: 'DENY',
        });
        if (res?.code == 0) {
          message.success('操作成功');
          onClose();
          return true;
        }
      },
    });
  };

  async function handleSave(
    formName: string,
    info: any,
    oldFormValues: any,
    saveFunction: (params: any) => Promise<any>,
  ) {
    const formIsValid = await info?.forms?.[formName]?.validateFields();
    if (!formIsValid) return;

    const formData = info?.forms?.[formName]?.getFieldsValue(true);
    const params = {
      ...formData,
      operation_type: OPERATION_TYPE[formName as keyof typeof OPERATION_TYPE],
      id: oldFormValues?.id,
    };
    const res = await saveFunction(params);
    if (res?.code == 0) {
      message.success('保存成功');
    }
  }
  return (
    <div className={styles.cardContent}>
      <XlbProPageContainer
        details={{
          itemSpan: 12,
          primaryKey: 'id',
          readOnly: (formValues) => {
            return (
              formValues?.look_state === 'PASS' || formValues?.state === 'AUDIT'
            );
          },
          onFinish: async (
            name: any,
            info: any,
            actions: any,
            oldFormValues: any,
          ) => {
            const { onClose, fetchData } = actions;
            if (ref.current === 'license') {
              await handleSave('license', info, oldFormValues, save);
            } else if (ref.current === 'food_business') {
              await handleSave('food_business', info, oldFormValues, save);
            } else if (ref.current === 'food_production') {
              await handleSave('food_production', info, oldFormValues, save);
            } else if (ref.current === 'business_production') {
              await handleSave(
                'business_production',
                info,
                oldFormValues,
                savebusinessproduction,
              );
            } else if (ref.current === 'quality_certificate_new') {
              await handleSave(
                'quality_certificate_new',
                info,
                oldFormValues,
                qualitycertificateSave,
              );
            } else if (ref.current === 'factory_place') {
              await handleSave(
                'factory_place',
                info,
                oldFormValues,
                savebusinessproduction,
              );
            } else if (ref.current === 'factory_gate') {
              await handleSave(
                'factory_gate',
                info,
                oldFormValues,
                savebusinessproduction,
              );
            } else if (ref.current === 'laboratory') {
              await handleSave(
                'laboratory',
                info,
                oldFormValues,
                savebusinessproduction,
              );
            } else if (ref.current == 'submit') {
              //贸易商，营业执照，食品经营许可证必填
              if (oldFormValues.supplier_type == 'TRADER') {
                let licenseV = true;
                let food_businessV = true;
                licenseV = await info?.forms?.license?.validateFields();
                if (!licenseV) return;
                food_businessV =
                  await info?.forms?.food_business?.validateFields();
                if (!food_businessV) return;
              } else {
                //生产商，营业执照，生产许可证必填
                let licenseV = true;
                let food_productionV = true;
                licenseV = await info?.forms?.license?.validateFields();
                if (!licenseV) return;
                food_productionV =
                  await info?.forms?.food_production?.validateFields();
                if (!food_productionV) return;
              }
              const license = info?.forms?.license?.getFieldsValue(true);
              const food_business =
                info?.forms?.food_business?.getFieldsValue(true);
              const food_production =
                info?.forms?.food_production?.getFieldsValue(true);
              const business_production =
                info?.forms?.business_production?.getFieldsValue(true);
              const quality_certificate_new =
                info?.forms?.quality_certificate_new?.getFieldsValue(true);
              console.log('=-=-=-=', info?.forms?.quality_certificate_new);
              const factory_gate =
                info?.forms?.factory_gate?.getFieldsValue(true);
              const factory_place =
                info?.forms?.factory_place?.getFieldsValue(true);
              const laboratory = info?.forms?.laboratory?.getFieldsValue(true);
              const params = {
                ...oldFormValues,
                license,
                food_business,
                food_production,
                business_production,
                quality_certificate_new,
                factory_gate,
                factory_place,
                laboratory,
                refresh_page: 1,
              };
              const res = await submitItem(params);
              if (res?.code == 0) {
                message.success('提交成功');
                onClose();
                fetchData();
              }
            }
          },
          queryFieldProps: {
            url: '/scm/hxl.scm.supplierfilemanage.read',
          },

          formList: [
            {
              componentType: 'tabs',
              fieldProps: {
                items: (formValues: any) => {
                  return [
                    {
                      label: '营业执照',
                      key: 'license',
                      forceRender: true,
                      children: [
                        readcheckout('license', formValues) as DetailFormList,
                        {
                          componentType: 'form',
                          name: 'license',
                          fieldProps: {
                            formList: transformData('license'),
                          },
                        },
                      ],
                    },
                    {
                      label: '食品经营许可证',
                      key: 'food_business',
                      forceRender: true,
                      children: [
                        readcheckout(
                          'food_business',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          name: 'food_business',
                          fieldProps: {
                            formList: transformData('food_business'),
                          },
                        },
                      ],
                    },
                    {
                      label: '食品生产许可证',
                      key: 'food_production',
                      children: [
                        readcheckout(
                          'food_production',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          name: 'food_production',
                          fieldProps: {
                            formList: transformData(
                              'food_production',
                              formValues,
                            ),
                          },
                        },
                      ],
                      forceRender: true,
                    },
                    // {
                    //   label: '经营生产环境',
                    //   key: 'business_production',
                    //   children: [
                    //     readcheckout(
                    //       'business_production',
                    //       formValues,
                    //     ) as DetailFormList,

                    //     {
                    //       componentType: 'form',
                    //       name: 'business_production',
                    //       fieldProps: {
                    //         formList: [
                    //           {
                    //             label: '证件附件',
                    //             name: 'files',
                    //             id: 'commonUpload',
                    //             fieldProps: {
                    //               action:
                    //                 '/scm/hxl.scm.storefilemanage.file.upload',
                    //               buttonModal: true,
                    //               accept: ['image'],
                    //               mode: 'textButton',
                    //               listType: 'picture',
                    //               data: {
                    //                 id: 123,
                    //               },
                    //             },
                    //             rules: [
                    //               {
                    //                 required: true,
                    //                 message: '请上传经营生产环境',
                    //               },
                    //             ],
                    //           },
                    //         ],
                    //       },
                    //     },
                    //   ],
                    //   forceRender: true,
                    // },
                    {
                      label: '质量体系认证证书',
                      key: 'quality_certificate_new',
                      forceRender: true,
                      children: [
                        readcheckout(
                          'quality_certificate_new',
                          formValues,
                        ) as DetailFormList,
                        // {
                        //   componentType: 'form',
                        //   name: 'quality_certificate',
                        //   fieldProps: {
                        //     formList: qualityList,
                        //   },
                        // },
                        {
                          componentType: 'customer',
                          name: 'quality_certificate_new',
                          render: () => {
                            return (
                              <ProFormDependency
                                name={['quality_certificate_list']}
                              >
                                {(obj, form) => {
                                  console.log('_+_obj', obj, formValues);
                                  return (
                                    <div>
                                      {formValues?.look_state === 'PASS' ||
                                      formValues?.state === 'AUDIT' ? (
                                        <div>
                                          {obj?.quality_certificate_list?.map(
                                            (item: any, index: any) => {
                                              return (
                                                <div
                                                  className={
                                                    styles.cardContainer
                                                  }
                                                >
                                                  <div
                                                    className={styles.cardTitle}
                                                  >
                                                    <div>
                                                      质量体系认证证书
                                                      {index + 1}
                                                    </div>
                                                  </div>
                                                  <div
                                                    className={styles.cardBody}
                                                  >
                                                    <Row>
                                                      <Col
                                                        span={12}
                                                        style={{
                                                          marginBottom: 16,
                                                        }}
                                                      >
                                                        <div
                                                          style={{
                                                            display: 'flex',
                                                          }}
                                                        >
                                                          <span
                                                            className={
                                                              styles.label
                                                            }
                                                          >
                                                            证件附件:
                                                          </span>
                                                          <span>
                                                            <XlbBaseUpload
                                                              fileList={
                                                                item?.files ||
                                                                []
                                                              }
                                                              mode="look"
                                                              listType={
                                                                'picture'
                                                              }
                                                              maxCount={1}
                                                              accept={['image']}
                                                              data={{
                                                                id: '1',
                                                              }}
                                                              action="/scm/hxl.scm.storefilemanage.file.upload"
                                                            />
                                                          </span>
                                                        </div>
                                                      </Col>
                                                      <Col
                                                        span={12}
                                                        style={{
                                                          marginBottom: 16,
                                                        }}
                                                      >
                                                        <span
                                                          className={
                                                            styles.label
                                                          }
                                                        >
                                                          体系证书类型:
                                                        </span>
                                                        <span>
                                                          {item?.licence_type}
                                                        </span>
                                                      </Col>
                                                      <Col
                                                        span={12}
                                                        style={{
                                                          marginBottom: 16,
                                                        }}
                                                      >
                                                        <span
                                                          className={
                                                            styles.label
                                                          }
                                                        >
                                                          名称:
                                                        </span>
                                                        <span>
                                                          {item?.name}
                                                        </span>
                                                      </Col>
                                                      <Col
                                                        span={12}
                                                        style={{
                                                          marginBottom: 16,
                                                        }}
                                                      >
                                                        <span
                                                          className={
                                                            styles.label
                                                          }
                                                        >
                                                          有效期始:
                                                        </span>
                                                        <span>
                                                          {item?.start_date}
                                                        </span>
                                                      </Col>
                                                      <Col
                                                        span={12}
                                                        style={{
                                                          marginBottom: 16,
                                                        }}
                                                      >
                                                        <span
                                                          className={
                                                            styles.label
                                                          }
                                                        >
                                                          有效期止:
                                                        </span>
                                                        <span>
                                                          {item?.end_date}
                                                        </span>
                                                      </Col>
                                                    </Row>
                                                  </div>
                                                </div>
                                              );
                                            },
                                          )}
                                        </div>
                                      ) : (
                                        <XlbBasicForm
                                          form={form}
                                          autoComplete="off"
                                          //layout="vertical"
                                        >
                                          <XlbBasicForm.List name="quality_certificate_list">
                                            {(fields, { add, remove }) => (
                                              <div>
                                                {fields.map(
                                                  ({
                                                    key,
                                                    name: fieldIndex,
                                                    ...restField
                                                  }) => (
                                                    <div
                                                      className={
                                                        styles.cardContainer
                                                      }
                                                    >
                                                      <div
                                                        className={
                                                          styles.cardTitle
                                                        }
                                                      >
                                                        <div>
                                                          质量体系认证证书
                                                          {fieldIndex + 1}
                                                        </div>
                                                        <div>
                                                          <XlbIcon
                                                            name="shanchu"
                                                            onClick={() =>
                                                              remove(fieldIndex)
                                                            }
                                                          />
                                                        </div>
                                                      </div>
                                                      <div
                                                        className={
                                                          styles.cardBody
                                                        }
                                                      >
                                                        <XlbBasicForm.Item
                                                          {...restField}
                                                          label="证件附件："
                                                          name={[
                                                            fieldIndex,
                                                            'files',
                                                          ]}
                                                          rules={[
                                                            {
                                                              required: true,
                                                              message:
                                                                '请上传证件附件',
                                                            },
                                                          ]}
                                                        >
                                                          <div
                                                            style={{
                                                              width: 200,
                                                            }}
                                                          >
                                                            <XlbBaseUpload
                                                              onChange={(
                                                                value: any,
                                                              ) => {
                                                                console.log(
                                                                  'value',
                                                                  value,
                                                                );

                                                                form.setFieldsValue(
                                                                  {
                                                                    quality_certificate_list:
                                                                      {
                                                                        [fieldIndex]:
                                                                          {
                                                                            files:
                                                                              value,
                                                                          },
                                                                      },
                                                                  },
                                                                );
                                                              }}
                                                              fileList={
                                                                obj
                                                                  .quality_certificate_list?.[
                                                                  fieldIndex
                                                                ]?.files || []
                                                              }
                                                              mode="textButton"
                                                              listType={
                                                                'picture'
                                                              }
                                                              maxCount={1}
                                                              accept={['image']}
                                                              data={{
                                                                id: '1',
                                                              }}
                                                              action="/scm/hxl.scm.storefilemanage.file.upload"
                                                            />
                                                          </div>
                                                        </XlbBasicForm.Item>

                                                        <XlbBasicForm.Item
                                                          {...restField}
                                                          label="体系证书类型："
                                                          name={[
                                                            fieldIndex,
                                                            'licence_type',
                                                          ]}
                                                          rules={[
                                                            {
                                                              required: true,
                                                              message:
                                                                '请选择体系证书类型',
                                                            },
                                                          ]}
                                                        >
                                                          <XlbSelect
                                                            width={200}
                                                            options={
                                                              QUALITY_TYPE
                                                            }
                                                          />
                                                        </XlbBasicForm.Item>

                                                        <XlbBasicForm.Item
                                                          {...restField}
                                                          label="名称："
                                                          name={[
                                                            fieldIndex,
                                                            'name',
                                                          ]}
                                                          rules={[
                                                            {
                                                              required: true,
                                                              message:
                                                                '请输入名称',
                                                            },
                                                          ]}
                                                        >
                                                          <XlbInput
                                                            width={200}
                                                            placeholder="请输入名称"
                                                          />
                                                        </XlbBasicForm.Item>

                                                        <XlbBasicForm.Item
                                                          {...restField}
                                                          label="有效期始："
                                                          rules={[
                                                            {
                                                              required: true,
                                                              message:
                                                                '请有效期始',
                                                            },
                                                          ]}
                                                          name={[
                                                            fieldIndex,
                                                            'start_date',
                                                          ]}
                                                        >
                                                          <XlbDatePicker
                                                            placeholder="请选择"
                                                            style={{
                                                              width: 200,
                                                            }}
                                                          />
                                                        </XlbBasicForm.Item>

                                                        <XlbBasicForm.Item
                                                          {...restField}
                                                          label="有效期止："
                                                          name={[
                                                            fieldIndex,
                                                            'end_date',
                                                          ]}
                                                          rules={[
                                                            {
                                                              required: true,
                                                              message:
                                                                '请输入有效期止',
                                                            },
                                                            ({
                                                              getFieldValue,
                                                            }: any) => ({
                                                              validator: (
                                                                _: any,
                                                                value: any,
                                                              ) => {
                                                                const start =
                                                                  getFieldValue(
                                                                    [
                                                                      'quality_certificate_list',
                                                                      fieldIndex,
                                                                      'start_date',
                                                                    ],
                                                                  );
                                                                if (
                                                                  !!value &&
                                                                  start &&
                                                                  dayjs(
                                                                    start,
                                                                  ).isAfter(
                                                                    value,
                                                                  )
                                                                ) {
                                                                  return Promise.reject(
                                                                    '有效期始不得大于有效期止',
                                                                  );
                                                                }
                                                                return Promise.resolve();
                                                              },
                                                            }),
                                                          ]}
                                                        >
                                                          <XlbDatePicker
                                                            style={{
                                                              width: 200,
                                                            }}
                                                            placeholder="请选择"
                                                          />
                                                        </XlbBasicForm.Item>
                                                      </div>
                                                    </div>
                                                  ),
                                                )}
                                                <XlbButton
                                                  type="primary"
                                                  style={{
                                                    width: 110,
                                                    borderRadius: 4,
                                                    backgroundColor: '#3D66FE',
                                                    color: '#fff',
                                                    height: 36,
                                                    borderColor: 'unset',
                                                    marginBottom: 16,
                                                  }}
                                                  onClick={() => add()}
                                                >
                                                  新增
                                                </XlbButton>
                                              </div>
                                            )}
                                          </XlbBasicForm.List>
                                        </XlbBasicForm>
                                      )}
                                    </div>
                                  );
                                }}
                              </ProFormDependency>
                            );
                          },
                        },
                      ],
                    },
                    {
                      label: '工厂门头照片',
                      key: 'factory_gate',
                      forceRender: true,
                      children: [
                        readcheckout(
                          'factory_gate',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          name: 'factory_gate',
                          fieldProps: {
                            formList: filesList,
                          },
                        },
                      ],
                    },
                    {
                      label: '加工场所照片',
                      key: 'factory_place',
                      forceRender: true,
                      children: [
                        readcheckout(
                          'factory_place',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          name: 'factory_place',
                          fieldProps: {
                            formList: filesList,
                          },
                        },
                      ],
                    },
                    {
                      label: '实验室照片',
                      key: 'laboratory',
                      forceRender: true,
                      children: [
                        readcheckout(
                          'laboratory',
                          formValues,
                        ) as DetailFormList,
                        {
                          componentType: 'form',
                          name: 'laboratory',
                          fieldProps: {
                            formList: filesList,
                          },
                        },
                      ],
                    },
                  ];
                },
              },
            },
          ],
          extra({ values, onClose, fetchData, form }: any) {
            onClose = onClose;
            return (
              <>
                {values?.look_state !== 'PASS' && values?.state !== 'AUDIT' && (
                  <XlbButton
                    type="primary"
                    onClick={() => handleSubmit(form, 'submit')}
                  >
                    提交
                  </XlbButton>
                )}
                {values?.look_state === 'PASS' &&
                values?.state === 'AUDIT' &&
                values?.data_state === 'TEMP' &&
                hasAuth(['供应商证件', '审核']) ? (
                  <XlbDropdownButton
                    trigger={['click']}
                    dropList={[{ label: '审核通过' }, { label: '审核拒绝' }]}
                    dropdownItemClick={(index) => {
                      processingResults(values, index, onClose, fetchData);
                    }}
                    label={'审核'}
                  ></XlbDropdownButton>
                ) : null}
                {
                  <XlbButton
                    type="primary"
                    onClick={() => {
                      handleCheck(values, 'aduit');
                    }}
                  >
                    审核记录
                  </XlbButton>
                }
              </>
            );
          },
        }}
        searchFieldProps={{
          formList: formList,
          initialValues: {
            ...record,
            query_date_type: 'UPDATE',
            store_ids: record ? record.store_ids : [],
          },
        }}
        exportFieldProps={{
          url: hasAuth(['供应商证件', '导出'])
            ? '/scm/hxl.scm.storefilemanage.export'
            : undefined,
          fileName: '供应商证件管理',
        }}
        tableFieldProps={{
          url: '/scm/hxl.scm.supplierfilemanage.page',
          tableColumn: tableList,
          immediatePost: true,
          primaryKey: 'fid',
          showColumnsSetting: false,
        }}
      />
    </div>
  );
};

export default Index;
