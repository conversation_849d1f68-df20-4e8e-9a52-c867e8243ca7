.cardContent {
  height: 100%;
  // :global .ant-picker .ant-picker-input {
  //   width: auto !important;
  // }
}
.cilck {
  color: #3d66fe;
  text-decoration: underline;
  cursor: pointer;
}
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardContainer {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 680px;
  .cardTitle {
    display: flex;
    justify-content: space-between;
    padding: 10px 12px;
    border-bottom: 1px solid #e8e8e8;
  }
  .cardBody {
    padding: 12px 12px 0px 12px;
    .label {
      color: #86909c;
      margin-right: 4px;
    }
  }
  :global .ant-form-item .ant-form-item-control-input-content > div {
    width: 200px !important ;
  }
  :global
    .ant-form-item
    .ant-form-item-control-input-content
    > span.ant-input-affix-wrapper {
    width: 200px !important;
  }
}
