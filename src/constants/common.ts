export const REGEX = {
  // 数字校验
  NUMBER: /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/,
  // 正整数
  POSITIVE_INTERGER: /^(0|[1-9][0-9]*)$/,
  TEL: /^1[3456789]\d{9}$/,
};

export enum COLUMN_WIDTH_ESUM {
  TEL = 120,
  FID = 160,
  TIME = 160,
  DATE = 100,
  ITEM_CODE = 96,
  ITEM_BAR_CODE = 124,
  SHORTHAND_CODE = 110,
  STORE_NAME = 140,
  MEMO = 140,
  STOP_SALE = 90,
  ORDER_STATE = 100,
  INDEX = 50,
  ITEM_SPEC = 110,
  BY = 110,
  ORDER_FID = 140,
}

export enum RES_CODE {
  SUCCESS = 0,
  EXPIRED = 2005,
}

export const API_BASE_URL = 'https://test-api.xlbsoft.com';

export const MAX_INT = 2147483647;
