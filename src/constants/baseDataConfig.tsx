import { BasicDataConfig, SelectTreeType, SelectType } from '@xlb/components';
import dayjs from 'dayjs';
const cargoList = [
  {
    label: '组织',
    value: 'ORGANIZATION',
  },
  {
    label: '供应商',
    value: 'SUPPLIER',
  },
];
export const itemProperty = [
  {
    label: '正常',
    value: 'normal',
  },
  {
    label: '停购',
    value: 'stop_purchase',
  },
  {
    label: '停售',
    value: 'stop_sale',
  },
  {
    label: '停止要货',
    value: 'stop_request',
  },
  {
    label: '停止批发',
    value: 'stop_wholesale',
  },
];

export const config: BasicDataConfig = {
  // 选择门店
  store: {
    title: '选择门店',
    url: '/erp/hxl.erp.store.short.page',
    leftUrl: '/erp/hxl.erp.storegroup.find',
    selectedAllUrl: '/erp/hxl.erp.store.ids.find', //全选接口
    resetForm: true,
    columns: (settings) => [
      {
        name: '代码',
        code: 'store_code',
        width: 80,
      },
      {
        name: '名称',
        code: 'store_name',
        width: 150,
      },
      {
        name: '营业执照名称',
        code: 'license_name',
        width: 240,
      },
      {
        name: '执照类型',
        code: 'license_type',
        width: 100,
        render(text) {
          const obj: any = {
            COMPANY: '公司',
            PERSONAL: '个人',
          };
          return obj[text];
        },
      },
      {
        name: '门店分组',
        code: 'store_group',
        width: 100,
        render(text) {
          return text?.name || '';
        },
      },
      {
        name: '配送类型',
        code: 'delivery_type',
        width: 80,
        render(text) {
          const obj: any = {
            JOIN: '加盟',
            DIRECT: '直营',
          };
          return obj[text];
        },
      },
      {
        name: '组织',
        code: 'org_name',
        width: 140,
        hidden: !settings?.enable_organization,
      },
      {
        name: '经营类型',
        code: 'management_type',
        width: 80,
        render(text) {
          const obj: any = {
            0: '直营',
            1: '加盟',
          };
          return obj[text];
        },
      },
    ],
    settingProps: {
      url: '/erp/hxl.erp.baseparam.read',
    },
    formList: (settings, data) => {
      return [
        {
          type: 'input',
          label: '关键字',
          name: 'keyword',
          suffix: true,
        },
        {
          type: 'select',
          label: '门店标签',
          name: 'store_label_ids',
          multiple: true,
          options: [],
          selectRequestParams: {
            url: '/erp/hxl.erp.storelabel.find',
            responseTrans(data) {
              const options: SelectType[] = data.map((item: any) => {
                const obj: SelectType = {
                  label: item.store_label_name,
                  value: item.id,
                };
                return obj;
              });
              return options;
            },
          },
        },
        {
          type: 'select',
          label: '经营类型',
          name: 'management_type',
          options: [
            { label: '直营店', value: '0' },
            { label: '加盟店', value: '1' },
          ],
        },
        {
          type: 'select',
          label: '配送类型',
          name: 'delivery_type',
          options: [
            { label: '直营', value: 'DIRECT' },
            { label: '加盟', value: 'JOIN' },
          ],
        },
        {
          type: 'inputDialog',
          label: '行政区域',
          name: 'city_codes',
          treeModalConfig: {
            zIndex: 2002,
            title: '选择区域', // 标题
            url: '/erp/hxl.erp.store.area.find.all', // 请求地址
            dataType: 'lists',
            checkable: true, // 是否多选
            primaryKey: 'code',
            // data: {
            //   enabled: true
            // },
            // params: {
            //   company_id: LStorage.get('userInfo')?.company_id,
            //   // levels: [0, 1]
            // },
            // requestParams: {
            //   levels: [0, 1]
            // },
            fieldName: {
              id: 'code',
              parent_id: 'parent_code',
            },
            width: 360, // 模态框宽度
          },
          fieldNames: {
            // @ts-ignore
            idKey: 'code',
            nameKey: 'name',
          },
        },
        {
          type: 'inputDialog',
          label: '业务区域',
          name: 'business_area_ids',
          dialogParams: {
            type: 'businessArea',
            dataType: 'lists',
            isLeftColumn: false,
            isMultiple: true,
          },
        },
        {
          type: 'compactDatePicker',
          label: '开业日期',
          name: 'opening_time',
          format: 'YYYY-MM-DD',
        },
        {
          type: 'checkbox',
          label: '',
          name: 'wait_assign',
          colon: false,
          width: 300,
          hidden: !data?.isShowWaitAssign,
          options: [{ label: '仅查待分配门店', value: 'wait_assign' }],
        },
      ];
    },
    withSelect: true,
    selectOptions: (settings, data) => {
      return [
        {
          label: '门店分组',
          value: 'group',
          reqUrl: '/erp/hxl.erp.storegroup.find',
        },
        {
          label: '业务区域',
          value: 'business',
          reqUrl: '/erp/hxl.erp.businessarea.find',
        },
        {
          label: '行政区域',
          value: 'admin',
          reqUrl: '/erp/hxl.erp.store.area.find.all',
          filter: [
            {
              label: '省',
              value: 1,
            },
            {
              label: '市',
              value: 2,
            },
            {
              label: '区',
              value: 3,
            },
          ],
        },
        {
          label: '门店标签',
          value: 'tag',
          reqUrl: '/erp/hxl.erp.storelabel.find',
        },
        settings?.enable_organization && !data?.org_ids?.length
          ? {
              label: '组织分组',
              value: 'org',
              reqUrl: '/erp/hxl.erp.org.find',
            }
          : null,
      ].filter((item) => !!item) as Array<SelectTreeType>;
    },
    primaryKey: 'id',
    // leftKey: 'store_group_id',
    leftKey: (url: string): string => {
      const urlMatchLeftKey: Record<string, any> = {
        '/erp/hxl.erp.storegroup.find': {
          isWithChild: true,
          queryParams: 'store_group_ids',
        },
        '/erp/hxl.erp.businessarea.find': {
          // isWithChild: true,
          queryParams: 'business_area_ids',
        },
        '/erp/hxl.erp.store.area.find.all': {
          // isWithChild: true,
          queryParams: 'city_codes',
          fieldName: { parent_id: 'parent_code', id: 'code' },
        },
        '/erp/hxl.erp.storelabel.find': {
          // isWithChild: true,
          queryParams: 'store_label_ids',
          fieldName: { id: 'id', name: 'store_label_name' },
        },
        '/erp/hxl.erp.org.find': {
          // isWithChild: true,
          queryParams: 'org_ids',
          fieldName: { id: 'id', name: 'name' },
        },
      };
      return urlMatchLeftKey[url];
    },

    // @ts-ignore
    customPrevPost: (params: any, data: any) => {
      const result: any = {};
      Object.keys(data).forEach((key: any) => {
        const val = data[key];
        if (val) {
          result[key] =
            key == 'store_group_ids' ? data[key].filter(Boolean) : [data[key]];
        } else {
          result[key] = undefined;
        }
      });
      return {
        ...params,
        ...result,
        // org_ids: data?.org_ids ? [data?.org_ids] : null,
        opening_time: params?.opening_time?.length
          ? params.opening_time
          : undefined,
        wait_assign: params?.wait_assign?.includes('wait_assign')
          ? true
          : false,
      };
    },
  },
  businessArea: {
    title: '选择业务区域',
    url: '/erp/hxl.erp.businessarea.find',
    leftUrl: '',
    // dataType: 'list',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
      },
      {
        name: '区域',
        code: 'name',
        features: { sortable: true },
        width: 100,
      },
      {
        name: '',
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
      },
    ],
  },
  //供应商
  supplier: {
    title: '选择供应商',
    url: '/erp/hxl.erp.supplier.short.page',
    leftUrl: '/erp/hxl.erp.suppliercategory.find',
    columns: [
      {
        name: '代码',
        code: 'code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '供应商类别',
        code: 'supplier_identity',
        width: 150,
        features: {
          sortable: true,
        },
        render: (text: string) => {
          const email = [
            { label: '父供应商', value: 'PARENT' },
            { label: '子供应商', value: 'CHILD' },
            { label: '普通供应商', value: 'NORMAL' },
          ];
          return (
            <span>{email.find((item) => item.value === text)?.label}</span>
          );
        },
      },
      {
        name: '营业执照名称',
        code: 'letterhead',
        width: 104,
      },
      {
        name: '纳税人类型',
        code: 'taxpayer_type',
        width: 80,
        render(text) {
          const obj: Record<number, string> = {
            0: '一般纳税人',
            1: '小规模纳税人',
          };
          return obj[text];
        },
      },
      {
        name: '类别',
        code: 'supplier_category_name',
        width: 120,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        label: '供应商类别',
        name: 'supplier_identities',
        type: 'select',
        multiple: true,
        options: [
          { label: '父供应商', value: 'PARENT' },
          { label: '子供应商', value: 'CHILD' },
          { label: '普通供应商', value: 'NORMAL' },
        ],
      },
      // ????!!!! value又什么东西
      {
        type: 'select',
        label: '供货组织',
        name: 'supply_ogs_ids',
        selectRequestParams: {
          url: '/erp/hxl.erp.org.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        options: [],
      },
      {
        type: 'select',
        label: '结算方式',
        name: 'settlement_type',
        options: [
          {
            label: '现结',
            value: 0,
          },
          {
            label: '月结',
            value: 1,
          },
          {
            label: '批结',
            value: 2,
          },
          {
            label: '货到付款',
            value: 3,
          },
        ],
      },
      {
        type: 'checkbox',
        label: '',
        colon: false,
        name: 'filter_un_actived',
        options: [{ label: '仅显示启用供应商', value: 'filter_un_actived' }],
      },
    ],
    initialValues: { filter_un_actived: 'filter_un_actived' },
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '供应商分类',
        reqUrl: '/erp/hxl.erp.suppliercategory.find',
      },
      {
        value: 2,
        label: '采购组',
        reqUrl: '/erp/hxl.erp.purchasegroup.find',
      },
    ],
    leftKey: (url) => {
      const obj: Record<string, any> = {
        '/erp/hxl.erp.suppliercategory.find': {
          queryParams: 'category_ids',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.purchasegroup.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
      };

      return obj[url];
    },

    prevPost(params: any) {
      return {
        ...params,
        category_ids: params.category_ids ? [params.category_ids] : undefined,
        filter_un_actived: params.filter_un_actived?.includes(
          'filter_un_actived',
        )
          ? true
          : false,
      };
    },
  },
  // 商品档案
  item: {
    title: '选择商品',
    url: '/erp/hxl.erp.item.short.page',
    leftUrl: '/erp/hxl.erp.category.find',
    resetForm: true,
    columns: [
      {
        name: '代码',
        code: 'code',
        width: 80,
      },
      {
        name: '代码',
        code: 'bar_code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '采购规格',
        code: 'purchase_spec',
        width: 104,
      },
      {
        name: '商品类型',
        code: 'item_type',
        width: 80,
        render(text) {
          const obj: Record<string, string> = {
            STANDARD: '标准商品',
            COMBINATION: '组合商品',
            COMPONENT: '成分商品',
            MAKEBILL: '制单组合',
            GRADING: '分级商品',
          };
          return obj[text];
        },
      },
      {
        name: '基本单位',
        code: 'unit',
        width: 80,
      },
      {
        name: '保质期',
        code: 'period',
        width: 80,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'select',
        label: '商品标签',
        name: 'item_label_id',
        selectRequestParams: {
          url: '/erp/hxl.erp.itemlabel.find',
          responseTrans(data) {
            const options: SelectType[] = data.map((item: any) => {
              const obj: SelectType = {
                label: item.item_label_name,
                value: item.id,
              };
              return obj;
            });
            return options;
          },
        },
        // @ts-ignore
        onChange(e, form: FormInstance) {
          if (!e) {
            form.setFieldValue('item_label_ids', []);
          } else {
            form.setFieldValue('item_label_ids', [e]);
          }
        },
        options: [],
      },
      {
        type: 'inputPanel',
        label: '商品属性',
        name: 'checkValue',
        placeholder: '正常/停购/停售/停止要货/停止批发',
        afterChange: (value) => {
          // 正常与四个停止选项互斥逻辑
          const stopArr = [
            'stop_purchase',
            'stop_sale',
            'stop_request',
            'stop_wholesale',
          ];
          const stopMap: Record<string, number> = {
            normal: -1,
            stop_purchase: -1,
            stop_sale: -1,
            stop_request: -1,
            stop_wholesale: -1,
          };
          value.forEach((item: any, index) => {
            const wight = stopMap[item];
            if (typeof wight === 'number') {
              stopMap[item] = index;
            }
          });

          let wight: number = -1,
            current: string,
            newValue: typeof value;
          Object.keys(stopMap).forEach((key) => {
            const value = stopMap[key];
            if (wight < value) {
              wight = value;
              current = key;
            }
          });
          //@ts-ignore
          if (current == 'normal') {
            newValue = value.filter((item) => !stopArr.includes(item));
          } else {
            newValue = value.filter((item) => item !== 'normal');
          }

          return newValue;
        },
        items: [
          {
            label: '仅显示',
            key: 1,
          },
          {
            label: '不显示',
            key: 0,
          },
        ],
        allowClear: true,
        options: itemProperty,
        width: 372,
      },
      {
        type: 'input',
        name: 'item_label_ids',
        hidden: true,
      },
    ],
    prevPost: (params: any) => {
      const data = {
        ...params,
      };
      itemProperty.forEach((item) => {
        data[item.value] = undefined;
      });
      params?.checkValue?.forEach((v: any) => {
        data[v.value] = !!v.itemKey;
      });
      return data;
    },
    // initialValues: {
    //   checkValue: [
    //     // { label: "正常", value: "normal", itemLable: "仅显示", itemKey: 1 }
    //     'normal'
    //   ]
    // },
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: Record<string, any> = {
        '/erp/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };

      return obj[url];
    },
  },
  //供应商查询商品接口
  goods: {
    title: '选择商品',
    url: '/erp/hxl.erp.item.supplier.page',
    leftUrl: '/erp/hxl.erp.category.find',
    resetForm: true,
    columns: [
      {
        name: '代码',
        code: 'code',
        width: 80,
      },
      {
        name: '条码',
        code: 'bar_code',
        width: 80,
      },
      {
        name: '商品名称',
        code: 'name',
        width: 150,
        features: {
          sortable: true,
        },
      },
      {
        name: '采购规格',
        code: 'purchase_spec',
        width: 104,
      },
      {
        name: '商品类型',
        code: 'item_type',
        width: 80,
        render(text) {
          const obj: Record<string, string> = {
            MAINSPEC: '主规格商品',
            MULTIPLESPEC: '多规格商品',
            STANDARD: '标准商品',
            COMBINATION: '组合商品',
            COMPONENT: '成分商品',
            MAKEBILL: '制单组合',
            GRADING: '分级商品',
          };
          return obj[text];
        },
      },
      {
        name: '基本单位',
        code: 'unit',
        width: 80,
      },
      {
        name: '保质期',
        code: 'period',
        width: 80,
      },
    ],
    formList: [
      {
        type: 'input',
        label: '关键字',
        name: 'keyword',
        suffix: true,
      },
    ],
    withSelect: true,
    selectOptions: [
      {
        value: 1,
        label: '商品分类',
        reqUrl: '/erp/hxl.erp.category.find',
      },
      {
        value: 2,
        label: '商品部门',
        reqUrl: '/erp/hxl.erp.dept.find',
      },
      {
        value: 3,
        label: '商品品牌',
        reqUrl: '/erp/hxl.erp.brand.find',
      },
      {
        value: 4,
        label: '供应商',
        reqUrl: '/erp/hxl.erp.suppliercategory.findwithsupplier',
      },
    ],
    leftKey: (url) => {
      const obj: Record<string, any> = {
        '/erp/hxl.erp.category.find': {
          queryParams: 'category_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.dept.find': {
          queryParams: 'item_dept_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.brand.find': {
          queryParams: 'item_brand_id',
          fieldName: { parent_id: 'parent_id' },
        },
        '/erp/hxl.erp.suppliercategory.findwithsupplier': {
          queryParams: 'supplier_ids',
          isWithChild: true,
          fieldName: { parent_id: 'parent_id', children: 'suppliers' },
          dataType: 'tree',
        },
      };

      return obj[url];
    },
  },
  /**
   * @采购经理
   */
  purchaseManager: {
    title: '采购经理',
    url: '/scm-mdm/hxl.scm.centeruser.page',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
      },
      {
        name: '用户名称',
        code: 'name',
        width: 180,
      },
      {
        name: '手机号',
        code: 'tel',
        width: 120,
        render: (text) => {
          return <div>{text?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}</div>;
        },
      },
    ],
    formList: [
      {
        type: 'input',
        label: '用户名称',
        name: 'keyword',
        suffix: true,
      },
      {
        type: 'input',
        label: '手机号',
        name: 'tel',
        suffix: true,
      },
    ],
    primaryKey: 'id',
  },
  /**
   * @ scm仓库抽检 入库申请单单据选择
   */
  scmInOrder: {
    title: '选择单据',
    url: '/wms/hxl.wms.inapplicationorder.scm.find',
    columns: [
      {
        name: '序号',
        code: '_index',
        width: 60,
        align: 'center',
        lock: true,
      },
      {
        name: '单据号',
        code: 'fid',
        width: 200,
        features: { sortable: true },
      },
      {
        name: '供应商名称',
        code: 'client_id',
        width: 250,
        features: { sortable: true },
        render(text, record) {
          return record?.client_name;
        },
      },
      {
        name: '预约时间',
        code: 'appointment_date',
        width: 150,
        features: { sortable: true },
      },
    ],
    formList: [
      {
        type: 'compactDatePicker',
        label: '日期范围',
        name: 'audit_date',
        allowClear: false,
      },
      {
        label: '单据号',
        name: 'keyword',
        type: 'input',
      },
    ],
    initialValues: {
      audit_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    },
    primaryKey: 'fid',
  },

  cargoOwner: {
    title: '选择货主',
    url: '/erp/hxl.erp.cargo.owner.page',
    formList: [
      {
        label: '关键字',
        name: 'keyword',
        type: 'input',
        allowClear: true,
        check: true,
      },
      {
        label: '货主类型',
        name: 'owner_type',
        type: 'select',
        check: true,
        // multiple: true,
        allowClear: true,
        options: [
          {
            label: '组织',
            value: 'ORGANIZATION',
          },
          {
            label: '供应商',
            value: 'SUPPLIER',
          },
        ],
      },
    ],
    columns: [
      {
        title: '序号',
        key: 'index',
        code: '_index',
        width: 60,
        align: 'center',
      },
      {
        title: '货主代码',
        key: 'owner_code',
        code: 'owner_code',
        align: 'left',
        width: 180,
        sorter: (a, b) => a.owner_code.localeCompare(b.owner_code),
      },
      {
        title: '组织代码/供应商代码',
        key: 'source_code',
        code: 'source_code',
        align: 'left',
        width: 180,
        sorter: (a, b) => a.source_code.localeCompare(b.source_code),
      },
      {
        title: '组织代码/供应商名称',
        key: 'source_name',
        code: 'source_name',
        align: 'left',
        width: 280,
        sorter: (a, b) => a.source_name.localeCompare(b.source_name),
      },
      {
        title: '货主类型',
        key: 'owner_type',
        code: 'owner_type',
        align: 'left',
        width: 120,
        sorter: (a, b) => a.owner_type.localeCompare(b.owner_type),
        render: (value: string) => {
          const curT = cargoList?.find((v) => v.value === value);
          return <div>{curT?.label}</div>;
        },
      },
    ],
  },
};
