import {
  CERTIFICATE_MAINTAINED,
  FoodSafeType,
} from '@/pages/newProductSign/data';
import { LStorage } from '@/utils/storage';
import { ProForm, ProFormDependency } from '@ant-design/pro-form';
import {
  XlbIcon,
  XlbInputDialogProps,
  XlbInputPanelProps,
  XlbSelect,
} from '@xlb/components';
import { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type';
import { safeMath, toFixed } from '@xlb/utils';
import { FormInstance, Tooltip, message } from 'antd';
import copy from 'copy-to-clipboard';
import omit from 'lodash/omit';
import {
  EXECUTIVE_STANDARD_LIST,
  ITEM_TYPE,
  MONTHS_OPTION,
  executiveStandardType,
} from './data';
import { FieldKeyMap, stockUpplanFormList } from './stockUpPlanConfig';
import { storeOrdersFieldKeyMap, storeOrdersFormList } from './storeOrders';

const userInfo = LStorage.get('userInfo');

export const ScmFieldKeyMap = {
  supplierIds: 'supplierIds',
  categoryManage: 'categoryManage',
  goodsCategory: 'goodsCategory',
  expireType: 'expireType',
  packageBar: 'packageBar',
  executiveStandard: 'executiveStandard',
  executiveStandardType: 'executiveStandardType',
  centimeter: 'centimeter',
  scmUploadPicture: 'scmUploadPicture',
  scmItemByBoolean: 'scmItemByBoolean',
  productDepartment: 'productDepartment',
  productBrand: 'productBrand',
  scmUnit: 'scmUnit',
  priceWithRadio: 'priceWithRadio',
  scmPrice: 'scmPrice',
  productAttributes: 'productAttributes',
  deliveryPriceWithRadio: 'deliveryPriceWithRadio',
  standardPriceWithRadio: 'standardPriceWithRadio',
  taxRate: 'taxRate',
  scmOrgId: 'scmOrgId',
  scmCategoryId: 'scmCategoryId',
  scmQualityReportProducers: 'scmQualityReportProducers',
  specUnit: 'specUnit',
  fromList: 'fromList',
  scmItemId: 'scmItemId',
  scmItemIds: 'scmItemIds',
  deliveryPriceCompute: 'deliveryPriceCompute',
  wholesaleCompute: 'wholesaleCompute',
  scmStoreIds: 'scmStoreIds',
  scmStoreId: 'scmStoreId',
  scmStorehouseId: 'scmStorehouseId',
  goodsCategoryId: 'goodsCategoryId',
  scmSupplierProducer: 'scmSupplierProducer',
  purchaseVolume: 'purchaseVolume',
  deliveryVolume: 'deliveryVolume',
  scmDate: 'scmDate',
  billDate: 'billDate',
  orgIds: 'orgIds',
  billStoreIds: 'billStoreIds',
  EXCEPTIONCATEGORY_PARENT: 'EXCEPTIONCATEGORY_PARENT',
  EXCEPTIONCATEGORY: 'EXCEPTIONCATEGORY',
  scmSupplierId: 'scmSupplierId',
  scmInOrderFid: 'scmInOrderFid',
  scmInputNumber: 'scmInputNumber',
  scmqualifiedRatio: 'scmqualifiedRatio',
  scmCommonUpload: 'scmCommonUpload',
  DETAILPROBLEM: 'DETAILPROBLEM',
  contractExpirationReminder: 'contractExpirationReminder',
  aloneFeeOrganizations: 'aloneFeeOrganizations',
  scmOrgIds: 'scmOrgIds',
  centerLaterAndReport: 'centerLaterAndReport',
  scmInputcheckcount: 'scmInputcheckcount',
  externalReportList: 'externalReportList',
  disabledExternalReportList: 'disabledExternalReportList',
  scmCenterStoreIds: 'scmCenterStoreIds',
  scmexemptcheckInput: 'scmexemptcheckInput',
  externalReportListNoRules: 'externalReportListNoRules',
  formSpanItemLarge: 'formSpanItemLarge',
  contactCompanys: 'contactCompanys',
  supplierOrgIds: 'supplierOrgIds',
  ...FieldKeyMap,
  ...storeOrdersFieldKeyMap,
};

export const scmFormList: Array<CreateMapItem> = [
  {
    tag: 'SCM',
    label: '供应商',
    id: ScmFieldKeyMap?.supplierIds,
    name: 'supplier_ids',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },
    componentType: 'inputDialog',
  },
  {
    componentType: 'list',
    tag: 'FSMS',
    id: ScmFieldKeyMap.fromList,
    label: '选项',
    fieldProps: {
      max: undefined,
      formList: [
        {
          componentType: 'input',
          id: 'listinput',
          tag: 'FSMS',
          name: 'name',
          // itemSpan: 20,
        },
      ],
      createText: '新增选项',
    },
  },
  {
    tag: 'SCM',
    label: '商品品类',
    id: ScmFieldKeyMap.goodsCategoryId,
    name: 'item_public_category_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(
        `${baseURL}/scm-mdm/hxl.scm.itempubliccategory.list`,
        {},
      );
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'SCM',
    label: '保质期限',
    id: ScmFieldKeyMap.expireType,
    name: 'expire_type_num',
    componentType: 'inputNumber',
    dependencies: ['expire_type'],
    fieldProps: {
      precision: 2,
      defaultValue: 0,
      min: 0,
      addonAfter: (
        <ProForm.Item
          noStyle
          name={'expire_type'}
          rules={[{ required: true, message: '请选择单位' }]}
        >
          <XlbSelect
            width={80}
            defaultValue={1}
            options={[
              { label: '天', value: 1 },
              { label: '月', value: 2 },
            ]}
          />
        </ProForm.Item>
      ),
      style: { width: '100%' },
    },
  },
  {
    tag: 'SCM',
    label: '外箱码',
    id: ScmFieldKeyMap.packageBar,
    name: 'package_bar_code',
    dependencies: ['enable_package_bar_code', 'retail_method'],
    componentType: 'input',
    disabled: (obj) => {
      return obj.retail_method === 0;
    },
    fieldProps: {
      addonBefore: (
        <ProFormDependency name={['retail_method']}>
          {(obj) => {
            return (
              <ProForm.Item noStyle name={'enable_package_bar_code'}>
                <XlbSelect
                  disabled={obj?.retail_method === 0}
                  width={80}
                  defaultValue={true}
                  options={[
                    { label: '有', value: true },
                    { label: '无', value: false },
                  ]}
                />
              </ProForm.Item>
            );
          }}
        </ProFormDependency>
      ),
      style: { width: '100%' },
    },
  },
  {
    tag: 'SCM',
    label: '是否执行企业标准',
    id: ScmFieldKeyMap.executiveStandardType,
    name: 'executive_standard_type',
    dependencies: ['executive_standard'],
    componentType: 'select',
    fieldProps: {
      options: EXECUTIVE_STANDARD_LIST,
      defaultValue: executiveStandardType.NATIONAL_STANDARDS,
    },
  },
  {
    tag: 'SCM',
    label: '产品执行标准',
    id: ScmFieldKeyMap.executiveStandard,
    name: 'executive_standard',
    dependencies: ['executive_standard_type'],
    componentType: 'input',
  },
  {
    tag: 'SCM',
    componentType: 'inputNumber',
    id: ScmFieldKeyMap.centimeter,
    fieldProps: {
      precision: 2,
      suffix: 'cm',
      defaultValue: 0,
    },
  },
  {
    tag: 'SCM',
    label: '税率',
    id: ScmFieldKeyMap.taxRate,
    name: 'tax_rate',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.baseparam.read`,
        {},
      );
      if (result?.code === 0) {
        if (Array.isArray(result.data?.tax_rates)) {
          return result.data?.tax_rates?.map((item: any) => {
            return {
              label: item?.toString(),
              value: item,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'SCM',
    componentType: 'upload',
    id: ScmFieldKeyMap.scmUploadPicture,
    fieldProps: {
      action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
      maxCount: 1,
      columnNum: 1,
      showUpload: true,
      listType: 'picture-card',
      accept: 'image',
      data: {
        refType: 'SUPPLIER_AUDIT',
        refId: 'sfsffefefe',
      },
    },
  },
  {
    tag: 'SCM',
    label: '是否',
    id: ScmFieldKeyMap.scmItemByBoolean,
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '商品部门',
    id: ScmFieldKeyMap.productDepartment,
    name: 'item_dept_id',
    componentType: 'select',
    fieldProps: {
      placeholder: '请选择',
    },
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.dept.find`,
        {},
      );
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'SCM',
    label: '商品品牌',
    id: ScmFieldKeyMap.productBrand,
    name: 'item_brand_id',
    componentType: 'select',
    fieldProps: {
      placeholder: '请选择',
    },
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.brand.find`,
        {},
      );
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
              center_id: item.center_id,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'SCM',
    label: '基本单位',
    id: ScmFieldKeyMap.scmUnit,
    name: 'unit',
    componentType: 'select',
    fieldProps: { placeholder: '请选择' },
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.itemunit.find`,
        {},
      );
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.item_unit_name,
              value: item.item_unit_name,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'SCM',
    label: '配送价类型',
    id: ScmFieldKeyMap.deliveryPriceWithRadio,
    componentType: 'inputNumber',
    dependencies: ['delivery_type'],
    name: 'delivery_type_item',
    fieldProps: {
      placeholder: '请输入',
      style: { width: '100%' },
      defaultValue: 0,
      min: 0,
      addonBefore: (
        <ProForm.Item noStyle name={'delivery_type'}>
          <XlbSelect width={80} options={ITEM_TYPE} defaultValue={2} />
        </ProForm.Item>
      ),
    },
  },
  {
    tag: 'SCM',
    label: '配送价',
    id: ScmFieldKeyMap.deliveryPriceCompute,
    name: 'deliveryPrice',
    disabled: true,
    componentType: 'inputNumber',
    dependencies: [
      'state',
      'purchase_price',
      'delivery_type',
      'delivery_type_item',
    ],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      if (obj.delivery_type === 0) {
        console.log(obj.delivery_type_item, obj.purchase_price);
        return (
          (obj.delivery_type_item * 0.01 + 1) *
          Number(obj.purchase_price || 0)
        )?.toFixed(8);
      }
      if (obj.delivery_type === 1) {
        return (
          Number(obj.delivery_type_item || 0) + Number(obj.purchase_price || 0)
        )?.toFixed(8);
      }
      if (obj.delivery_type === 2) {
        return obj.delivery_type_item?.toFixed(8);
      }
      return null;
    },

    fieldProps: {
      placeholder: '请输入',
      precision: 8,
      min: 0,
      textAfter: (
        <ProFormDependency
          name={[
            'state',
            'purchase_price',
            'deliveryPrice',
            'delivery_type',
            'delivery_type_item',
          ]}
        >
          {(obj, form) => {
            let value;
            const purchase_price = Number(obj.purchase_price || 0);
            const delivery_type_item = Number(obj.delivery_type_item || 0);
            if (obj.delivery_type == 0) {
              value =
                delivery_type_item && purchase_price
                  ? (
                      ((obj.deliveryPrice - purchase_price) /
                        obj.deliveryPrice) *
                      100
                    ).toFixed(2)
                  : '0.00';
            }
            if (obj.delivery_type == 1) {
              value =
                delivery_type_item && purchase_price
                  ? ((delivery_type_item / obj.deliveryPrice) * 100).toFixed(2)
                  : '0.00';
            }
            if (obj.delivery_type == 2) {
              value =
                delivery_type_item && purchase_price
                  ? (
                      ((delivery_type_item - purchase_price) /
                        delivery_type_item) *
                      100
                    ).toFixed(2)
                  : '0.00';
            }
            return (
              <Tooltip title={`配送毛利率:${Number(value).toFixed(2)}%`}>
                <span
                  style={{
                    color: Number(value) >= 0 ? 'red' : 'green',
                    marginLeft: '10px',
                  }}
                >
                  <span>{value}</span>
                  <span>%</span>
                </span>
              </Tooltip>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'SCM',
    label: '批发价',
    id: ScmFieldKeyMap.wholesaleCompute,
    name: 'wholesalePrice',
    disabled: true,
    componentType: 'inputNumber',
    dependencies: [
      'state',
      'purchase_price',
      'wholesale_type',
      'wholesale_type_item',
    ],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      if (obj.wholesale_type === 0) {
        console.log(obj.wholesale_type_item, obj.purchase_price);
        return (
          (obj.wholesale_type_item * 0.01 + 1) *
          Number(obj.purchase_price || 0)
        )?.toFixed(8);
      }
      if (obj.wholesale_type === 1) {
        return (
          Number(obj.wholesale_type_item || 0) + Number(obj.purchase_price || 0)
        )?.toFixed(8);
      }
      if (obj.wholesale_type === 2) {
        return obj.wholesale_type_item?.toFixed(8);
      }
      return null;
    },
    fieldProps: {
      placeholder: '请输入',
      precision: 8,
      min: 0,
      textAfter: (
        <ProFormDependency
          name={[
            'state',
            'purchase_price',
            'wholesalePrice',
            'wholesale_type',
            'wholesale_type_item',
          ]}
        >
          {(obj, form) => {
            let value;
            const purchase_price = Number(obj.purchase_price || 0);
            const wholesale_type_item = Number(obj.wholesale_type_item || 0);
            if (obj.wholesale_type == 0) {
              value =
                wholesale_type_item && purchase_price
                  ? (
                      ((obj.wholesalePrice - purchase_price) /
                        obj.wholesalePrice) *
                      100
                    ).toFixed(2)
                  : '0.00';
            }
            if (obj.wholesale_type == 1) {
              value =
                wholesale_type_item && purchase_price
                  ? ((wholesale_type_item / obj.wholesalePrice) * 100).toFixed(
                      2,
                    )
                  : '0.00';
            }
            if (obj.wholesale_type == 2) {
              value =
                wholesale_type_item && purchase_price
                  ? (
                      ((wholesale_type_item - purchase_price) /
                        wholesale_type_item) *
                      100
                    ).toFixed(2)
                  : '0.00';
            }
            return (
              <Tooltip title={`批发毛利率:${Number(value).toFixed(2)}%`}>
                <span
                  style={{
                    color: Number(value) >= 0 ? 'red' : 'green',
                    marginLeft: '10px',
                  }}
                >
                  <span>{value}</span>
                  <span>%</span>
                </span>
              </Tooltip>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'SCM',
    label: '斤规格',
    id: ScmFieldKeyMap.specUnit,
    componentType: 'input',
    name: 'weight_spec',
    dependencies: ['options', 'weight_spec'],
    fieldProps: {
      placeholder: '请输入',
      style: { width: '100%' },
      addonAfter: (
        <ProFormDependency name={['options', 'weight_spec']}>
          {(obj, form) => {
            return (
              <ProForm.Item
                noStyle
                name={'weight_spec_unit'}
                rules={[
                  ({ getFieldValue }: any) => ({
                    validator: (_: any, value: any) => {
                      if (!value && obj?.weight_spec !== '0') {
                        return Promise.reject(new Error('请选择单位'));
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <XlbSelect width={80} options={obj.options} placeholder="*包" />
              </ProForm.Item>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'SCM',
    id: ScmFieldKeyMap.standardPriceWithRadio,
    label: '标准售价',
    componentType: 'inputNumber',
    name: 'sale_price',
    fieldProps: {
      placeholder: '请输入',
      precision: 2,
      min: 0,
      textAfter: (
        <ProFormDependency name={['sale_price', 'purchase_price']}>
          {(obj, form) => {
            const value =
              obj.sale_price && obj.purchase_price
                ? (
                    ((obj.sale_price - obj.purchase_price) / obj.sale_price) *
                    100
                  ).toFixed(2)
                : '0.00';
            return (
              <Tooltip title={`零售毛利率:${Number(value).toFixed(2)}%`}>
                <span
                  style={{
                    color: Number(value) > 0 ? 'red' : 'green',
                    marginLeft: 10,
                  }}
                >
                  <span>{value}</span>
                  <span>%</span>
                </span>
              </Tooltip>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'SCM',
    label: '商品属性',
    id: ScmFieldKeyMap.productAttributes,
    name: 'check',
    componentType: 'checkbox',
  },
  {
    tag: 'SCM',
    label: '指定公司',
    id: ScmFieldKeyMap?.scmOrgId,
    name: 'org_id',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.org.find', {
        levels: [2],
      });
      if (res?.code === 0) {
        return res.data?.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
    componentType: 'select',
  },
  {
    tag: 'SCM',
    label: '商品分类',
    id: ScmFieldKeyMap.scmCategoryId,
    fieldProps: {
      treeModalConfig: {
        // disabled: true,
        width: 300,
        title: '选择商品分类', // 标题
        url: '/erp/hxl.erp.category.find', // 请求地址
        dataType: 'lists',
        checkable: false, // 是否多选
        primaryKey: 'id',
        afterPost: (data: any) => {
          return data.map((item: any) => {
            return {
              ...item,
              disabled: item?.level !== 3,
            };
          });
        },
      } as any,
    },
    componentType: 'inputDialog',
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  {
    componentType: 'inputDialog',
    tag: 'scm',
    id: ScmFieldKeyMap.scmQualityReportProducers,
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'tree',
        isMultiple: false,

        data: {
          supplier_type: 'PRODUCER',
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },

  {
    componentType: 'select',
    tag: 'scm',
    id: ScmFieldKeyMap.scmSupplierProducer,
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.supplier.read.supplier`,
        { id: userInfo?.supplier?.id },
      );
      if (result?.code === 0) {
        if (result.data?.supplier_type == 'PRODUCER') {
          if (
            Array.isArray(result.data?.supplier_association_producer_suppliers)
          ) {
            const list =
              result.data?.supplier_association_producer_suppliers.map(
                (item: any) => {
                  return {
                    value: item?.detail_supplier_id,
                    label: item?.name,
                  };
                },
              );
            return [
              ...list,
              {
                value: result.data?.id,
                label: result.data?.name,
              },
            ];
          }
        } else if (result.data?.supplier_type == 'TRADER') {
          if (Array.isArray(result.data?.supplier_association_suppliers)) {
            return result.data?.supplier_association_suppliers.map(
              (item: any) => {
                return {
                  value: item?.detail_supplier_id,
                  label: item?.name,
                };
              },
            );
          }
        }
      }
      return [];
    },
  },
  {
    tag: 'SCM',
    label: '商品',
    id: ScmFieldKeyMap?.scmItemId,
    name: 'item_id',
    fieldProps: {
      dialogParams: {
        type: 'item',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: false,
      },
    },
    formItemProps: {
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
      ) => {
        return event ? event[0] : undefined;
      },
      getValueProps: (e: any) => {
        return { value: e ? [e] : undefined };
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '商品',
    id: ScmFieldKeyMap?.scmItemIds,
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'item',
        dataType: 'lists',
        isMultiple: true,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '门店',
    id: ScmFieldKeyMap?.scmStoreIds,
    name: 'store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '仓库',
    id: ScmFieldKeyMap?.scmStorehouseId,
    name: 'storehouse_id',
    dependencies: ['store_ids'],
    async request(params: any, anybaseURL, globalFetch) {
      const { store_ids } = params;
      let store_idT: number;
      if (!Array.isArray(store_ids) || !store_ids.length) {
        console.warn('store_id不存在');
        return [];
      }

      if (store_ids.length > 1) {
        console.warn('多个store_id');
        return [];
      }
      store_idT = store_ids[0];
      const result = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.storehouse.store.find`,
        {
          ...omit(params, 'store_ids'),
          store_id: store_idT,
        },
      );
      if (Array.isArray(result.data)) {
        return result.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
    // showSearch:true,
    componentType: 'select',
  },
  {
    tag: 'SCM',
    label: '采购经理',
    componentType: 'inputDialog',
    id: 'purchaseManager',
    fieldProps: {
      dialogParams: {
        isLeftColumn: false,
        type: 'purchaseManager',
        dataType: 'tree',
        isMultiple: false,
        data: {
          if_purchase_manager: true,
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  //新品申请
  {
    tag: 'SCM',
    label: '采购体积(m³)',
    id: ScmFieldKeyMap?.purchaseVolume,
    name: 'purchase_volume',
    componentType: 'inputNumber',
    dependencies: ['purchase_length', 'purchase_width', 'purchase_height'],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      const volume =
        obj.purchase_length *
        obj.purchase_width *
        obj.purchase_height *
        0.000001;
      return !volume ? '0.0000' : volume?.toFixed(4);
    },
  },
  {
    tag: 'SCM',
    label: '配送体积(m³)',
    id: ScmFieldKeyMap?.deliveryVolume,
    name: 'delivery_volume',
    componentType: 'inputNumber',
    dependencies: ['purchase_ratio', 'delivery_ratio', 'purchase_volume'],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      const purchase_ratio = Number(obj.purchase_ratio || 0);
      const delivery_ratio = Number(obj.delivery_ratio || 0);

      const volume =
        ((obj?.purchase_volume / purchase_ratio) * delivery_ratio).toFixed(
          4,
        ) === 'NaN'
          ? '0.0000'
          : ((obj?.purchase_volume / purchase_ratio) * delivery_ratio).toFixed(
              4,
            );
      return volume;
    },
  },
  {
    id: ScmFieldKeyMap.scmDate,
    componentType: 'datePicker',
    tag: 'SCM',
  },
  //@ts-ignore
  {
    tag: 'SCM',
    id: 'ScmInputPanel',
    label: '面板/外检报告',
    name: 'inputPanel',
    componentType: 'inputPanel',
    fieldProps: {
      allowClear: true,
      notTransform: true,
      options: [
        {
          label: '停购商品',
          value: 'stop_purchase',
        },
        {
          label: '超期外检报告商品',
          value: 'query_over_due',
        },
        {
          label: '无外检报告商品',
          value: 'no_report_store',
        },
      ],
      items: [
        {
          label: '仅显示',
          key: true,
        },
        {
          label: '不显示',
          key: false,
        },
      ],
    },
    formItemProps: (form: FormInstance) => ({
      label: '  ',
      colon: false,
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputPanelProps['onChange']>>[0],
      ) => {
        let reqValue: any = {
          no_report_store: undefined,
          query_over_due: undefined,
          stop_purchase: undefined,
        };
        Object.keys(reqValue).forEach((v) => {
          event.forEach((item) => {
            if (item.value == v) {
              reqValue[v] = item.itemKey;
            }
          });
        });
        form.setFieldsValue({ ...reqValue });
        return reqValue;
      },
      getValueProps: () => {
        const data = form.getFieldsValue(true);
        let values: any = [];
        const objKeys = Object?.keys(data);
        console.log(objKeys, 'objKeysobjKeys');

        const allListVualue = [
          'no_report_store',
          'query_over_due',
          'stop_purchase',
        ];
        objKeys?.forEach(
          (v) =>
            allListVualue?.includes(v) &&
            data[v] != undefined &&
            values.push(v),
        );
        return {
          value: values,
        };
      },
    }),
  },
  {
    tag: 'SCM',
    id: ScmFieldKeyMap.centerLaterAndReport,
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          componentType: 'checkbox',
          name: 'query_temporary',
          id: 'group1',
          tag: 'center',
          group: false,
          colon: false,
          fieldProps: {
            options: [{ label: '查询临期', value: 'query_temporary' }],
          },
        },
        {
          componentType: 'inputNumber',
          id: 'group2',
          tag: 'center',
          textAfter: '天内外检报告',
          name: 'query_temporary_day',
          async request(formValues, anybaseURL, globalFetch) {
            const data = await globalFetch.post(
              anybaseURL + '/scm/hxl.scm.scmparam.read',
              {},
            );

            if (data.code === 0) {
              return data.data;
            }
            return {};
          },
          handleDefaultValue(data) {
            if (data) {
              return data.item_report_remind_days;
            }
          },
          fieldProps: {
            min: -999,
            max: 999,
          },
        },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '对账月份',
    id: ScmFieldKeyMap.billDate,
    name: 'bill_date',
    componentType: 'datePicker',
    fieldProps: {
      picker: 'month',
      resultFormat: 'YYYY-MM',
      format: 'YYYY-MM',
    },
  },
  {
    tag: 'SCM',
    label: '组织',
    id: ScmFieldKeyMap?.orgIds,
    name: 'org_ids',
    componentType: 'inputDialog',
    fieldProps: {
      treeModalConfig: {
        // @ts-ignore
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
    },
  },
  {
    tag: 'SCM',
    label: '门店',
    componentType: 'inputDialog',
    id: ScmFieldKeyMap.billStoreIds,
    name: 'store_ids',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const obj = form.getFieldsValue(true);
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            query_center: true,
            org_ids: obj?.org_ids || null,
          },
        },
      };
    },
  },

  {
    componentType: 'select',
    tag: 'SCM',
    id: ScmFieldKeyMap.EXCEPTIONCATEGORY_PARENT,
    name: 'abnormal_one_level_id',
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/scm/hxl.scm.abnormalcategory.findTree',
        {
          level: 1,
        },
      );
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },

  {
    componentType: 'select',
    tag: 'SCM',
    id: ScmFieldKeyMap.EXCEPTIONCATEGORY,
    name: 'abnormal_two_level_id',
    dependencies: ['abnormal_one_level_id'],
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      if (!formValues?.abnormal_one_level_id) return [];
      const res = await globalFetch.post(
        anybaseURL + '/scm/hxl.scm.abnormalcategory.findTree',
        {
          level: 2,
          parent_id: formValues?.abnormal_one_level_id,
        },
      );
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },
  /**门店单选 */
  {
    tag: 'SCM',
    label: '门店',
    id: ScmFieldKeyMap?.scmStoreId,
    name: 'store_id',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: false,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
      ) => {
        return event ? event[0] : undefined;
      },
      getValueProps: (value: any) => {
        return { value: value ? [value] : undefined };
      },
    },
    componentType: 'inputDialog',
  },
  /** 供应商单选 */
  {
    tag: 'SCM',
    label: '商品',
    id: ScmFieldKeyMap?.scmSupplierId,
    name: 'supplier_id',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isMultiple: false,
      },
    },
    formItemProps: {
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
      ) => {
        return event ? event[0] : undefined;
      },
      getValueProps: (e: any) => {
        return { value: e ? [e] : undefined };
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '入库申请单',
    id: ScmFieldKeyMap?.scmInOrderFid,
    name: 'in_order_fids',
    dependencies: ['store_id', 'supplier_id', 'item_id', 'fid'],
    disabled: (formValues: any) =>
      !formValues?.store_id ||
      !formValues?.supplier_id ||
      !formValues?.item_id ||
      !!formValues?.fid,
    fieldProps: (form) => {
      const formValues = form.getFieldsValue(true);

      return {
        dialogParams: {
          type: 'scmInOrder',
          dataType: 'lists',
          isMultiple: true,
          isLeftColumn: false,
          immediatePost: false,
          data: {
            store_id: formValues?.store_id,
            supplier_id: formValues?.supplier_id,
            item_id: formValues?.item_id,
          },
          primaryKey: 'fid',
        },
        fieldNames: {
          idKey: 'fid',
          nameKey: 'fid',
        },
      };
    },
    componentType: 'entitySelectDialog',
  },
  {
    tag: 'SCM',
    label: '数字输入框',
    id: ScmFieldKeyMap.scmInputNumber,
    componentType: 'inputNumber',
  },
  {
    tag: 'SCM',
    componentType: 'input',
    label: '合格率',
    name: 'qualified_ratio',
    id: ScmFieldKeyMap.scmqualifiedRatio,
    dependencies: ['check_count', 'unqualified_count'],
    request: (formValues: any) => {
      if (!formValues?.check_count || !formValues?.unqualified_count)
        return undefined;
      return toFixed(
        safeMath.divide(
          safeMath.minus(
            formValues?.check_count,
            formValues?.unqualified_count,
          ),
          formValues?.check_count,
        ) * 100,
        'MONEY',
      );
    },
    handleDefaultValue: (data: any) => {
      return data;
    },
    fieldProps: {
      suffix: '%',
    },
    disabled: true,
  },
  {
    componentType: 'upload',
    tag: 'SCM',
    id: ScmFieldKeyMap.scmCommonUpload,
  },
  {
    componentType: 'select',
    tag: 'SCM',
    id: ScmFieldKeyMap.DETAILPROBLEM,
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/fsms/hxl.center.problemcategory.find',
        {
          parent_id: 0,
          company_ids: userInfo?.company_id ? [userInfo?.company_id] : '',
          quality_types: ['SELF'],
        },
      );
      if (res?.code == 0) {
        return res?.data.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  /**
   * 合同参数
   */
  {
    tag: 'SCM',
    id: 'contractExpirationReminder',
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          tag: 'SCM',
          label: '到期提醒',
          id: 'rebateTip',
          name: 'rebate_tip',
          componentType: 'radio',
          textAfter: ',',
          fieldProps: {
            options: [
              { label: '是', value: true },
              { label: '否', value: false },
            ],
          },
        },
        {
          tag: 'SCM',
          label: '合同到期前',
          id: 'contractRebateExpireday',
          name: 'supplier_contract_rebate_expire_day',
          componentType: 'inputNumber',
          textAfter: '提醒合同负责人',
          colon: false,
          fieldProps: {
            min: 1,
            suffix: '天',
            precision: 0,
            controls: false,
          },
        },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '合同不合并生成费用单',
    id: ScmFieldKeyMap.aloneFeeOrganizations,
    name: 'alone_fee_organizations',
    componentType: 'inputDialog',
    fieldProps: {
      treeModalConfig: {
        topLevelTreeDisabled: true,
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
        afterPost: (data: any) => {
          return data.map((item: any) => {
            return {
              ...item,
              disabled: item?.level !== 2,
            };
          });
        },
      },
    },
    formItemProps: {
      getValueFromEvent(ids: any, list: any, ...args: any) {
        return list;
      },
      getValueProps(value: any) {
        return {
          value: value?.map((item: any) => item?.id),
        };
      },
    },
  },
  /************* */
  {
    tag: 'SCM',
    label: '所属组织',
    id: ScmFieldKeyMap?.scmOrgIds,
    name: 'org_ids',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      return userInfo?.query_orgs
        ? userInfo?.query_orgs.map((item: any) => ({
            ...item,
            label: item.name,
            value: item.id,
          }))
        : [];
    },
    componentType: 'select',
  },
  {
    tag: 'SCM',
    label: '抽检数数字输入框',
    id: ScmFieldKeyMap.scmInputcheckcount,
    componentType: 'input',
    dependencies: ['delivery_count', 'item_id'],
    request: async (formValues: any, anybaseURL, globalFetch) => {
      if (!formValues?.delivery_count || !formValues?.item_id) return undefined;
      const res = await globalFetch.post(
        anybaseURL +
          '/scm/hxl.scm.storehouseitemspotcheck.itemspotcheckstandard.find',
        {
          delivery_count: formValues?.delivery_count,
          item_id: formValues?.item_id,
        },
      );
      if (res.code === 0) {
        return `${res?.['data']?.check_num}`;
      }
      return undefined;
    },
    handleDefaultValue: (data: any) => {
      return data;
    },
  },
  //新品申请上传外检报告list
  {
    tag: 'SCM',
    label: '上传',
    id: ScmFieldKeyMap.externalReportList,
    componentType: 'list',
    dependencies: ['state', 'producerSupplierOptions', 'imports'],
    fieldProps: {
      createText: '添加',
      formList: [
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          hidden: (obj) => {
            return obj?.food_safe_audit_state === FoodSafeType.waitAudit;
          },
          name: 'supplier_quality_record_files',
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请上传',
            }),
          ],
          dependencies: ['state', 'imports'],
          itemSpan: 1,
          fieldProps: {
            action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
            mode: 'textButton',
            listType: 'text',
            accept: ['pdf'],
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          name: 'supplier_quality_record_files',
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请上传',
            }),
          ],
          dependencies: ['state', 'imports'],
          hidden: (obj) => {
            return obj?.food_safe_audit_state !== FoodSafeType.waitAudit;
          },
          fieldProps: {
            action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
            mode: 'look',
            listType: 'text',
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          id: 'supplier_item',
          label: '生产厂商',
          readOnly: false,
          dependencies: ['state', 'supplier_id', 'imports'],
          name: 'supplier_item',
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请选择生产厂商',
            }),
          ],
          fieldProps: (form, index) => {
            const { supplier_quality_record_data_list } =
              form.getFieldsValue(true);
            const supplierItem =
              supplier_quality_record_data_list?.[index?.dataIndex]
                ?.supplier_item;
            return {
              placeholder: '请选择',
              options: form.getFieldValue('producerSupplierOptions'),
              textAfter: supplierItem?.producer_supplier_name && (
                <XlbIcon
                  name="fuzhi"
                  className="custom-form-item-icon"
                  onClick={() => {
                    copy(supplierItem?.producer_supplier_name);
                    message.success('复制成功');
                  }}
                />
              ),
              className: 'custom-form-item',
            };
          },

          formItemProps: {
            getValueFromEvent(a, b, c) {
              return {
                producer_supplier_id: b?.value,
                producer_supplier_name: b?.label,
                origin_place: b?.origin_place,
              };
            },
            getValueProps(value) {
              return { value: value?.producer_supplier_id };
            },
            labelCol: {
              span: 2,
            },
            labelAlign: 'left',
          },
        },
        {
          componentType: 'datePicker',
          tag: 'scm',
          label: '外检报告签发日期',
          id: 'valid_date',
          name: 'valid_date',
          dependencies: ['state', 'imports'],
          fieldProps: {
            placeholder: '请选择外检报告签发日期',
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请选择外检报告签发日期',
            }),
          ],
        },
        {
          componentType: 'select',
          tag: 'scm',
          label: '外检报告截止时间',
          id: 'end_date',
          name: 'end_date',
          dependencies: ['state', 'imports'],
          fieldProps: {
            placeholder: '请选择外检报告截止时间',
            options: MONTHS_OPTION,
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请选择外检报告签发日期',
            }),
          ],
        },
        {
          componentType: 'input',
          tag: 'scm',
          label: '产地',
          id: 'origin_place',
          name: 'origin_place',
          dependencies: ['supplier_item', 'state', 'imports'],
          fieldProps: {
            placeholder: '请输入',
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请输入产地',
            }),
          ],
          request: (params) => {
            return params?.supplier_item?.origin_place;
          },
          handleDefaultValue: (data: any) => {
            return data;
          },
        },
      ],
      // itemRender: ({ listDom }, listMeta) => {
      //   return listDom;
      // },
    },
  },
  //新品申请上传外检报告编辑供应商
  // @ts-ignore
  {
    tag: 'SCM',
    label: '外检报告',
    id: ScmFieldKeyMap.disabledExternalReportList,
    componentType: 'list',
    dependencies: ['state', 'producerSupplierOptions'],
    fieldProps: {
      createText: null,
      formList: [
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          itemSpan: 1,
          hidden: (obj) => {
            return obj?.food_safe_audit_state === FoodSafeType.waitAudit;
          },
          name: 'supplier_quality_record_files',
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请上传',
            }),
          ],
          dependencies: ['state', 'imports'],
          fieldProps: {
            action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
            mode: 'textButton',
            listType: 'text',
            accept: ['pdf'],
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          name: 'supplier_quality_record_files',
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请上传',
            }),
          ],
          dependencies: ['state', 'imports'],
          hidden: (obj) => {
            return obj?.food_safe_audit_state !== FoodSafeType.waitAudit;
          },
          fieldProps: {
            action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
            mode: 'look',
            listType: 'text',
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          id: 'supplier_item',
          label: '生产厂商',
          readOnly: false,
          dependencies: ['supplier_id', 'state', 'imports'],
          name: 'supplier_item',
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请选择生产厂商',
            }),
          ],
          fieldProps: (form, index) => {
            const { supplier_quality_record_data_list } =
              form.getFieldsValue(true);
            const supplierItem =
              supplier_quality_record_data_list?.[index?.dataIndex]
                ?.supplier_item;
            return {
              placeholder: '请选择',
              options: form.getFieldValue('producerSupplierOptions'),
              textAfter: supplierItem?.producer_supplier_name && (
                <XlbIcon
                  name="fuzhi"
                  className="custom-form-item-icon"
                  onClick={() => {
                    copy(supplierItem?.producer_supplier_name);
                    message.success('复制成功');
                  }}
                />
              ),
              className: 'custom-form-item',
            };
          },

          formItemProps: {
            getValueFromEvent(a, b, c) {
              return {
                producer_supplier_id: b?.value,
                producer_supplier_name: b?.label,
                origin_place: b?.origin_place,
              };
            },
            getValueProps(value) {
              return { value: value?.producer_supplier_id };
            },
          },
        },
        {
          componentType: 'datePicker',
          tag: 'scm',
          label: '外检报告签发日期',
          id: 'valid_date',
          name: 'valid_date',
          dependencies: ['state', 'imports'],
          fieldProps: {
            placeholder: '请选择外检报告签发日期',
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请选择外检报告签发日期',
            }),
          ],
        },
        {
          componentType: 'select',
          tag: 'scm',
          label: '外检报告截止时间',
          id: 'end_date',
          name: 'end_date',
          dependencies: ['state', 'imports'],
          fieldProps: {
            placeholder: '请选择外检报告截止时间',
            options: MONTHS_OPTION,
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请选择外检报告截止时间',
            }),
          ],
        },
        {
          componentType: 'input',
          tag: 'scm',
          label: '产地',
          id: 'origin_place',
          name: 'origin_place',
          dependencies: ['supplier_item', 'state', 'imports'],
          fieldProps: {
            placeholder: '请输入',
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required:
                getFieldsValue(true)?.state === CERTIFICATE_MAINTAINED.value &&
                !getFieldsValue(true)?.imports,
              message: '请输入产地',
            }),
          ],
          request: (params) => {
            return params?.supplier_item?.origin_place;
          },
          handleDefaultValue: (data: any) => {
            return data;
          },
        },
      ],
      itemRender: ({ listDom }, listMeta) => {
        return listDom;
      },
    },
  },
  //新品申请上传外检报告初审时不校验
  {
    tag: 'SCM',
    label: '上传',
    id: ScmFieldKeyMap.externalReportListNoRules,
    componentType: 'list',
    dependencies: ['state', 'producerSupplierOptions'],
    fieldProps: {
      createText: '添加',
      formList: [
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          name: 'supplier_quality_record_files',
          dependencies: ['state'],
          itemSpan: 1,
          fieldProps: {
            action: '/scm-mdm/hxl.scm.newitemapplyorder.file.upload',
            mode: 'textButton',
            listType: 'text',
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          id: 'supplier_item',
          label: '生产厂商',
          readOnly: false,
          dependencies: ['supplier_id'],
          name: 'supplier_item',
          fieldProps: (form, index) => {
            const { supplier_quality_record_data_list } =
              form.getFieldsValue(true);
            const supplierItem =
              supplier_quality_record_data_list?.[index?.dataIndex]
                ?.supplier_item;
            return {
              placeholder: '请选择',
              options: form.getFieldValue('producerSupplierOptions'),
              textAfter: supplierItem?.producer_supplier_name && (
                <XlbIcon
                  name="fuzhi"
                  className="custom-form-item-icon"
                  onClick={() => {
                    copy(supplierItem?.producer_supplier_name);
                    message.success('复制成功');
                  }}
                />
              ),
              className: 'custom-form-item',
            };
          },
          formItemProps: {
            labelCol: {
              span: 8,
            },
            labelAlign: 'right',
            getValueFromEvent(a, b, c) {
              return {
                producer_supplier_id: b?.value,
                producer_supplier_name: b?.label,
                origin_place: b?.origin_place,
              };
            },
            getValueProps(value) {
              return { value: value?.producer_supplier_id };
            },
          },
        },
        {
          componentType: 'datePicker',
          tag: 'scm',
          label: '外检报告签发日期',
          id: 'valid_date',
          name: 'valid_date',
          dependencies: ['state'],
          fieldProps: {
            placeholder: '请选择外检报告签发日期',
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          label: '外检报告截止时间',
          id: 'end_date',
          name: 'end_date',
          dependencies: ['state'],
          fieldProps: {
            placeholder: '请选择外检报告截止时间',
            options: MONTHS_OPTION,
          },
        },
        {
          componentType: 'input',
          tag: 'scm',
          label: '产地',
          id: 'origin_place',
          name: 'origin_place',
          dependencies: ['supplier_item'],
          fieldProps: {
            placeholder: '请输入',
          },
          request: (params) => {
            return params?.supplier_item?.origin_place;
          },
          handleDefaultValue: (data: any) => {
            return data;
          },
        },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '配送中心门店',
    id: ScmFieldKeyMap?.scmCenterStoreIds,
    name: 'store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {
          center_flag: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  // 免检品抽检数
  {
    tag: 'SCM',
    label: '抽检数数字输入框',
    id: ScmFieldKeyMap.scmexemptcheckInput,
    componentType: 'input',
    dependencies: ['delivery_count', 'item_id'],
    request: async (formValues: any, anybaseURL, globalFetch) => {
      if (!formValues?.delivery_count || !formValues?.item_id) return undefined;
      const res = await globalFetch.post(
        anybaseURL +
          '/scm/hxl.scm.storehouseitemspotcheck.itemspotcheckstandard.find',
        {
          delivery_count: formValues?.delivery_count,
          item_id: formValues?.item_id,
        },
      );
      if (res.code === 0) {
        return `${res?.['data']?.exempt_check_num}`;
      }
      return undefined;
    },
    handleDefaultValue: (data: any) => {
      return data;
    },
  },
  {
    tag: 'SCM',
    id: ScmFieldKeyMap?.formSpanItemLarge,
    componentType: 'upload',
    formItemProps: {
      labelCol: {
        span: 12,
      },
      labelAlign: 'right',
    },
  },

  {
    tag: 'SCM',
    label: '联系公司',
    id: ScmFieldKeyMap?.contactCompanys,
    name: 'org_id',
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.noLimit.find',
        {
          levels: [1, 2],
        },
      );
      if (res?.code === 0) {
        const newarr = res.data?.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));

        return [...newarr];
      } else {
        return [];
      }
    },
    componentType: 'select',
  },

  //  供应商组织

  {
    tag: 'SCM',
    label: '所属组织',
    id: ScmFieldKeyMap?.supplierOrgIds,
    name: 'org_ids',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      if (!userInfo?.supplier?.id) {
        return [];
      }
      const res = await globalFetch.post(
        // anybaseURL + '/erp/hxl.erp.supplier.read',
        anybaseURL + '/erp/hxl.erp.org.tree',

        {
          company_id: userInfo.company_id,
          operator_store_id: userInfo.store_id || 0,
          id: userInfo?.supplier?.id,
        },
      );
      if (res?.code === 0) {
        const newarr =
          res?.data?.map((item: any) => ({
            //
            ...item,
            label: item.org_name,
            value: item.org_id,
          })) || [];

        return [...newarr];
      } else {
        return [];
      }
    },
    componentType: 'select',
  },
  ...stockUpplanFormList,
  ...storeOrdersFormList,
];
