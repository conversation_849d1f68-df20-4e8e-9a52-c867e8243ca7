// /**
//  * 供应商审核
//  */
// export const STATE_TYPE = [
//     { label: '制单', value: 'INIT', color: 'info' },
//     { label: '待确认', value: 'TOBECONFIRMED', color: 'default' },
//     { label: '审核中', value: 'AUDIT', color: 'warning' },
//     { label: '已结束', value: 'PASS', color: 'success' },
//     { label: '已作废', value: 'INVALID', color: 'danger' },
//   ];
//   export const AUDIT_TYPE = [
//     { label: '新供应商准入审核', value: 'NEWSUPPLIERADMISSION' },
//     { label: '供应商日常审核管理', value: 'SUPPLIERDAILYAUDITMANAGEMENT' },
//   ];
//   export const AUDIT_RESULT = [
//     { label: '优秀', value: 'EXCELLENT' },
//     { label: '良好', value: 'GOOD' },
//     { label: '及格', value: 'PASS' },
//     { label: '不合格', value: 'UNQUALIFIED' },
//   ];
//   export const FREQUENCY_TYPE = [
//     {
//       value: 'TRENDS',
//       label: '动态',
//     },
//     {
//       value: 'DAILY',
//       label: '每日',
//     },
//   ];
export const ITEM_TYPE = [
  { label: '按比例', value: 0 },
  { label: '按金额', value: 1 },
  { label: '固定金额', value: 2 },
];

export const MONTHS_OPTION = [
  {
    label: '半年',
    value: 6,
  },
  {
    label: '1年',
    value: 12,
  },
  {
    label: '2年',
    value: 24,
  },
  {
    label: '3年',
    value: 36,
  },
];

export enum executiveStandardType {
  NATIONAL_STANDARDS = 'NATIONAL_STANDARDS', // 国标
  COMPANY_STANDARDS = 'COMPANY_STANDARDS', // 企标
}
export const EXECUTIVE_STANDARD_LIST = [
  {
    label: '是',
    value: executiveStandardType.COMPANY_STANDARDS,
  },
  {
    label: '否',
    value: executiveStandardType.NATIONAL_STANDARDS,
  },
];
