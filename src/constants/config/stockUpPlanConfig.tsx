import { LStorage } from '@/utils/storage';
import { FormInstance } from '@ant-design/pro-components';
import { CreateMapItem, XlbTipsModal } from '@xlb/components';

const userInfo = LStorage.get('userInfo');

export const FieldKeyMap = {
  selectStoreIds: 'selectStoreIds',
  stockUpPlanOrgIds: 'stockUpPlanOrgIds',
  spotCheckOrderDetailSum: 'spotCheckOrderDetailSum',
  cargoOwner: 'cargoOwner',
};
export const stockUpplanFormList: Array<CreateMapItem> = [
  // 备货计划
  {
    tag: 'SCM',
    label: '所属组织',
    id: FieldKeyMap?.stockUpPlanOrgIds,
    name: 'org_ids',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      return userInfo?.query_orgs
        ? userInfo?.query_orgs
            ?.filter((v: any) => v.level === 2)
            ?.map((item: any) => ({
              ...item,
              label: item.name,
              value: item.id,
            }))
        : [];
    },
    componentType: 'select',
  },
  {
    tag: 'SCM',
    label: '所属门店',
    id: FieldKeyMap?.selectStoreIds,
    name: 'store_ids',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.store.short.page',
        {
          center_flag: true,
        },
      );
      if (res?.code === 0) {
        return res?.data?.content?.map((item: any) => ({
          label: item.store_name,
          value: item.id,
        }));
      }
    },
    componentType: 'select',
  },
  {
    tag: 'SCM',
    componentType: 'select',
    label: '汇总条件',
    id: FieldKeyMap.spotCheckOrderDetailSum,
    formItemProps: (form: FormInstance) => {
      return {
        label: '汇总条件',
        id: 'commonSelect',
        name: 'summary_type',
        shouldUpdate: true,
        normalize(value) {
          if (
            !value?.includes('ABNORMAL') &&
            value?.includes('ABNORMAL_Level_TWO')
          ) {
            XlbTipsModal({
              tips: '请先选择异常类别',
            });
            return value?.filter((v: string) => v !== 'ABNORMAL_Level_TWO');
          }
          return value;
        },
      };
    },
  },
  {
    tag: 'SCM',
    label: '所属货主',
    id: FieldKeyMap.cargoOwner,
    componentType: 'inputDialog',
  },
];
