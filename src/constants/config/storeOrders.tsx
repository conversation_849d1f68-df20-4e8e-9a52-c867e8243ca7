import { LStorage } from '@/utils/storage';
import { CreateMapItem, XlbInputDialogProps } from '@xlb/components';

const userInfo = LStorage.get('userInfo');

export const storeOrdersFieldKeyMap = {
  scmDocumentNumber: 'scmDocumentNumber',
  scmBillState: 'scmBillState',
  scmTimeType: 'scmTimeType',
  scmDeliveryType: 'scmDeliveryType',
  scmDownstreamDocuments: 'scmDownstreamDocuments',
  scmOutOrgIds: 'scmOutOrgIds',
  billStoreCenterId: 'billStoreCenterId',
  billStoreCenterIds: 'billStoreCenterIds',
  outOrgIds: 'outOrgIds',
  outStoreId: 'outStoreId',
  outStoreName: 'outStoreName',
  outStoreIds: 'outStoreIds',
  outStorehouseName: 'outStorehouseName',
  scmStoreStorehouseId: 'scmStoreStorehouseId',
  scmSupplierIds: 'scmSupplierIds',
  scmStoreOrderItemIds: 'scmStoreOrderItemIds',
  scmCreateBy: 'scmCreateBy',
  scmAuditBy: 'scmAuditBy',
  scmCommitBy: 'scmCommitBy',
  storeSupplierId: 'storeSupplierId',
  storeSupplierIds: 'storeSupplierIds',
  itemDeptNames: 'itemDeptNames',
  deliveryDate: 'deliveryDate',
  validDate: 'validDate',
  scmMemo: 'scmMemo',
  scmStoreStoreCommonUpload: 'scmStoreStoreCommonUpload',
};
export const storeOrdersFormList: Array<CreateMapItem> = [
  {
    tag: 'SCM',
    label: '单据号',
    id: storeOrdersFieldKeyMap.scmDocumentNumber,
    name: 'fid',
    formItemProps: {
      rules: [{ max: 20 }],
    },
    componentType: 'input',
  },
  {
    tag: 'SCM',
    label: '时间类型',
    id: storeOrdersFieldKeyMap.scmTimeType,
    name: 'time_type',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '制单时间', value: 'create_date' },
        { label: '审核时间', value: 'audit_date' },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '单据状态',
    id: storeOrdersFieldKeyMap.scmBillState,
    name: 'state',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '制单', value: 'INIT', type: 'info' },
        { label: '审核', value: 'AUDIT', type: 'warning' },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '配送类型',
    id: storeOrdersFieldKeyMap.scmDeliveryType,
    name: 'delivery_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '直营',
          value: 'DIRECT',
        },
        {
          label: '加盟',
          value: 'JOIN',
        },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '下游单据',
    id: storeOrdersFieldKeyMap.scmDownstreamDocuments,
    name: 'ref_order_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '采购订单',
          value: 'PURCHASE',
        },
      ],
    },
  },
  {
    tag: 'SCM',
    label: '发货组织',
    name: 'org_ids',
    id: storeOrdersFieldKeyMap?.scmOutOrgIds,
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
    },
    onChange: (ids, form) => {
      form?.setFieldValue('store_id', []);
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value;
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '应用门店',
    id: storeOrdersFieldKeyMap.billStoreCenterIds,
    name: 'store_id',
    dependencies: ['supplier_ids'],
    fieldProps(formValue) {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          url: '/erp/hxl.erp.storeorder.store.shortfind',
          data: {
            supplier_ids: formValue.getFieldValue('supplier_ids') || [],
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
      ) => {
        return event ? event[0] : undefined;
      },
      getValueProps: (value: any) => {
        return { value: value ? [value] : undefined };
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '应用门店',
    id: storeOrdersFieldKeyMap.billStoreCenterId,
    name: 'store_id',
    dependencies: ['supplier_id'],
    fieldProps(formValue) {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          url: '/erp/hxl.erp.storeorder.store.shortfind',
          data: {
            supplier_id: formValue.getFieldValue('supplier_id') || '',
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
      ) => {
        return event ? event[0] : undefined;
      },
      getValueProps: (value: any) => {
        return { value: value ? [value] : undefined };
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '调出组织',
    id: storeOrdersFieldKeyMap.outOrgIds,
    name: 'out_org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {},
      );
      if (res?.code === 0) {
        return res.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
    onChange: (e: string[], _: any, form: any) => {
      if (e.length > 0) {
        _.setFieldsValue({
          out_store_ids: null,
        });
      }
    },
  },
  {
    tag: 'SCM',
    label: '应用门店',
    id: storeOrdersFieldKeyMap.outStoreId,
    name: 'out_store_id',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: false,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
      ) => {
        return event ? event[0] : undefined;
      },
      getValueProps: (value: any) => {
        return { value: value ? [value] : undefined };
      },
    },
    componentType: 'inputDialog',
  },

  {
    tag: 'SCM',
    label: '调出仓库',
    id: storeOrdersFieldKeyMap?.outStoreName,
    name: 'out_store_name',
    componentType: 'input',
  },

  {
    tag: 'SCM',
    label: '应用门店',
    id: storeOrdersFieldKeyMap.outStoreIds,
    name: 'out_store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    // formItemProps: {
    //   getValueFromEvent: (
    //     event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0],
    //   ) => {
    //     return event ? event[0] : undefined;
    //   },
    //   getValueProps: (value: any) => {
    //     return { value: value ? [value] : undefined };
    //   },
    // },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '调出仓库',
    id: storeOrdersFieldKeyMap?.outStorehouseName,
    name: 'out_storehouse_name',
    componentType: 'input',
  },

  {
    tag: 'SCM',
    label: '仓库',
    id: storeOrdersFieldKeyMap?.scmStoreStorehouseId,
    name: 'storehouse_id',
    dependencies: ['store_id'],
    async request(params: any, anybaseURL: any, globalFetch: any) {
      const { store_id } = params;
      if (!store_id) {
        return [];
      }
      const result = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.storehouse.store.find`,
        {
          store_id,
        },
      );
      if (Array.isArray(result?.data)) {
        return result.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
    // showSearch:true,
    // handleDefaultValue(data: any) {
    //   const defaultStoreHouse =
    //     data.find((item: any) => item.default_flag) || data[0];
    //   return defaultStoreHouse?.value;
    // },
    componentType: 'select',
  },
  {
    tag: 'SCM',
    label: '供应商',
    id: storeOrdersFieldKeyMap.scmSupplierIds,
    name: 'supplier_ids',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },
    onChange: (e: string[], _: any, formValue: any) => {
      console.log(e, _, formValue, 'onnnnnn');
      if (e.length > 0) {
        _.setFieldsValue({
          supplier_ids_name: formValue?.map((m: any) => m.name).join(','),
        });
      }
    },
    componentType: 'inputDialog',

    dependencies: ['summary_types'],
  },
  {
    tag: 'SCM',
    label: '商品档案',
    id: storeOrdersFieldKeyMap?.scmStoreOrderItemIds,
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
        // isLeftColumn: false,
      },
    },
    componentType: 'inputDialog',
    dependencies: ['summary_types'],
  },
  {
    tag: 'SCM',
    componentType: 'input',
    label: '制单人',
    name: 'create_by',
    id: storeOrdersFieldKeyMap?.scmCreateBy,
  },
  {
    tag: 'SCM',
    componentType: 'input',
    label: '审核人',
    name: 'audit_by',
    id: storeOrdersFieldKeyMap?.scmAuditBy,
  },
  {
    tag: 'SCM',
    componentType: 'input',
    label: '确认人',
    name: 'confirm_by',
    id: storeOrdersFieldKeyMap?.scmCommitBy,
  },

  {
    tag: 'SCM',
    label: '供应商',
    id: storeOrdersFieldKeyMap?.storeSupplierId,
    name: 'supplier_id',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isMultiple: false,
      },
    },
    dependencies: ['type'],
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    label: '供应商',
    id: storeOrdersFieldKeyMap?.storeSupplierIds,
    name: 'supplier_ids',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isMultiple: true,
      },
    },
    dependencies: ['type'],
    componentType: 'inputDialog',
  },
  {
    tag: 'SCM',
    componentType: 'input',
    label: '商品部门',
    name: 'item_dept_names',
    id: storeOrdersFieldKeyMap?.itemDeptNames,
  },
  {
    tag: 'SCM',
    label: '配送日期',
    id: storeOrdersFieldKeyMap?.deliveryDate,
    name: 'delivery_date',
    componentType: 'datePicker',
  },
  {
    tag: 'SCM',
    label: '有效日期',
    id: storeOrdersFieldKeyMap?.validDate,
    name: 'valid_date',
    componentType: 'datePicker',
  },
  {
    tag: 'SCM',
    label: '备注',
    id: storeOrdersFieldKeyMap.scmMemo,
    name: 'memo',
    componentType: 'textArea',
  },
  {
    componentType: 'upload',
    tag: 'SCM',
    id: storeOrdersFieldKeyMap.scmStoreStoreCommonUpload,
    dependencies: ['fid'],
    formItemProps: {
      rules: [{ required: true, message: '请上传文件' }],
    },
  },
];
