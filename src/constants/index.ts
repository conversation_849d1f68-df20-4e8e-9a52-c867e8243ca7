import { XlbTableColumnProps } from '@xlb/components';

export const tableColStyle = {
  orderNoWidth: 60, // 序号列宽
  timeWidth: 150, // 时间宽度
  phoneWidth: 140, // 手机号宽度
  stateWidth: 90, // 状态宽度
  storeWidth:172,//门店
  singleWidth:88,//单个
  scoreWidth:102, //分数、比例
};

export const formItemStyle = {
  formWidth: 160, // 表单宽度
  twoWidth: 440, // 两列的宽度
};

export const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

export const addModalStyle = {
  formItemWidth: 264,
  modalWidth: 500,
  viewModalWidth: 624,
};

export const numberColumn = () => {
  let col: XlbTableColumnProps<any> = {
    name: '序号',
    code: '_index',
    width: tableColStyle.orderNoWidth,
    lock: true,
    align: 'center',
  };
  return col;
};
