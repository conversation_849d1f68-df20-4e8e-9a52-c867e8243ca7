import { LStorage } from '@/utils/storage';
import React from 'react';

// 模块按钮权限判断
export const hasAuth = (keys: string[] | number, appType?: string) => {
  let SYSTEM_NAME = appType ?? 'SCM';
  const user = LStorage.get('userInfo');
  if (!keys || !user) return false;
  let auths = user.authorities;
  if (!auths || auths.length === 0) return false;
  /* 对于重复菜单的权限控制通过权限 id */
  if (typeof keys === 'number' && auths.some((auth) => auth.id === keys)) {
    return true;
  }
  if (
    auths.some(
      (v) =>
        v.path == keys[0] && v.action == keys[1] && v.app_type == SYSTEM_NAME,
    )
  ) {
    return true;
  }
  return false;
};

// 老系统菜单权限判断
export const hasOldAuth = (keys: any[]) => {
  if (keys.length === 0 && keys[0]) return true;
  let isAuth = LStorage.get('old_userInfo')?.permission;
  if (isAuth) {
    keys.map((v) => (isAuth = isAuth ? isAuth[v] : false));
  } else {
    isAuth = false;
  }
  return !!isAuth;
};

export const getParams = (url: string) => {
  if (!url.includes('?')) {
    return {};
  }
  let str = url.split('?')[1]; //?号后面的就是我们需要的name=quan&age=21&sex=1
  let arr = str.split('&'); //["name=quan", "age=21", "sex=1"]
  let obj = {};
  for (let i = 0; i < arr.length; i++) {
    let key = arr[i].split('='); //["name", "quan"] ["age", "21"] ["sex", "1"]
    obj[key[0]] = key[1];
  }
  return obj;
};

/**
 * @name 遍历vnode 找到文本为止
 *
 */
export const findVnodeText = (vnode: React.ReactNode): string | number => {
  let text: string | number;
  if (React.isValidElement(vnode)) {
    const children = vnode.props.children;
    if (React.isValidElement(children)) {
      text = findVnodeText(children);
    } else {
      text = children;
    }
  } else if (typeof vnode === 'string') {
    text = vnode;
  } else if (typeof vnode === 'number') {
    text = vnode;
  } else {
    text = '';
  }
  return text;
};

// 遍历对象换键名
// {id:'公司'}
// {id:'xxx'}=>{公司:'xxx'}
export const renameKeys = (obj: any, keyMap: any) => {
  return Object.keys(obj).reduce((acc, key) => {
    const newKey = keyMap[key];
    if (newKey) {
      acc[newKey] = obj[key];
    }
    return acc;
  }, {});
};
