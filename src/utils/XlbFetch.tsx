import { XlbFetch } from '@xlb/utils';

const fetchData = async <T = any, K = any>(url: string, data?: any) => {
  const requestData = {
    page_number: 0,
    page_size: 200,
    ...data,
  };

  const filteredObj = Object.fromEntries(
    Object.entries({ ...requestData }).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        acc.push([key, value]);
      }
      return acc;
    }, []),
  );

  return await XlbFetch.post<T, HttpResponse<K>>(
    `${process.env.BASE_URL}${url}`,
    filteredObj,
  );
};
export default fetchData;
