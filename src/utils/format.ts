// 示例方法，没有实际意义
export function trim(str: string) {
  return str.trim();
}

// 转换查询的state
export const transferState = (values: any, handleKey: string) => {
  const state = values[handleKey];
  if (state?.length > 1 || !state?.length) {
    values[handleKey] = null;
  } else if (state?.length === 1) {
    values[handleKey] = values[handleKey][0];
  }

  return values;
};
// 转换时间
export const dateStrSlice = (dateStr: string) => {
  // 提取开始时间和结束时间
  if (!dateStr) return;
  if (!dateStr.includes('年')) return null;
  const startDate = dateStr
    .slice(0, 10)
    .replace('年', '-')
    .replace('月', '-')
    .replace('日', '');
  const endDate = dateStr.slice(13).includes('年')
    ? dateStr.slice(12).replace('年', '-').replace('月', '-').replace('日', '')
    : null;
  return {
    start_date: startDate,
    end_date: endDate,
  };
};
