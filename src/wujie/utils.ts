import { history } from '@umijs/max';
import { name } from '../../package.json';

// 子应用名称
export const appName = name;
export const auth = 'SCM';

const appUrlPrefix = `/${appName}`;

declare global {
  interface Window {
    [key: string]: any;
  }
}
export const useIRouter = () => {
  /**
   * 跳转
   * @param params
   *        url = string 时是直接跳转的地址
   *            = object 时是跳转携带的参数
   */
  const navigate = (path: any, state?: any) => {
    if (isWUJIE()) {
      window.$wujie?.bus.$emit('wujie-router-jump', {
        url: path,
        params: state,
        appType: appName,
      });
    } else {
      history.push(path, state);
    }
  };

  /**
   * 回退
   */
  const goBack = () => {
    // @ts-ignore
    history.go(-1);

    // masterProps?.childAction?.({
    //   appName,
    //   eventType: EVENT_TYPE.GOBACK,
    //   payload: { path: path },
    // });
  };

  return {
    navigate,
    goBack,
  };
};

export const replaceUrlAppName = (path: string) => {
  return path.replace(appUrlPrefix, '');
};

export const isWUJIE = () => {
  return window.__POWERED_BY_WUJIE__;
};

// 是否是乾坤环境
export const isQkEnvironment = () => {
  // @ts-ignore
  return !!window.__POWERED_BY_QIANKUN__;
};

export const isNoQkEnvironment = () => {
  // @ts-ignore
  return !window.__POWERED_BY_QIANKUN__;
};
