// @ts-ignore
/* eslint-disable */
// import { request } from '@umijs/max';
import { XlbFetch } from '@xlb/utils';
/** 查询 POST /tms/hxl.wms.storehouse.default.find */
export async function accountLoginUsingPost(
  body: any,
  options?: { [key: string]: any },
) {
  return XlbFetch('/erp/hxl.erp.user.account.login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function exportPage(body: any, options?: { [key: string]: any }) {
  return XlbFetch('/export/hxl.export.reporthistory.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
