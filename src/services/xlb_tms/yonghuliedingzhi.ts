// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 用户自定义列查询 POST /tms/hxl.tms.usercolumn.get */
export async function getUserColumnUsingPost(
  body: API.UserColumnFindReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseUserColumnResDTO>('/tms/hxl.tms.usercolumn.get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户自定义列更新 POST /tms/hxl.tms.usercolumn.update */
export async function updateUserColumnUsingPost(
  body: API.UserColumnUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseUserColumnResDTO>('/tms/hxl.tms.usercolumn.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
