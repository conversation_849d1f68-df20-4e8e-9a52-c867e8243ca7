// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.car.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.car.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出 POST /tms/hxl.tms.car.export */
export async function exportUsingPost(body: API.CarPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponsestring>('/tms/hxl.tms.car.export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.car.page */
export async function pageUsingPost(body: API.CarPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTOCarResDTO>('/tms/hxl.tms.car.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /tms/hxl.tms.car.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseCarResDTO>('/tms/hxl.tms.car.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.car.save */
export async function saveUsingPost(body: API.CarSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.car.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.car.update */
export async function updateUsingPost(body: API.CarUpdateReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.car.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
