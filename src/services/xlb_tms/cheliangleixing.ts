// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.cartype.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.cartype.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出 POST /tms/hxl.tms.cartype.export */
export async function exportUsingPost(
  body: API.CarTypePageReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponsestring>('/tms/hxl.tms.cartype.export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.cartype.page */
export async function pageUsingPost(body: API.CarTypePageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTOCarTypeResDTO>('/tms/hxl.tms.cartype.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /tms/hxl.tms.cartype.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseCarTypeResDTO>('/tms/hxl.tms.cartype.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.cartype.save */
export async function saveUsingPost(body: API.CarTypeSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.cartype.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.cartype.update */
export async function updateUsingPost(
  body: API.CarTypeUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.cartype.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
