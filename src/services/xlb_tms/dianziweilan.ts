// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.electron.fence.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.electron.fence.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.electron.fence.page */
export async function pageUsingPost(
  body: API.ElectronFencePageReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseBasePageResDTOElectronFenceResDTO>(
    '/tms/hxl.tms.electron.fence.page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 查询 POST /tms/hxl.tms.electron.fence.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseElectronFenceResDTO>('/tms/hxl.tms.electron.fence.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.electron.fence.save */
export async function saveUsingPost(
  body: API.ElectronFenceSaveReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.electron.fence.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.electron.fence.update */
export async function updateUsingPost(
  body: API.ElectronFenceUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.electron.fence.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
