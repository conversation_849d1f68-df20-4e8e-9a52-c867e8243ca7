// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 分页查询 POST /tms/hxl.tms.transportorder.page */
export async function pageUsingPost(
  body: API.TransportOrderPageReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseBasePageResDTOTransportOrderResDTO>(
    '/tms/hxl.tms.transportorder.page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 新增 POST /tms/hxl.tms.transportorder.save */
export async function saveUsingPost(
  body: API.TransportOrderSaveReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.transportorder.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
