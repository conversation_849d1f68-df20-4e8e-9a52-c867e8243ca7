// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.carrier.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.carrier.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出 POST /tms/hxl.tms.carrier.export */
export async function exportUsingPost(
  body: API.CarrierPageReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponsestring>('/tms/hxl.tms.carrier.export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 附件上传 POST /tms/hxl.tms.carrier.file.upload */
export async function fileUploadUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fileUploadUsingPOSTParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseFileResDTO>('/tms/hxl.tms.carrier.file.upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** ocr识别 POST /tms/hxl.tms.carrier.file.upload.ocr */
export async function fileUploadOcrUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fileUploadOcrUsingPOSTParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseOcrResultDTO>('/tms/hxl.tms.carrier.file.upload.ocr', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.carrier.page */
export async function pageUsingPost(body: API.CarrierPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTOCarrierResDTO>('/tms/hxl.tms.carrier.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /tms/hxl.tms.carrier.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseCarrierResDTO>('/tms/hxl.tms.carrier.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.carrier.save */
export async function saveUsingPost(body: API.CarrierSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.carrier.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.carrier.update */
export async function updateUsingPost(
  body: API.CarrierUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseCarrierResDTO>('/tms/hxl.tms.carrier.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
