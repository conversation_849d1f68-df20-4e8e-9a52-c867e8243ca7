declare namespace API {
  type AccountInfoReqDTO = {
    /** 开户银行 */
    bank_name?: string;
    /** 银行卡号 */
    card_code?: string;
    /** 银行卡持有人 */
    card_holder?: string;
    /** id */
    id?: number;
    /** 结算方式：REACH(货到付款),MONTH(月结) */
    payment_type?: 'MONTH' | 'REACH';
  };

  type AccountInfoResDTO = {
    /** 开户银行 */
    bank_name?: string;
    /** 银行卡号 */
    card_code?: string;
    /** 银行卡持有人 */
    card_holder?: string;
    /** 商户号 */
    company_id?: number;
    /** id */
    id?: number;
    /** 结算方式：REACH(货到付款),MONTH(月结) */
    payment_type?: 'MONTH' | 'REACH';
  };

  type AuthorityAssembleResDTO = {
    /** 权限明细 */
    authority_details?: AuthorityDetailResDTO[];
    /** 名称 */
    name?: string;
    /** 父名称 */
    parent_name?: string;
    /** 顺序 */
    sequence?: string;
  };

  type AuthorityCategoryResDTO = {
    /** 操作列表 */
    actions?: string[];
    /** 权限 */
    authorities?: AuthorityAssembleResDTO[];
    /** 类别名称 */
    category_name?: string;
    /** 类别路径 */
    category_path?: string;
    /** 父类别路径 */
    parent_category_path?: string;
  };

  type AuthorityDetailResDTO = {
    /** 操作 */
    action?: string;
    /** id */
    id?: number;
  };

  type AuthorityResDTO = {
    /** 操作 */
    action?: string;
    /** app */
    app_type?: string;
    /** id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 路径 */
    path?: string;
    /** 顺序 */
    sequence?: string;
  };

  type AuthoritySearchReqDTO = {
    /** 关键词 */
    keyword?: string;
  };

  type AuthoritySearchResDTO = {
    /** 动作 */
    action?: string;
    /** 应用 */
    app_type?: string;
    /** id */
    id?: number;
    /** 权限名称 */
    name?: string;
  };

  type BankCardDTO = {
    /** 银行名称 */
    bank_name?: string;
    /** 银行卡号 */
    card_number?: string;
    /** 卡种（CC（贷记卡），SCC（准贷记卡），DCC（存贷合一卡），DC（储蓄卡），PC（预付卡）） */
    card_type?: 'CC' | 'DC' | 'DCC' | 'PC' | 'SCC';
    /** 有效期限 */
    valid_to_date?: string;
  };

  type BasePageResDTOCarResDTO = {
    /** 明细 */
    content?: CarResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTOCarrierResDTO = {
    /** 明细 */
    content?: CarrierResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTOCarTypeResDTO = {
    /** 明细 */
    content?: CarTypeResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTOCompanyResDTO = {
    /** 明细 */
    content?: CompanyResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTODriverResDTO = {
    /** 明细 */
    content?: DriverResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTOElectronFenceResDTO = {
    /** 明细 */
    content?: ElectronFenceResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTORoleResDTO = {
    /** 明细 */
    content?: RoleResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTORouteResDTO = {
    /** 明细 */
    content?: RouteResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTOTransportOrderResDTO = {
    /** 明细 */
    content?: TransportOrderResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BasePageResDTOUserResDTO = {
    /** 明细 */
    content?: UserResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总页数 */
    total_pages?: number;
  };

  type BaseReqDTO = true;

  type BaseResponseBasePageResDTOCarResDTO = {
    code?: number;
    data?: BasePageResDTOCarResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTOCarrierResDTO = {
    code?: number;
    data?: BasePageResDTOCarrierResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTOCarTypeResDTO = {
    code?: number;
    data?: BasePageResDTOCarTypeResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTOCompanyResDTO = {
    code?: number;
    data?: BasePageResDTOCompanyResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTODriverResDTO = {
    code?: number;
    data?: BasePageResDTODriverResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTOElectronFenceResDTO = {
    code?: number;
    data?: BasePageResDTOElectronFenceResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTORoleResDTO = {
    code?: number;
    data?: BasePageResDTORoleResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTORouteResDTO = {
    code?: number;
    data?: BasePageResDTORouteResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTOTransportOrderResDTO = {
    code?: number;
    data?: BasePageResDTOTransportOrderResDTO;
    msg?: string;
  };

  type BaseResponseBasePageResDTOUserResDTO = {
    code?: number;
    data?: BasePageResDTOUserResDTO;
    msg?: string;
  };

  type BaseResponseCarResDTO = {
    code?: number;
    data?: CarResDTO;
    msg?: string;
  };

  type BaseResponseCarrierResDTO = {
    code?: number;
    data?: CarrierResDTO;
    msg?: string;
  };

  type BaseResponseCarTypeResDTO = {
    code?: number;
    data?: CarTypeResDTO;
    msg?: string;
  };

  type BaseResponseDeleteResultResDTO = {
    code?: number;
    data?: DeleteResultResDTO;
    msg?: string;
  };

  type BaseResponseDriverResDTO = {
    code?: number;
    data?: DriverResDTO;
    msg?: string;
  };

  type BaseResponseElectronFenceResDTO = {
    code?: number;
    data?: ElectronFenceResDTO;
    msg?: string;
  };

  type BaseResponseFileResDTO = {
    code?: number;
    data?: FileResDTO;
    msg?: string;
  };

  type BaseResponseListAuthorityCategoryResDTO = {
    code?: number;
    data?: AuthorityCategoryResDTO[];
    msg?: string;
  };

  type BaseResponseListAuthoritySearchResDTO = {
    code?: number;
    data?: AuthoritySearchResDTO[];
    msg?: string;
  };

  type BaseResponseListmendianxinxibiao = {
    code?: number;
    data?: mendianxinxibiao[];
    msg?: string;
  };

  type BaseResponseListRoleCategoryResDTO = {
    code?: number;
    data?: RoleCategoryResDTO[];
    msg?: string;
  };

  type BaseResponseListStorehouseResDTO = {
    code?: number;
    data?: StorehouseResDTO[];
    msg?: string;
  };

  type BaseResponseobject = {
    code?: number;
    data?: Record<string, any>;
    msg?: string;
  };

  type BaseResponseOcrResultDTO = {
    code?: number;
    data?: OcrResultDTO;
    msg?: string;
  };

  type BaseResponseOrderHeaderCountBasePageResDTOOrderHeaderResDTO = {
    code?: number;
    data?: OrderHeaderCountBasePageResDTOOrderHeaderResDTO;
    msg?: string;
  };

  type BaseResponseOrderHeaderSumResDTO = {
    code?: number;
    data?: OrderHeaderSumResDTO;
    msg?: string;
  };

  type BaseResponseRoleResDTO = {
    code?: number;
    data?: RoleResDTO;
    msg?: string;
  };

  type BaseResponseRouteResDTO = {
    code?: number;
    data?: RouteResDTO;
    msg?: string;
  };

  type BaseResponsestring = {
    code?: number;
    data?: string;
    msg?: string;
  };

  type BaseResponseUserColumnResDTO = {
    code?: number;
    data?: UserColumnResDTO;
    msg?: string;
  };

  type BaseResponseUserResDTO = {
    code?: number;
    data?: UserResDTO;
    msg?: string;
  };

  type BusinessLicense = {
    /** 企业名称 */
    business_name?: string;
    /** 信用代码 */
    credit_code?: string;
    /** 失效时间 */
    expire_time?: string;
    /** 法人姓名 */
    leader_name?: string;
    /** 文件链接 */
    url?: string;
  };

  type BusinessLicenseDTO = {
    /** 营业场所/住所 */
    business_address?: string;
    /** 经营范围 */
    business_scope?: string;
    /** 营业名称 */
    company_name?: string;
    /** 类型 */
    company_type?: string;
    /** 统一社会信用代码 */
    credit_code?: string;
    /** 法人 */
    legal_person?: string;
    /** 注册资本 */
    registered_capital?: string;
    /** 注册日期 */
    registration_date?: string;
    /** 格式化营业期限起始日期 */
    valid_from_date?: string;
    /** 营业期限 */
    valid_period?: string;
    /** 格式化营业期限终止日期 */
    valid_to_date?: string;
  };

  type CarPageReqDTO = {
    /** 车辆类型Id */
    car_type_id?: number;
    /** 主驾驶员Id */
    driver_id?: number;
    /** 能源类型 */
    energy_type?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 车牌号 */
    plate_number?: string;
    /** 车辆状态 */
    status?: boolean;
    /** 主驾驶员手机号 */
    tel?: string;
  };

  type CarResDTO = {
    /** 车辆品牌 */
    car_brand?: string;
    /** 车辆类型 */
    car_type?: CarType;
    /** 车辆类型id */
    car_type_id?: number;
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 主驾驶员id */
    driver_id?: number;
    /** 司机姓名 */
    driver_name?: string;
    /** 司机联系电话 */
    driver_tel?: string;
    /** 能源类型 */
    energy_type?: string;
    /** id */
    id?: number;
    /** 备注 */
    memo?: string;
    /** 车牌号 */
    plate_number?: string;
    /** 车辆状态 */
    status?: boolean;
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type CarrierPageReqDTO = {
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type?: 'CARRIER' | 'DRIVER';
    /** 联系人 */
    contact?: string;
    /** 名称 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state?: 'DISABLE' | 'ENABLE';
    /** 所属仓库 */
    storehouse_ids?: number[];
    /** 承运商类型：SYSTEM(自有),TEMP(临时),EXTERNAL(第三方) */
    type?: 'EXTERNAL' | 'SYSTEM' | 'TEMP';
  };

  type CarrierResDTO = {
    /** 财务信息 */
    account_info?: AccountInfoResDTO;
    /** 财务信息主键 */
    account_info_id?: number;
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type: 'CARRIER' | 'DRIVER';
    /** 联系地址 */
    address?: string;
    /** 公司id */
    company_id?: number;
    /** 联系人 */
    contact: string;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 联系邮箱 */
    email?: string;
    /** 承运商id */
    id: number;
    /** 备注 */
    memo?: string;
    /** 承运商名称 */
    name: string;
    /** 联系人电话 */
    phone: string;
    /** 资质证件信息 */
    qualification_documents?: QualificationDocumentsResDTO;
    /** 资质证件id */
    qualification_documents_id?: number;
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 所属仓库 */
    storehouse_ids: number[];
    /** 所属仓库 */
    storehouse_name: string;
    /** 承运商类型：SYSTEM(自有),TEMP(临时),EXTERNAL(第三方) */
    type: 'EXTERNAL' | 'SYSTEM' | 'TEMP';
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type CarrierSaveReqDTO = {
    /** 财务信息 */
    account_info_req?: AccountInfoReqDTO;
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type: 'CARRIER' | 'DRIVER';
    /** 联系地址 */
    address?: string;
    /** 联系人 */
    contact: string;
    /** 联系邮箱 */
    email?: string;
    /** 备注 */
    memo?: string;
    /** 承运商名称 */
    name: string;
    /** 联系人电话 */
    phone: string;
    /** 资质证件 */
    qualification_documents_req?: QualificationDocumentsReqDTO;
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 所属仓库ids */
    storehouse_ids?: number[];
    /** 承运商类型：SYSTEM(自有),TEMP(临时),EXTERNAL(第三方) */
    type: 'EXTERNAL' | 'SYSTEM' | 'TEMP';
  };

  type CarrierUpdateReqDTO = {
    /** 财务信息 */
    account_info_req?: AccountInfoReqDTO;
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type: 'CARRIER' | 'DRIVER';
    /** 联系地址 */
    address?: string;
    /** 联系人 */
    contact: string;
    /** 联系邮箱 */
    email?: string;
    /** id */
    id?: number;
    /** 备注 */
    memo?: string;
    /** 承运商名称 */
    name: string;
    /** 联系人电话 */
    phone: string;
    /** 资质证件 */
    qualification_documents_req?: QualificationDocumentsReqDTO;
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 所属仓库ids */
    storehouse_ids?: number[];
    /** 承运商类型：SYSTEM(自有),TEMP(临时),EXTERNAL(第三方) */
    type: 'EXTERNAL' | 'SYSTEM' | 'TEMP';
  };

  type CarSaveReqDTO = {
    /** 车辆品牌 */
    car_brand?: string;
    /** 车辆类型id */
    car_type_id: number;
    /** 主驾驶员id */
    driver_id: number;
    /** 能源类型 */
    energy_type?: string;
    /** 备注 */
    memo?: string;
    /** 车牌号 */
    plate_number: string;
    /** 车辆状态（启用：true,禁用：false） */
    status: boolean;
  };

  type CarType = {
    company_id?: number;
    create_by?: string;
    create_time?: string;
    deleted?: boolean;
    height?: number;
    id?: number;
    length?: number;
    max_volume?: number;
    name?: string;
    ratio?: number;
    status?: boolean;
    update_by?: string;
    update_time?: string;
    volume?: number;
    weight?: number;
    width?: number;
  };

  type CarTypePageReqDTO = {
    /** 车辆类型 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 车辆类型状态 */
    status?: boolean;
  };

  type CarTypeResDTO = {
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 车身高度 */
    height?: number;
    /** id */
    id?: number;
    /** 车身长度 */
    length?: number;
    /** 最大装载体积 */
    max_volume?: number;
    /** 车辆类型 */
    name?: string;
    /** 装载系数 */
    ratio?: number;
    /** 车辆类型状态 */
    status?: boolean;
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
    /** 装载体积 */
    volume?: number;
    /** 装载重量 */
    weight?: number;
    /** 车身宽度 */
    width?: number;
  };

  type CarTypeSaveReqDTO = {
    /** 车身高度 */
    height: number;
    /** 车身长度 */
    length: number;
    /** 最大装载体积 */
    max_volume: number;
    /** 车辆类型 */
    name: string;
    /** 装载系数 */
    ratio: number;
    /** 车辆类型状态（启用：true,禁用：false） */
    status: boolean;
    /** 装载体积 */
    volume: number;
    /** 装载重量 */
    weight: number;
    /** 车身宽度 */
    width: number;
  };

  type CarTypeUpdateReqDTO = {
    /** 车身高度 */
    height: number;
    /** id */
    id: number;
    /** 车身长度 */
    length: number;
    /** 最大装载体积 */
    max_volume: number;
    /** 车辆类型 */
    name: string;
    /** 装载系数 */
    ratio: number;
    /** 车辆类型状态（启用：true,禁用：false） */
    status: boolean;
    /** 装载体积 */
    volume: number;
    /** 装载重量 */
    weight: number;
    /** 车身宽度 */
    width: number;
  };

  type CarUpdateReqDTO = {
    /** 车辆品牌 */
    car_brand?: string;
    /** 车辆类型id */
    car_type_id: number;
    /** 主驾驶员id */
    driver_id: number;
    /** 能源类型 */
    energy_type?: string;
    /** id */
    id: number;
    /** 备注 */
    memo?: string;
    /** 车牌号 */
    plate_number: string;
    /** 车辆状态（启用：true,禁用：false） */
    status: boolean;
  };

  type CompanyPageReqDTO = {
    /** 名称 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
  };

  type CompanyResDTO = {
    /** 企业appid */
    corp_id?: string;
    /** 企业secret */
    corp_secret?: string;
    /** 是否启用 */
    enabled?: boolean;
    /** 公司id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 是否同步企业微信组织架构 */
    sync?: number;
    /** 企业token */
    token?: string;
    /** 微信appid */
    wechat_id?: string;
    /** 微信secret */
    wechat_secret?: string;
  };

  type Coordinate = {
    latitude?: string;
    longitude?: string;
  };

  type DeleteResultResDTO = {
    /** 错误信息 */
    error_messages?: string[];
    /** 失败数量 */
    fail_num?: number;
    /** 状态：1-成功 0-失败 */
    state?: boolean;
    /** 成功数量 */
    success_num?: number;
  };

  type DispatchOrder = {
    channel_name?: string;
    client_id?: number;
    client_name?: string;
    client_type?: 'CLIENT' | 'STORE' | 'SUPPLIER';
    company_id?: number;
    create_by?: string;
    create_time?: string;
    deleted?: boolean;
    delivery_date?: string;
    dispatch_order_details?: DispatchOrderDetail[];
    fid?: string;
    item_count?: number;
    memo?: string;
    oms_order_id?: string;
    open_stock_quantity?: number;
    order_pre?: string;
    order_type?: 'ALLOCATE' | 'APPLY' | 'RESTOCK';
    pallet_name?: string;
    pick_quantity?: number;
    pick_state?: 'DOING' | 'FINISH' | 'INIT';
    quantity?: number;
    split_order_type?: 'INIT_ORDER' | 'MERGE_ORDER' | 'SPLIT_ORDER';
    state?: 'DISPATCHED' | 'ORDERED' | 'PENDING_ORDER' | 'UN_DISPATCH';
    store_id?: number;
    store_name?: string;
    storehouse_id?: number;
    storehouse_name?: string;
    take_state?: 'DOING' | 'FINISH' | 'INIT';
    update_by?: string;
    update_time?: string;
    volume?: number;
    weight?: number;
    whole_quantity?: number;
  };

  type DispatchOrderDetail = {
    basic_unit?: string;
    company_id?: number;
    detail_num?: number;
    fid?: string;
    item_bar_code?: string;
    item_category_id?: number;
    item_category_name?: string;
    item_code?: string;
    item_id?: number;
    item_name?: string;
    item_spec?: string;
    open_stock_quantity?: number;
    quantity?: number;
    ratio?: number;
    store_id?: number;
    store_name?: string;
    storehouse_id?: number;
    storehouse_name?: string;
    unit?: string;
    volume?: number;
    weight?: number;
    whole_quantity?: number;
  };

  type DriverBackDTO = {
    license_number?: string;
    name?: string;
    record?: string;
    record_number?: string;
  };

  type DriverFrontDTO = {
    /** 地址 */
    address?: string;
    /** 准驾类型 */
    approved_type?: string;
    /** 出生日期 */
    birth_date?: string;
    /** 初次领证日期 */
    initial_issue_date?: string;
    /** 发证单位 */
    issue_authority?: string;
    /** 证号 */
    license_number?: string;
    /** 姓名 */
    name?: string;
    /** 国籍 */
    nationality?: string;
    /** 性别 */
    sex?: string;
    /** 有效起始日期 */
    valid_from_date?: string;
    /** 有效期限 */
    valid_period?: string;
  };

  type DriverLicenseDTO = {
    /** 驾驶证反面 */
    driver_back?: DriverBackDTO;
    /** 驾驶证正面 */
    driver_front?: DriverFrontDTO;
  };

  type DriverPageReqDTO = {
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type?: 'CARRIER' | 'DRIVER';
    /** 准驾车型 */
    approved_driver_model?: string;
    /** 供应商id */
    carrier_id?: number;
    /** 名称 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    status?: boolean;
  };

  type DriverReadReqDTO = {
    /** 主键id */
    id: number;
  };

  type DriverResDTO = {
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type?: 'CARRIER' | 'DRIVER';
    /** 准驾车型 */
    approved_driver_model?: string;
    /** 银行卡号 */
    bank_card_number?: string;
    /** 开户行 */
    bank_name?: string;
    /** 银行卡持有人 */
    bank_owner?: string;
    /** 供应商id */
    carrier_id?: number;
    /** 供应商名称 */
    carrier_name?: string;
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 驾驶证反面url */
    driver_card_back_url?: string;
    /** 驾驶证正面url */
    driver_card_front_url?: string;
    /** 驾驶证司机姓名 */
    driver_card_name?: string;
    /** 驾驶证号 */
    driver_card_number?: string;
    /** 驾驶者有效期限 */
    driver_card_validity_date?: string;
    /** id */
    id?: number;
    /** 身份证反面url */
    id_card_back_url?: string;
    /** 身份证正面url */
    id_card_front_url?: string;
    /** 身份证姓名 */
    id_card_name?: string;
    /** 身份证号 */
    id_card_number?: string;
    /** 身份证有效期限 */
    id_card_validity_date?: string;
    /** 备注 */
    memo?: string;
    /** 司机姓名 */
    name?: string;
    /** 结算方式：REACH(货到付款),MONTH(月结) */
    payment_type?: 'MONTH' | 'REACH';
    /** 司机性别 */
    sex?: string;
    /** 状态 false-关闭 true-启用 */
    status?: boolean;
    /** 联系电话 */
    tel?: string;
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type DriverSaveReqDTO = {
    /** 结算主体：DRIVER(司机),CARRIER(承运商) */
    account_type: 'CARRIER' | 'DRIVER';
    /** 准驾车型 */
    approved_driver_model?: string;
    /** 银行卡号 */
    bank_card_number?: string;
    /** 开户行 */
    bank_name?: string;
    /** 银行卡持有人 */
    bank_owner?: string;
    /** 承运商id */
    carrier_id: number;
    /** 承运商名称 */
    carrier_name?: string;
    /** 驾驶证反面url */
    driver_card_back_url?: string;
    /** 驾驶证正面url */
    driver_card_front_url?: string;
    /** 驾驶证司机姓名 */
    driver_card_name?: string;
    /** 驾驶证号 */
    driver_card_number?: string;
    /** 驾驶者有效期限 */
    driver_card_validity_date?: string;
    /** 主键id */
    id: number;
    /** 身份证反面url */
    id_card_back_url?: string;
    /** 身份证正面url */
    id_card_front_url?: string;
    /** 身份证姓名 */
    id_card_name?: string;
    /** 身份证号 */
    id_card_number?: string;
    /** 身份证有效期限 */
    id_card_validity_date?: string;
    /** 备注 */
    memo?: string;
    /** 司机姓名 */
    name: string;
    /** 结算方式：REACH(货到付款),MONTH(月结) */
    payment_type?: 'MONTH' | 'REACH';
    /** 司机性别 */
    sex: string;
    /** 状态 false-关闭 true-启用 */
    status?: boolean;
    /** 联系电话 */
    tel: string;
  };

  type ElectronFencePageReqDTO = {
    /** 围栏名称 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
  };

  type ElectronFenceResDTO = {
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** id */
    id?: number;
    /** 围栏名称 */
    name?: string;
    /** 围栏半径 */
    scope?: string;
    /** 范围坐标 */
    scope_coordinate?: Coordinate[];
    /** 范围类型：VARIABLE(手动绘制),FIXED(固定半径) */
    scope_type?: 'FIXED' | 'VARIABLE';
    /** 来源区域坐标 */
    source_coordinate?: string;
    /** 来源Id */
    source_id?: number;
    /** 围栏分类：STORE(门店),STOREHOUSE(仓库) */
    type?: 'STORE' | 'STOREHOUSE';
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type ElectronFenceSaveReqDTO = {
    /** 围栏名称 */
    name: string;
    /** 围栏半径 */
    scope?: string;
    /** 范围坐标 */
    scope_coordinate?: Coordinate[];
    /** 范围类型：VARIABLE(手动绘制),FIXED(固定半径) */
    scope_type: 'FIXED' | 'VARIABLE';
    /** 来源区域坐标 */
    source_coordinate?: string;
    /** 来源Id */
    source_id?: number;
    /** 围栏分类：STORE(门店),STOREHOUSE(仓库) */
    type: 'STORE' | 'STOREHOUSE';
  };

  type ElectronFenceUpdateReqDTO = {
    /** id */
    id: number;
    /** 围栏名称 */
    name: string;
    /** 围栏半径 */
    scope?: string;
    /** 范围坐标 */
    scope_coordinate?: Coordinate[];
    /** 范围类型：VARIABLE(手动绘制),FIXED(固定半径) */
    scope_type: 'FIXED' | 'VARIABLE';
    /** 来源区域坐标 */
    source_coordinate?: string;
    /** 来源Id */
    source_id?: number;
    /** 围栏分类：STORE(门店),STOREHOUSE(仓库) */
    type: 'STORE' | 'STOREHOUSE';
  };

  type FileResDTO = {
    /** 序号 */
    detail_num?: number;
    /** 文件id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 关联id */
    ref_id?: string;
    /** 子文件类型 */
    ref_sub_type?: string;
    /** 后缀类型 */
    suffix_type?: string;
    /** 文件链接 */
    url?: string;
  };

  type fileUploadOcrUsingPOSTParams = {
    /** id */
    id: number;
    /** 正反（身份证识别的时候需要） */
    side?: boolean;
    /** BUSINESS_LICENSE:营业执照,FOOD_PRODUCTION_LICENSE:食品生产许可证,FOOD_BUSINESS_LICENSE:食品经营许可证,HANDWRITING:手写识别,ID_CARD:身份证,BANK_CARD:银行卡,DRIVER_LICENSE:驾驶证,VEHICLE_LICENSE:行驶证 */
    type: dto;
  };

  type fileUploadUsingPOSTParams = {
    /** id */
    id: number;
  };

  type fileUploadUsingPOSTParams = {
    /** id */
    id: number;
    /** 正反（身份证和驾驶证识别的时候需要） */
    side?: boolean;
    /** BUSINESS_LICENSE:营业执照,FOOD_PRODUCTION_LICENSE:食品生产许可证,FOOD_BUSINESS_LICENSE:食品经营许可证,HANDWRITING:手写识别,ID_CARD:身份证,BANK_CARD:银行卡,DRIVER_LICENSE:驾驶证,VEHICLE_LICENSE:行驶证 */
    type: dto;
  };

  type FoodBusinessLicenseDTO = {
    /** 经营场所 */
    business_address?: string;
    /** 经营范围 */
    business_scope?: string;
    /** 社会信用代码 */
    credit_code?: string;
    /** 发证机关 */
    issue_authority?: string;
    /** 签发日期 */
    issue_date?: string;
    /** 签发人 */
    issue_officer?: string;
    /** 法定代表人 */
    legal_representative?: string;
    /** 许可证编号 */
    licence_number?: string;
    /** 主体业态 */
    main_business?: string;
    /** 住所 */
    office_address?: string;
    /** 经营者名称 */
    operator_name?: string;
    /** 日常监督管理机构 */
    regulatory_authority?: string;
    /** 日常监督管理人员 */
    regulatory_personnel?: string;
    /** 举报电话 */
    report_hotline?: string;
    /** 格式化签发日期 */
    standardized_issue_date?: string;
    /** 格式化有效期至 */
    standardized_valid_to_date?: string;
    /** 有效期至 */
    valid_to_date?: string;
  };

  type FoodProductionLicenseDTO = {
    /** 社会信用代码 */
    credit_code?: string;
    /** 食品类别 */
    food_type?: string;
    /** 发证机关 */
    issue_authority?: string;
    /** 签发日期 */
    issue_date?: string;
    /** 签发人 */
    issue_officer?: string;
    /** 法定代表人 */
    legal_representative?: string;
    /** 许可证编号 */
    licence_number?: string;
    /** 住所 */
    office_address?: string;
    /** 生产者名称 */
    producer_name?: string;
    /** 生产地址 */
    production_address?: string;
    /** 日常监督管理机构 */
    regulatory_authority?: string;
    /** 日常监督管理人员 */
    regulatory_personnel?: string;
    /** 举报电话 */
    report_hotline?: string;
    /** 有效期至 */
    valid_to_date?: string;
  };

  type IdCard = {
    /** 身份证号 */
    code?: string;
    /** 失效时间 */
    expire_time?: string;
    /** 姓名 */
    name?: string;
    /** 背面url */
    reverse_side_url?: string;
    /** 正面url */
    side_url?: string;
  };

  type IdCardBackDTO = {
    /** 签发机关 */
    issue_authority?: string;
    /** 有效期限 */
    valid_period?: string;
  };

  type IdCardDTO = {
    back?: IdCardBackDTO;
    front?: IdCardFrontDTO;
  };

  type IdCardFrontDTO = {
    /** 住址 */
    address?: string;
    /** 出生日期 */
    birth_date?: string;
    /** 民族 */
    ethnicity?: string;
    /** 身份证号 */
    id_number?: string;
    /** 姓名 */
    name?: string;
    /** 性别 */
    sex?: string;
  };

  type IdReqDTO = {
    /** id */
    id: number;
  };

  type IdsReqDTO = {
    /** id */
    ids: number[];
  };

  type mendianxinxibiao = {
    /** 是否配送中心 */
    enable_delivery_center?: boolean;
    /** 门店id */
    id?: number;
    /** 门店代码 */
    store_code?: string;
    /** 门店分组ID */
    store_group_id?: number;
    /** 门店名称 */
    store_name?: string;
    /** 门店编码 */
    store_number?: number;
    /** 上游配送中心id */
    upstream_center_id?: number;
    /** 老系统id */
    wms_id?: number;
  };

  type OcrResultDTO = {
    /** 银行卡 */
    bank_card?: BankCardDTO;
    /** 营业执照 */
    business_license?: BusinessLicenseDTO;
    /** 驾驶证 */
    driver_license?: DriverLicenseDTO;
    /** 食品经营许可证 */
    food_business_license?: FoodBusinessLicenseDTO;
    /** 食品生产许可证 */
    food_production_license?: FoodProductionLicenseDTO;
    /** 身份证 */
    id_card?: IdCardDTO;
    /** 文件链接 */
    url?: string;
  };

  type OrderDetailResDTO = {
    /** 基本单位 */
    basic_unit?: string;
    /** 公司id */
    company_id?: number;
    /** 序号 */
    detail_num?: number;
    /** 订单号 */
    fid?: string;
    /** 商品条形码 */
    item_bar_code?: string;
    /** 商品类别id */
    item_category_id?: number;
    /** 商品类别名称 */
    item_category_name?: string;
    /** 商品代码 */
    item_code?: string;
    /** 商品ID */
    item_id?: number;
    /** 商品名称 */
    item_name?: string;
    /** 商品规模 */
    item_spec?: string;
    /** 拆零数量 */
    open_stock_quantity?: number;
    /** 订单件数 */
    quantity?: number;
    /** 换算率 */
    ratio?: number;
    /** 门店id */
    store_id?: number;
    /** 门店名称 */
    store_name?: string;
    /** 仓库id */
    storehouse_id?: number;
    /** 仓库名称 */
    storehouse_name?: string;
    /** 单位 */
    unit?: string;
    /** 订单体积(m³) */
    volume?: number;
    /** 订单重量(kg) */
    weight?: number;
    /** 整件数量 */
    whole_quantity?: number;
  };

  type OrderHeaderCountBasePageResDTOOrderHeaderResDTO = {
    /** 明细 */
    content?: OrderHeaderResDTO[];
    /** 总记录数 */
    total_elements?: number;
    /** 总sku数 */
    total_item_count?: number;
    /** 总订单数 */
    total_order_count?: number;
    /** 总页数 */
    total_pages?: number;
    /** 总件数 */
    total_quantity?: number;
    /** 总体积 */
    total_volume?: number;
  };

  type OrderHeaderPageReqDTO = {
    /** 门店区域，例：江苏省、江苏省南京市、江苏省南京市建邺区 */
    client_area?: string;
    /** 收货门店Id */
    client_id?: number;
    /** 收货门店Ids */
    client_ids?: number[];
    /** 配送日期 yyyy-MM-dd格式 */
    delivery_date?: string[];
    /** 是否排序 1排序 0不排 */
    order_sort?: boolean;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 门店线路Id */
    route_id?: number;
    /** 排单状态 */
    state?: 'DISPATCHED' | 'ORDERED' | 'PENDING_ORDER' | 'UN_DISPATCH';
    /** 门店地址 */
    store_address?: string;
    /** 发货仓库Id */
    storehouse_id?: number;
  };

  type OrderHeaderResDTO = {
    /** 通道名称 */
    channel_name?: string;
    /** 收货区域 */
    client_area?: string;
    /** 收货客户id */
    client_id?: number;
    /** 收货客户名称 */
    client_name?: string;
    /** 收货客户类型 门店:STORE,供应商:SUPPLIER,批发客户:CLIENT */
    client_type?: 'CLIENT' | 'STORE' | 'SUPPLIER';
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 配送日期 */
    delivery_date?: string;
    /** 订单号 */
    fid?: string;
    /** 订单SKU数量 */
    item_count?: number;
    /** 备注 */
    memo?: string;
    /** 拆零数量 */
    open_stock_quantity?: number;
    /** 订单数 */
    order_count?: number;
    /** 订单体详情 */
    order_details?: OrderDetailResDTO[];
    /** 订单类型 门店补货单:RESTOCK,门店申请单：APPLY,仓库调拨单：ALLOCATE */
    order_type?: 'ALLOCATE' | 'APPLY' | 'RESTOCK';
    /** 托盘名称 */
    pallet_name?: string;
    /** 拣货数量 */
    pick_quantity?: number;
    /** 拣货状态 待拣货:INIT,拣货中:DOING,拣货完成:FINISH */
    pick_state?: 'DOING' | 'FINISH' | 'INIT';
    /** 订单件数 */
    quantity?: number;
    /** 线路id */
    route_id?: number;
    /** 线路名称 */
    route_name?: string;
    /** 状态 待排单:PENDING_ORDER,已排单:ORDERED */
    state?: 'DISPATCHED' | 'ORDERED' | 'PENDING_ORDER' | 'UN_DISPATCH';
    /** 门店地址 */
    store_address?: string;
    /** 门店id */
    store_id?: number;
    /** 门店名称 */
    store_name?: string;
    /** 仓库地址 */
    storehouse_address?: string;
    /** 仓库id */
    storehouse_id?: number;
    /** 仓库名称 */
    storehouse_name?: string;
    /** 取件状态 待取件:INIT,取件中:DOING,取件完成:FINISH */
    take_state?: 'DOING' | 'FINISH' | 'INIT';
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
    /** 订单体积(m³) */
    volume?: number;
    /** 订单重量(kg) */
    weight?: number;
    /** 整件数量 */
    whole_quantity?: number;
  };

  type OrderHeaderSumResDTO = {
    /** 商品汇总 */
    order_details?: OrderDetailResDTO[];
    /** 订单汇总 */
    order_headers?: OrderHeaderResDTO[];
    /** 商品总数量 */
    total_detail_quantity?: number;
    /** 商品总体积 */
    total_detail_volume?: number;
    /** 总sku数 */
    total_item_count?: number;
    /** 总件数 */
    total_quantity?: number;
    /** 总体积 */
    total_volume?: number;
  };

  type OrderParam = {
    /** 方向 */
    direction?: 'ASC' | 'DESC';
    /** 属性 */
    property?: string;
  };

  type QualificationDocumentsReqDTO = {
    /** 营业执照 */
    business_license?: BusinessLicense;
    /** 商户号 */
    company_id?: number;
    /** 合同url */
    contract_url?: string;
    /** 营业执照信用代码 */
    credit_code?: string;
    /** id */
    id?: number;
    /** 法人身份证 */
    id_card?: IdCard;
    /** 经营许可证 */
    operate_license?: BusinessLicense;
  };

  type QualificationDocumentsResDTO = {
    /** 营业执照 */
    business_license?: BusinessLicense;
    /** 商户号 */
    company_id?: number;
    /** 合同url */
    contract_url?: string;
    /** 营业执照信用代码 */
    credit_code?: string;
    /** id */
    id?: number;
    /** 法人身份证 */
    id_card?: IdCard;
    /** 经营许可证 */
    operate_license?: BusinessLicense;
  };

  type RoleCategoryResDTO = {
    /** 公司id */
    company_id?: number;
    /** id */
    id?: number;
    /** 级别 */
    level?: number;
    /** 名称 */
    name?: string;
    /** 父id */
    parent_id?: number;
  };

  type RoleCategorySaveDTO = {
    /** 角色分类名称 */
    name?: string;
  };

  type RoleCategoryUpdateDTO = {
    /** id */
    id?: number;
    /** 角色分类名称 */
    name?: string;
  };

  type RolePageReqDTO = {
    /** 角色分类id */
    category_id?: number;
    /** 角色名称 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 状态 */
    status?: boolean;
  };

  type RoleResDTO = {
    /** 权限 仅read接口返回 */
    authorities?: AuthorityResDTO[];
    /** 角色分类id */
    category_id?: number;
    /** 角色分类名称 */
    category_name?: string;
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** id */
    id?: number;
    /** 角色名称 */
    name?: string;
    /** 状态 */
    status?: boolean;
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type RoleSaveReqDTO = {
    /** 权限列表 */
    authority_ids?: number[];
    /** 角色分类id */
    category_id?: number;
    /** 角色名称 */
    name: string;
    /** 状态（启用：true,禁用：false） */
    status: boolean;
  };

  type RoleUpdateReqDTO = {
    /** 权限列表 */
    authority_ids?: number[];
    /** 角色分类id */
    category_id?: number;
    /** id */
    id: number;
    /** 角色名称 */
    name: string;
    /** 状态（启用：true,禁用：false） */
    status: boolean;
  };

  type RouteAreaResDTO = {
    /** 区code */
    area_code?: number;
    /** 市code */
    city_code?: number;
    /** 区域id */
    id?: number;
    /** 区域名称 */
    name?: string;
    /** 省code */
    province_code?: number;
    /** 线路id */
    route_id?: number;
    /** 优先级 */
    sort?: number;
  };

  type RouteAreaSaveReqDTO = {
    /** 区code */
    area_code: number;
    /** 市code */
    city_code: number;
    /** 区域名称 */
    name: string;
    /** 省code */
    province_code: number;
    /** 优先级 */
    sort: number;
  };

  type RoutePageReqDTO = {
    /** 区code */
    area_codes?: number[];
    /** 市code */
    city_codes?: number[];
    /** 线路名称 */
    name?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 省code */
    province_codes?: number[];
    /** 配送仓库 */
    storehouse_ids?: number[];
  };

  type RouteResDTO = {
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** id */
    id?: number;
    /** 线路名称 */
    name?: string;
    /** 区域信息 */
    route_areas?: RouteAreaResDTO[];
    /** 排序 */
    sort?: number;
    /** 状态 */
    status?: boolean;
    /** 仓库id */
    storehouse_id?: number;
    /** 仓库名称 */
    storehouse_name?: string;
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type RouteSaveReqDTO = {
    /** 线路名称 */
    name: string;
    /** 区域 */
    route_areas?: RouteAreaSaveReqDTO[];
    /** 仓库id */
    storehouse_id: number;
    /** 仓库名称 */
    storehouse_name: string;
  };

  type RouteSortReqDTO = {
    /** sourceId（需要移动的id） */
    source_id: number;
    /** targetId（需要移动到的目标id） */
    target_id: number;
  };

  type RouteUpdateReqDTO = {
    /** id */
    id: number;
    /** 线路名称 */
    name: string;
    /** 区域 */
    route_areas?: RouteAreaSaveReqDTO[];
    /** 仓库id */
    storehouse_id: number;
    /** 仓库名称 */
    storehouse_name: string;
  };

  type StorehouseResDTO = {
    /** 仓库代码 */
    code?: string;
    /** 仓库坐标 */
    coordinate?: string;
    /** 是否为默认仓 */
    default_flag?: boolean;
    /** 是否启用库位 */
    enable_location?: boolean;
    /** 主键id */
    id?: number;
    /** 仓库名称 */
    name?: string;
    /** 是否是退货仓 */
    return_flag?: boolean;
    /** 状态 false-未启用 true-启用 */
    status?: boolean;
  };

  type TransportOrderPageReqDTO = {
    /** 排单状态 */
    arrange_state?: 'DISPATCHED' | 'UN_DISPATCH';
    /** 车辆类型Id */
    car_type_id?: number;
    /** 承运商id */
    carrier_id?: number;
    /** 收货门店名称 */
    client_name?: string;
    /** 配送日期 yyyy-MM-dd格式 */
    delivery_date?: string[];
    /** 司机id */
    driver_id?: number;
    /** 运单号 */
    fid?: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 车牌号 */
    plate_number?: string;
    /** 发货仓库Id */
    storehouse_id?: number;
  };

  type TransportOrderResDTO = {
    /** 承运商id */
    carrier_id?: number;
    /** 承运商名称 */
    carrier_name?: string;
    /** 点位数 */
    client_count?: number;
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 配送进度 */
    delivery_schedule?: number;
    /** 发车时间 */
    departed_time?: string;
    /** 调度单 */
    dispatch_orders?: DispatchOrder[];
    /** 调度人 */
    dispatcher?: string;
    /** 司机id */
    driver_id?: number;
    /** 司机姓名 */
    driver_name?: string;
    /** 司机姓名 */
    driver_phone?: string;
    fid?: string;
    /** 装载率 */
    load_rate?: number;
    /** 备注 */
    memo?: string;
    /** 拆零数量 */
    open_stock_quantity?: number;
    /** 订单数 */
    order_count?: number;
    /** 拣货状态 待拣货:INIT,拣货中:DOING,拣货完成:FINISH */
    pick_state?: 'DOING' | 'FINISH' | 'INIT';
    /** 取件时间 */
    pick_time?: string;
    /** 车牌号 */
    plate_number?: string;
    /** 装载件数 */
    quantity?: number;
    /** 签收完成时间 */
    receipt_time?: string;
    /** 状态：PENDING(待取件),PICKING(取件中),TRANSITING(运输中),RECEIPTING(部分签收),RECEIPTED(签收完成) */
    state?: 'PENDING' | 'PICKING' | 'RECEIPTED' | 'RECEIPTING' | 'TRANSITING';
    /** 总公里数 */
    total_kilometers?: number;
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
    /** 装载体积 */
    volume?: number;
    /** 装载重量 */
    weight?: number;
    /** 整件数量 */
    whole_quantity?: number;
  };

  type TransportOrderSaveReqDTO = {
    /** 车辆类型Id */
    car_type_id?: number;
    /** 车辆类型名称 */
    car_type_name?: string;
    /** 承运商id */
    carrier_id: number;
    /** 承运商名称 */
    carrier_name?: string;
    /** 收货门店Ids */
    client_ids?: number[];
    /** 司机id */
    driver_id: number;
    /** 司机名称 */
    driver_name?: string;
    /** 车牌号 */
    plate_number: string;
    /** 配送日期 */
    transport_time?: string;
    /** 装载体积(m³) */
    volume?: number;
  };

  type UserColumnDetail = {
    /** 在数据中的字段 code */
    code: string;
    /** 是否固定 */
    fix?: boolean;
    /** 是否隐藏 */
    hidden?: boolean;
    /** 列的序号 */
    index: number;
    /** 列的名称 */
    name: string;
    /** 列的宽度 */
    width: number;
  };

  type UserColumnFindReqDTO = {
    /** url地址 */
    name: string;
  };

  type UserColumnResDTO = {
    details?: ValueResDTO[];
  };

  type UserColumnUpdateReqDTO = {
    /** 列定制值 */
    details: UserColumnDetail[];
    /** url地址 */
    name: string;
  };

  type UserPageReqDTO = {
    /** 用户名 */
    name: string;
    /** 排序 */
    orders?: OrderParam[];
    /** 页号 */
    page_number?: number;
    /** 每页条数 */
    page_size?: number;
    /** 用户手机 */
    phone: string;
    /** 角色Ids */
    role_ids?: number[];
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 所属仓库ids */
    storehouse_ids?: number[];
    /** 用户类型：FOREVER(长期用户),TEMP(临时用户) */
    type: 'FOREVER' | 'TEMP';
  };

  type UserResDTO = {
    /** 登录账号 */
    account: string;
    /** 公司id */
    company_id?: number;
    /** 创建人 */
    create_by?: string;
    /** 创建时间 */
    create_time?: string;
    /** 有效期 */
    expire_time: string;
    /** id */
    id: number;
    /** 用户名 */
    name: string;
    /** 密码 */
    password: string;
    /** 用户手机 */
    phone: string;
    /** 角色ids */
    role_ids: number[];
    /** 角色名称 */
    role_name: string;
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 仓库ids */
    storehouse_ids: number[];
    /** 仓库名称 */
    storehouse_name: string;
    /** 用户类型：FOREVER(长期用户),TEMP(临时用户) */
    type: 'FOREVER' | 'TEMP';
    /** 更新人 */
    update_by?: string;
    /** 更新时间 */
    update_time?: string;
  };

  type UserSaveReqDTO = {
    account?: string;
    /** 有效期 */
    expire_time: string;
    /** 用户名 */
    name: string;
    /** 密码 */
    password: string;
    /** 用户手机 */
    phone: string;
    /** 角色Ids */
    role_ids?: number[];
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 所属仓库ids */
    storehouse_ids?: number[];
    /** 用户类型：FOREVER(长期用户),TEMP(临时用户) */
    type: 'FOREVER' | 'TEMP';
  };

  type UserUpdateReqDTO = {
    account?: string;
    /** 有效期 */
    expire_time: string;
    /** id */
    id: number;
    /** 用户名 */
    name: string;
    /** 密码 */
    password: string;
    /** 用户手机 */
    phone: string;
    /** 角色Ids */
    role_ids?: number[];
    /** 状态：ENABLE(正常),DISABLE(停用) */
    state: 'DISABLE' | 'ENABLE';
    /** 所属仓库ids */
    storehouse_ids?: number[];
    /** 用户类型：FOREVER(长期用户),TEMP(临时用户) */
    type: 'FOREVER' | 'TEMP';
  };

  type ValueResDTO = {
    code?: string;
    fix?: boolean;
    hidden?: boolean;
    index?: number;
    name?: string;
    width?: number;
  };
}
