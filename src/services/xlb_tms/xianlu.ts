// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.route.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.route.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.route.page */
export async function pageUsingPost(body: API.RoutePageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTORouteResDTO>('/tms/hxl.tms.route.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /tms/hxl.tms.route.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseRouteResDTO>('/tms/hxl.tms.route.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.route.save */
export async function saveUsingPost(body: API.RouteSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.route.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.route.sort */
export async function sortUsingPost(body: API.RouteSortReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.route.sort', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.route.update */
export async function updateUsingPost(
  body: API.RouteUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.route.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
