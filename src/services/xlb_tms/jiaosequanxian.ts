// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.role.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.role.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.role.page */
export async function pageUsingPost(body: API.RolePageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTORoleResDTO>('/tms/hxl.tms.role.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /tms/hxl.tms.role.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseRoleResDTO>('/tms/hxl.tms.role.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.role.save */
export async function saveUsingPost(body: API.RoleSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.role.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.role.update */
export async function updateUsingPost(
  body: API.RoleUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.role.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
