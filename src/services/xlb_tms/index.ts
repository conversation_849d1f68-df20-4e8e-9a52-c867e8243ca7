// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as cangku from './cangku';
import * as cheliang from './cheliang';
import * as cheliangleixing from './cheliangleixing';
import * as chengyunshang from './chengyunshang';
import * as dian<PERSON><PERSON><PERSON> from './dianziweilan';
import * as diaodudan from './diaodudan';
import * as gongsi from './gongsi';
import * as jiaosefenlei from './jiaosefenlei';
import * as jiaosequanxian from './jiaosequanxian';
import * as jiaoseshouquan from './jiaoseshouquan';
import * as mendian from './mendian';
import * as siji from './siji';
import * as xianlu from './xianlu';
import * as yonghuguanli from './yonghuguanli';
import * as yonghuliedingzhi from './yonghuliedingzhi';
import * as yundan from './yundan';
export default {
  jiaoseshouquan,
  cheliang,
  cheliangleixing,
  chengyunshang,
  gongsi,
  siji,
  dian<PERSON><PERSON><PERSON>,
  diao<PERSON><PERSON>,
  jiao<PERSON><PERSON><PERSON><PERSON>,
  jiaosequanxian,
  xianlu,
  mendian,
  cang<PERSON>,
  yundan,
  yonghuliedingzhi,
  yonghuguanli,
};
