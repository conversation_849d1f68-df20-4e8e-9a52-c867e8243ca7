// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 查询 POST /tms/hxl.tms.authority.find */
export async function findUsingPost(options?: { [key: string]: any }) {
  return request<API.BaseResponseListAuthorityCategoryResDTO>('/tms/hxl.tms.authority.find', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 权限智能查询 POST /tms/hxl.tms.authority.search */
export async function searchUsingPost(
  body: API.AuthoritySearchReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseListAuthoritySearchResDTO>('/tms/hxl.tms.authority.search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
