// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 分页查询 POST /tms/hxl.tms.company.page */
export async function pageUsingPost(body: API.CompanyPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTOCompanyResDTO>('/tms/hxl.tms.company.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
