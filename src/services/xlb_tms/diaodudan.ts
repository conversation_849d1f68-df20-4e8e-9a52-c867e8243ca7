// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 分页查询 POST /tms/hxl.tms.order.page */
export async function pageUsingPost(
  body: API.OrderHeaderPageReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseOrderHeaderCountBasePageResDTOOrderHeaderResDTO>(
    '/tms/hxl.tms.order.page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 门店详情 POST /tms/hxl.tms.order.read */
export async function readUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseOrderHeaderSumResDTO>('/tms/hxl.tms.order.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
