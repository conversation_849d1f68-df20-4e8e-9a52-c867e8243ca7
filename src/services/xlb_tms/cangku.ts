// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 查询 POST /tms/hxl.tms.storehouse.find */
export async function findUsingPost(body: API.BaseReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseListStorehouseResDTO>('/tms/hxl.tms.storehouse.find', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
