// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.driver.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.driver.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出 POST /tms/hxl.tms.driver.export */
export async function exportUsingPost(
  body: API.DriverPageReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponsestring>('/tms/hxl.tms.driver.export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 附件上传 POST /tms/hxl.tms.driver.file.upload */
export async function fileUploadUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fileUploadUsingPOSTParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseOcrResultDTO>('/tms/hxl.tms.driver.file.upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.driver.page */
export async function pageUsingPost(body: API.DriverPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTODriverResDTO>('/tms/hxl.tms.driver.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 读取 POST /tms/hxl.tms.driver.read */
export async function readUsingPost(body: API.DriverReadReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDriverResDTO>('/tms/hxl.tms.driver.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.driver.save */
export async function saveUsingPost(body: API.DriverSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.driver.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.driver.update */
export async function updateUsingPost(
  body: API.DriverSaveReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.driver.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
