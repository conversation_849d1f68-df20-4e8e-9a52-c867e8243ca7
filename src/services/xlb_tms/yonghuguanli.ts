// @ts-ignore
/* eslint-disable */
import { XlbFetch as request } from '@xlb/utils';

/** 删除 POST /tms/hxl.tms.user.delete */
export async function deleteUsingPost(body: API.IdsReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseDeleteResultResDTO>('/tms/hxl.tms.user.delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出 POST /tms/hxl.tms.user.export */
export async function exportUsingPost(body: API.UserPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponsestring>('/tms/hxl.tms.user.export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 POST /tms/hxl.tms.user.page */
export async function pageUsingPost(body: API.UserPageReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBasePageResDTOUserResDTO>('/tms/hxl.tms.user.page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /tms/hxl.tms.user.read */
export async function readUsingPost(body: API.IdReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseUserResDTO>('/tms/hxl.tms.user.read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增 POST /tms/hxl.tms.user.save */
export async function saveUsingPost(body: API.UserSaveReqDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.user.save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新 POST /tms/hxl.tms.user.update */
export async function updateUsingPost(
  body: API.UserUpdateReqDTO,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseobject>('/tms/hxl.tms.user.update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
