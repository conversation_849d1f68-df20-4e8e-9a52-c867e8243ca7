import { accountLoginUsingPost } from '@/services/system';
import { LStorage } from '@/utils/storage';
import { isWUJIE } from '@/wujie/utils';
import type { RequestConfig } from '@umijs/max';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import Cookies from 'js-cookie';
import React from 'react';
import { autoFixContext } from 'react-activation';
import jsxDevRuntime from 'react/jsx-dev-runtime';
import jsxRuntime from 'react/jsx-runtime';
import { useBaseParams } from './hooks/useBaseParams';
import Provider from './provider';
// 本地开发模式
const isLocalMode = () => {
  return process.env.XLB_ENV === 'development';
};

const tokenKey = `${process.env.XLB_TOKEN_PRE}_qiankun_token`;

autoFixContext(
  [jsxRuntime, 'jsx', 'jsxs', 'jsxDEV'],
  [jsxDevRuntime, 'jsx', 'jsxs', 'jsxDEV'],
);
if (process.env.XLB_TOKEN_PRE === 'production') {
  //  线上环境加密
  XlbFetch.interceptors.request.use(async (config) => {
    //@ts-ignore
    config.encryptConfig = {
      openSysList: ['scm'], // 开启加密
      filterUrlList: [''], // 不加密的url
    };
    return config;
  });
}
// @ts-ignore
XlbFetch.register([401], () => {
  if (isWUJIE()) {
    // @ts-ignore
    window.location.href = process.env.ERP_URL;
  }
  if (isLocalMode()) {
    LStorage.set('access_token', 'e1f1a70a8c664bc2b36f4a933ca76eff');
  }
});

// @ts-ignore
XlbFetch.register([2005], (data) => {
  return message.error(data.msg);
});

interface urlParamsProp {
  account?: string;
  companyId?: number | string;
}

export const request: RequestConfig = {
  timeout: 1000,
  errorConfig: {
    errorHandler() {},
    errorThrower() {},
  },
  requestInterceptors: [
    // 直接写一个 function，作为拦截器
    (url, options) => {
      return { url, options };
    },
    // 一个二元组，第一个元素是 request 拦截器，第二个元素是错误处理
    [
      (url, options) => {
        return { url, options };
      },
      (error) => {
        return Promise.reject(error);
      },
    ],
    // 数组，省略错误处理
    [
      (url, options) => {
        return { url, options };
      },
    ],
  ],
  responseInterceptors: [],
};

export async function getInitialState(): Promise<{ name: string }> {
  if (!isWUJIE()) {
    const { getEnableOrganization } = useBaseParams.getState();
    let userInfo = LStorage.get('userInfo');
    if (!userInfo) {
      let info: any = Cookies.get(tokenKey);
      if (info?.length) info = JSON.parse(info);
      if (info?.accessToken) {
        LStorage.set('access_token', info?.accessToken);
      }

      let res = await accountLoginUsingPost({
        account: info?.account || '***********',
        company_id: info?.companyId || 1000,
        source: 'WEB',
      });
      LStorage.set('userInfo', res?.data);
      userInfo = res?.data;
    }

    getEnableOrganization();
  }
  return { name: 'TMS物流管理' };
}

export const layout = () => {
  return {
    logo: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
    menu: { locale: false },
    pure: true,
  };
};

// 根节点注入NiceModal.Provider
export function rootContainer(container: any) {
  return React.createElement(Provider, null, container);
}
