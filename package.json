{"name": "xlb_scm", "private": true, "author": "曾涛 <<EMAIL>>", "scripts": {"build": "max build", "build:dev": "cross-env  UMI_ENV=dev max build", "dev": "max dev", "develop": "node node_modules/@xlb/components/bin/develop.mjs", "format": "prettier --cache --write .", "postinstall": "max setup", "openapi": "max openapi", "prepare": "husky install", "setup": "max setup", "start": "npm run dev", "xlbVersion": "npm view @xlb/components versions"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/pro-components": "^2.8.1", "@umijs/max": "^4.3.27", "@umijs/max-plugin-openapi": "^2.0.3", "@xlb/components": "2.0.183", "@xlb/max": "0.0.17", "@xlb/utils": "^1.8.4", "ahooks": "^3.8.1", "antd": "5.21.4", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "umi-plugin-keep-alive": "^0.0.1-beta.35", "zustand": "^4.3.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "cross-env": "^7.0.3", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.3", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}